<svg width="368" height="43" viewBox="0 0 368 43" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#0nr0lquvka)">
        <rect x="1.473" y="1.473" width="365.053" height="40.006" rx="10" fill="#fff" fill-opacity=".04"/>
        <rect x=".737" y=".737" width="366.527" height="41.479" rx="10.737" stroke="url(#9yhlftt2sb)" stroke-width="1.473"/>
    </g>
    <defs>
        <linearGradient id="9yhlftt2sb" x1="224.717" y1="117.078" x2="216.347" y2="11.646" gradientUnits="userSpaceOnUse">
            <stop stop-color="#fff"/>
            <stop offset="1" stop-color="#101015"/>
        </linearGradient>
        <filter id="0nr0lquvka" x="0" y="0" width="370.21" height="45.163" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dx="4" dy="5"/>
            <feGaussianBlur stdDeviation="1.105"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
            <feBlend in2="shape" result="effect1_innerShadow_1338_4132"/>
        </filter>
    </defs>
</svg>
