<svg width="515" height="542" viewBox="0 0 515 542" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_889_1649)">
<rect width="515" height="542" rx="14" fill="url(#paint0_linear_889_1649)" fill-opacity="0.12"/>
<rect x="0.25" y="0.25" width="514.5" height="541.5" rx="13.75" stroke="url(#paint1_linear_889_1649)" stroke-opacity="1" stroke-width="0.5"/>
<rect x="0.25" y="0.25" width="514.5" height="541.5" rx="13.75" stroke="url(#paint2_linear_889_1649)" stroke-opacity="1" stroke-width="0.5"/>
</g>
<defs>
<filter id="filter0_i_889_1649" x="0" y="0" width="515.5" height="544.501" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.500263" dy="2.50131"/>
<feGaussianBlur stdDeviation="5.45286"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.98 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_889_1649"/>
</filter>
<linearGradient id="paint0_linear_889_1649" x1="-581.905" y1="-946.58" x2="697.195" y2="288.415" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#52525E"/>
</linearGradient>
<linearGradient id="paint1_linear_889_1649" x1="515" y1="542" x2="308.726" y2="261.467" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FFAA43"/>
<stop offset="1" stop-color="#0F0F14"/>
</linearGradient>
<linearGradient id="paint2_linear_889_1649" x1="51.5" y1="-2.77719e-06" x2="226.728" y2="400.004" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC814"/>
<stop offset="1" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
