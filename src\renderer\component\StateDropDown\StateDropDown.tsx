import React, { useState, useRef, useEffect } from 'react';
import styles from '../../pages/buyer/CreatePo/CreatePo.module.scss';
import { ClickAwayListener } from '@mui/material';
import { ReactComponent as DropdownIcon } from '../../assets/images/StateIconDropDpown.svg';
import StatesList from '../StatesList/StatesList';
import clsx from 'clsx';


const StateDropDown = ({ states, setValue, stateDropDownValue = '', setStateDropDownValue, stateInputFocus, setStateInputFocus, onFocus, onBlur, setIsDataChanged,orderState, onChange, disabled = false, inputRef: externalInputRef , errorInput }: any) => {

  const [inputValue, setInputValue] = useState(stateDropDownValue || '');
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const internalInputRef = useRef<HTMLInputElement>(null);
  const inputRef = externalInputRef || internalInputRef;

  useEffect(() => {
    setInputValue(stateDropDownValue || '');
  }, [stateDropDownValue]);
  
  const handleStateSelection = (state: any) => {
    setInputValue(state.code);
    setValue('shipping_details.state_id', state.id, { shouldValidate: true });
    setValue('shipping_details.state_code', state.code, { shouldValidate: true });
    setStateDropDownValue(state.code);
    setStateInputFocus(false);
    inputRef.current?.blur();
    onBlur?.();
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Tab') {
      handleClickAway(states[selectedIndex]);
    }
    
    if (event.key === 'ArrowUp' || event.key === 'ArrowDown') {
      event.preventDefault();
    }
    
    const filteredStates = states.filter((state: any) => 
      state.code.toLowerCase().startsWith(inputValue.toLowerCase())
    );
    const filteredIndexes = filteredStates.map((state: any) => 
      states.findIndex((s: any) => s.id === state.id)
    );
    
    if (event.key === 'ArrowDown') {
      setSelectedIndex(prev => {
        const currentIdx = filteredIndexes.indexOf(prev);
        const nextIdx = currentIdx + 1;
        return filteredIndexes[nextIdx] !== undefined ? filteredIndexes[nextIdx] : prev;
      });
    } else if (event.key === 'ArrowUp') {
      setSelectedIndex(prev => {
        const currentIdx = filteredIndexes.indexOf(prev);
        const prevIdx = currentIdx - 1;
        return filteredIndexes[prevIdx] !== undefined ? filteredIndexes[prevIdx] : prev;
      });
    } else if (event.key === 'Enter') {
      handleClickAway(states[selectedIndex]);
    }
  };


  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIsDataChanged && setIsDataChanged(true);
    const value = e.target.value.toUpperCase();
    setInputValue(value);
    
    const matches = states?.filter((state: any) => 
      state.code.toLowerCase().startsWith(value.toLowerCase())
    );
    
    if (matches.length === 1 && value.length > 0) {
      setSelectedIndex(states.findIndex((s: any) => s.id === matches[0].id));
    } else {
      setSelectedIndex(-1);
    }
    onChange?.(e);
  };


  const handleClickAway = (state: any = null) => {
    // Only process click away if the state input is currently focused
    if (!stateInputFocus) {
      return;
    }

    // Check if state is an event (PointerEvent, MouseEvent, etc.) - treat as no explicit state
    const isStateAnEvent = state && typeof state === 'object' && ('type' in state || 'target' in state);
    const hasExplicitState = state && !isStateAnEvent;

    // Check if there's exactly one match when user types and clicks away (without explicit state selection)
    if (inputValue.trim() && !hasExplicitState) {
      const matches = states?.filter((s: any) =>
        s.code.toLowerCase().startsWith(inputValue.toLowerCase())
      );
      if (matches.length === 1) {
        // Auto-select the single matching state
        const matchedState = matches[0];
        // Set the values first
        setInputValue(matchedState.code);
        setValue('shipping_details.state_id', matchedState.id, { shouldValidate: true });
        setValue('shipping_details.state_code', matchedState.code, { shouldValidate: true });
        setStateDropDownValue(matchedState.code);
        setIsDataChanged && setIsDataChanged(true);
        setSelectedIndex(-1);

        // Add a small delay before closing to ensure state updates complete
        setTimeout(() => {
          setStateInputFocus(false);
          inputRef.current?.blur();
          onBlur?.();
        }, 50);
        return;
      }
    }

    // Original logic for when a state is explicitly provided or selectedIndex is set
    if(selectedIndex !== -1){
      setInputValue(state?.code || stateDropDownValue || '');
      setValue('shipping_details.state_id', state?.id || '', { shouldValidate: true });
      setValue('shipping_details.state_code', state?.code || '', { shouldValidate: true });
      setStateDropDownValue(state?.code || '');
      setSelectedIndex(-1);
    }else{
      setInputValue(state?.code || stateDropDownValue || '');
    }
    setStateInputFocus(false);
    inputRef.current?.blur();
    onBlur?.();
  };

  const handleInputFocus = () => {
    setStateInputFocus(true);
    setInputValue('');
    onFocus?.();
  }

  return (
    <ClickAwayListener onClickAway={handleClickAway}>
      <div className={clsx(styles.stateDropdownContainer ,orderState && styles.stateWrapper1)}>
        <div className={stateInputFocus ? styles.stateWrapper : ''}>
          <input
            ref={inputRef}
            type="text"
            placeholder="STATE"
            value={inputValue}
            maxLength={2}
            className={clsx(styles.addressInputs, !!errorInput && styles.errorInputHeaderDetails)}
            onFocus={handleInputFocus}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            disabled={states.length === 0 || disabled}
          />
          {stateInputFocus && <DropdownIcon onClick={handleClickAway} />}
        </div>
        
        {stateInputFocus && states.length > 0 && (
          <div className={styles.stateMain}>
          <StatesList
            states={states}
            onStateClick={handleStateSelection}
            inputValue={inputValue}
            selectedIndex={selectedIndex}
          />
          </div>
        )}
      </div>
    </ClickAwayListener>
  );
};

export default StateDropDown;
