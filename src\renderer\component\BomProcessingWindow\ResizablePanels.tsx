import React, {
  use<PERSON>allback,
  useEffect,
  useImperative<PERSON>andle,
  useMemo,
  useRef,
  useState,
  forwardRef,
  type ReactNode,
  type CSSProperties,
  type Ref,
} from 'react';

// Types
export interface PanelSize {
  min?: number;
  max?: number;
  default?: number;
}

export interface ResizeCallbackData {
  panelIndex: number;
  newSize: number;
  oldSize: number;
  sizes: number[];
}

export interface ResizeHandleProps {
  panelIndex: number;
  direction: 'horizontal' | 'vertical';
  onMouseDown: (event: React.MouseEvent) => void;
  onTouchStart: (event: React.TouchEvent) => void;
  className?: string;
  style?: CSSProperties;
  'data-resizable-handle'?: string;
  'data-handle-index'?: number;
  'data-handle-direction'?: string;
  'data-handle-panel-left'?: number;
  'data-handle-panel-right'?: number;
  'data-handle-disabled'?: string;
  'data-handle-resizing'?: string;
  'data-handle-snap-size'?: number;
  'data-handle-snap-threshold'?: number;
}

// Animation options interface
export interface AnimationOptions {
  duration?: number;
  easing?: string;
  onComplete?: () => void;
}

export interface ResizablePanelsProps {
  children: ReactNode[];
  direction?: 'horizontal' | 'vertical';
  sizes?: number[];
  defaultSizes?: number[];
  minSizes?: number[];
  maxSizes?: number[];
  onResize?: (data: ResizeCallbackData) => void;
  onResizeStart?: (data: ResizeCallbackData) => void;
  onResizeEnd?: (data: ResizeCallbackData) => void;
  className?: string;
  style?: CSSProperties;
  panelClassName?: string;
  panelStyle?: CSSProperties;
  handleClassName?: string;
  handleStyle?: CSSProperties;
  disabled?: boolean;
  snapToSize?: number;
  snapThreshold?: number;
  renderHandle?: (props: ResizeHandleProps) => ReactNode;
  // Animation props
  animationDuration?: number;
  animationEasing?: string;
  onAnimationComplete?: () => void;
}

export interface ResizablePanelsRef {
  getSizes: () => number[];
  setSizes: (sizes: number[], animate?: boolean | AnimationOptions) => void;
  resetSizes: (animate?: boolean | AnimationOptions) => void;
  // New animation methods
  animateToSizes: (sizes: number[], options?: AnimationOptions) => Promise<void>;
  isAnimating: () => boolean;
}

// Constants
const DEFAULT_MIN_SIZE = 50;
const DEFAULT_SNAP_THRESHOLD = 10;
const DEFAULT_SNAP_SIZE = 100;
const DEFAULT_ANIMATION_DURATION = 300;
const DEFAULT_ANIMATION_EASING = 'cubic-bezier(0.4, 0, 0.2, 1)';

// Utility functions
const clamp = (value: number, min: number, max: number): number => {
  return Math.min(Math.max(value, min), max);
};

const normalizeSizes = (sizes: number[]): number[] => {
  const total = sizes.reduce((sum, size) => sum + size, 0);
  return total > 0 ? sizes.map(size => size / total) : sizes.map(() => 1 / sizes.length);
};

// Animation utility functions
const easeInOutCubic = (t: number): number => {
  return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
};

const interpolateSizes = (from: number[], to: number[], progress: number): number[] => {
  return from.map((start, index) => {
    const end = to[index] || 0;
    return start + (end - start) * progress;
  });
};


const ResizablePanels = forwardRef<ResizablePanelsRef, ResizablePanelsProps>(
  (
    {
      children,
      direction = 'horizontal',
      sizes: controlledSizes,
      defaultSizes,
      minSizes = [],
      maxSizes = [],
      onResize,
      onResizeStart,
      onResizeEnd,
      className = '',
      style = {},
      panelClassName = '',
      panelStyle = {},
      handleClassName = '',
      handleStyle = {},
      disabled = false,
      snapToSize = DEFAULT_SNAP_SIZE,
      snapThreshold = DEFAULT_SNAP_THRESHOLD,
      renderHandle,
      animationDuration = DEFAULT_ANIMATION_DURATION,
      animationEasing = DEFAULT_ANIMATION_EASING,
      onAnimationComplete,
    },
    ref: Ref<ResizablePanelsRef>
  ) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const [internalSizes, setInternalSizes] = useState<number[]>([]);
    const [isResizing, setIsResizing] = useState(false);
    const [isAnimating, setIsAnimating] = useState(false);
    const [resizeData, setResizeData] = useState<{
      startSizes: number[];
      startPosition: number;
      panelIndex: number;
    } | null>(null);
    const animationRef = useRef<number | null>(null);

    const isControlled = controlledSizes !== undefined;
    const currentSizes = isControlled ? controlledSizes : internalSizes;

    // Initialize sizes
    useEffect(() => {
      if (isControlled) return;
      if (internalSizes.length > 0) return; // Don't reinitialize if already set

      const panelCount = children.length;
      if (panelCount === 0) return;

      let initialSizes: number[];

      if (defaultSizes && defaultSizes.length === panelCount) {
        initialSizes = normalizeSizes(defaultSizes);
      } else {
        initialSizes = Array(panelCount).fill(1 / panelCount);
      }

      setInternalSizes(initialSizes);
    }, [children.length, defaultSizes, isControlled, internalSizes.length]);

    // Force re-render when container size changes
    const [, forceUpdate] = useState({});
    const forceRerender = useCallback(() => forceUpdate({}), []);

    // Resize observer to handle container size changes
    useEffect(() => {
      if (!containerRef.current) return;
      const resizeObserver = new ResizeObserver(() => {
        forceRerender();
      });

      resizeObserver.observe(containerRef.current);

      return () => {
        resizeObserver.disconnect();
      };
    }, [forceRerender]);

    // Get min/max sizes for a panel
    const getMinSize = useCallback((index: number): number => {
      return minSizes[index] ?? DEFAULT_MIN_SIZE;
    }, [minSizes]);

    const getMaxSize = useCallback((index: number): number => {
      return maxSizes[index] ?? Infinity;
    }, [maxSizes]);

    // Handle resize start
    const handleResizeStart = useCallback(
      (event: React.MouseEvent | React.TouchEvent, panelIndex: number) => {
        if (disabled || !containerRef.current) return;

        event.preventDefault();
        setIsResizing(true);

        const startPosition = direction === 'horizontal' 
          ? ('touches' in event ? event.touches[0].clientX : event.clientX)
          : ('touches' in event ? event.touches[0].clientY : event.clientY);

        setResizeData({
          startSizes: [...currentSizes],
          startPosition,
          panelIndex,
        });

        const callbackData: ResizeCallbackData = {
          panelIndex,
          newSize: currentSizes[panelIndex],
          oldSize: currentSizes[panelIndex],
          sizes: [...currentSizes],
        };
        onResizeStart?.(callbackData);
      },
      [disabled, direction, currentSizes, onResizeStart]
    );

    // Handle resize move
    const handleResizeMove = useCallback(
      (event: MouseEvent | TouchEvent) => {
        if (!isResizing || !resizeData || !containerRef.current) return;

        event.preventDefault();

        const currentPosition = direction === 'horizontal' 
          ? ('touches' in event ? event.touches[0].clientX : event.clientX)
          : ('touches' in event ? event.touches[0].clientY : event.clientY);

        const containerSize = direction === 'horizontal' 
          ? containerRef.current.offsetWidth 
          : containerRef.current.offsetHeight;

        const delta = currentPosition - resizeData.startPosition;
        const deltaNormalized = delta / containerSize;

        const { panelIndex, startSizes } = resizeData;
        const newSizes = [...startSizes];

        // Calculate new sizes
        const leftPanelIndex = panelIndex;
        const rightPanelIndex = panelIndex + 1;

        if (rightPanelIndex >= newSizes.length) return;

        const leftNewSize = startSizes[leftPanelIndex] + deltaNormalized;
        const rightNewSize = startSizes[rightPanelIndex] - deltaNormalized;

        // Apply constraints
        const leftMinSize = getMinSize(leftPanelIndex) / containerSize;
        const leftMaxSize = getMaxSize(leftPanelIndex) / containerSize;
        const rightMinSize = getMinSize(rightPanelIndex) / containerSize;
        const rightMaxSize = getMaxSize(rightPanelIndex) / containerSize;

        const constrainedLeftSize = clamp(leftNewSize, leftMinSize, leftMaxSize);
        const constrainedRightSize = clamp(rightNewSize, rightMinSize, rightMaxSize);

        // Apply snap behavior
        const leftPixelSize = constrainedLeftSize * containerSize;
        const rightPixelSize = constrainedRightSize * containerSize;

        const leftSnappedSize = Math.abs(leftPixelSize - snapToSize) < snapThreshold 
          ? snapToSize / containerSize 
          : constrainedLeftSize;
        const rightSnappedSize = Math.abs(rightPixelSize - snapToSize) < snapThreshold 
          ? snapToSize / containerSize 
          : constrainedRightSize;

        // Apply the new sizes to only the two adjacent panels
        newSizes[leftPanelIndex] = leftSnappedSize;
        newSizes[rightPanelIndex] = rightSnappedSize;

        // Calculate the total of all panels
        const total = newSizes.reduce((sum, size) => sum + size, 0);
        
        // If the total is not 1, we need to adjust the two resized panels proportionally
        if (Math.abs(total - 1) > 0.001) {
          // Calculate how much space the other panels are taking
          const otherPanelsTotal = newSizes.reduce((sum, size, index) => 
            index === leftPanelIndex || index === rightPanelIndex ? sum : sum + size, 0
          );
          
          // The two resized panels should take up the remaining space
          const availableSpace = 1 - otherPanelsTotal;
          const resizedPanelsTotal = leftSnappedSize + rightSnappedSize;
          
          // Scale the two panels proportionally to fit the available space
          const scaleFactor = availableSpace / resizedPanelsTotal;
          newSizes[leftPanelIndex] = leftSnappedSize * scaleFactor;
          newSizes[rightPanelIndex] = rightSnappedSize * scaleFactor;
        }
        
        const normalizedSizes = newSizes;

        if (!isControlled) {
          setInternalSizes(normalizedSizes);
        }

        const callbackData: ResizeCallbackData = {
          panelIndex: leftPanelIndex,
          newSize: normalizedSizes[leftPanelIndex],
          oldSize: startSizes[leftPanelIndex],
          sizes: normalizedSizes,
        };

        console.log('Resize move:', {
          panelIndex: leftPanelIndex,
          newSizes: normalizedSizes,
          delta: deltaNormalized
        });
        onResize?.(callbackData);
      },
      [isResizing, resizeData, direction, getMinSize, getMaxSize, snapToSize, snapThreshold, isControlled, onResize]
    );

    // Handle resize end
    const handleResizeEnd = useCallback(() => {
      if (!isResizing || !resizeData) return;

      setIsResizing(false);

      const callbackData: ResizeCallbackData = {
        panelIndex: resizeData.panelIndex,
        newSize: currentSizes[resizeData.panelIndex],
        oldSize: resizeData.startSizes[resizeData.panelIndex],
        sizes: [...currentSizes],
      };

      onResizeEnd?.(callbackData);
      setResizeData(null);
    }, [isResizing, resizeData, currentSizes, onResizeEnd]);

    // Event listeners
    useEffect(() => {
      if (!isResizing) return;

      const handleMouseMove = (e: MouseEvent) => handleResizeMove(e);
      const handleMouseUp = () => handleResizeEnd();
      const handleTouchMove = (e: TouchEvent) => handleResizeMove(e);
      const handleTouchEnd = () => handleResizeEnd();

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.addEventListener('touchmove', handleTouchMove, { passive: false });
      document.addEventListener('touchend', handleTouchEnd);

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.removeEventListener('touchmove', handleTouchMove);
        document.removeEventListener('touchend', handleTouchEnd);
      };
    }, [isResizing, handleResizeMove, handleResizeEnd]);

    // Animation functions
    const animateToSizes = useCallback(
      (targetSizes: number[], options: AnimationOptions = {}): Promise<void> => {
        return new Promise((resolve) => {
          if (isControlled || isAnimating) {
            resolve();
            return;
          }

          const normalizedTarget = normalizeSizes(targetSizes);
          const startSizes = [...currentSizes];
          const duration = options.duration ?? animationDuration;
          const easing = options.easing ?? animationEasing;
          
          setIsAnimating(true);
          
          const startTime = performance.now();
          
          const animate = (currentTime: number) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Apply easing function
            const easedProgress = easing === 'linear' ? progress : easeInOutCubic(progress);
            
            const interpolatedSizes = interpolateSizes(startSizes, normalizedTarget, easedProgress);
            setInternalSizes(interpolatedSizes);
            
            if (progress < 1) {
              animationRef.current = requestAnimationFrame(animate);
            } else {
              setIsAnimating(false);
              animationRef.current = null;
              options.onComplete?.();
              onAnimationComplete?.();
              resolve();
            }
          };
          
          animationRef.current = requestAnimationFrame(animate);
        });
      },
      [currentSizes, isControlled, isAnimating, animationDuration, animationEasing, onAnimationComplete]
    );

    // Cleanup animation on unmount
    useEffect(() => {
      return () => {
        if (animationRef.current) {
          cancelAnimationFrame(animationRef.current);
        }
      };
    }, []);

    // Imperative API
    useImperativeHandle(ref, () => ({
      getSizes: () => [...currentSizes],
      setSizes: (newSizes: number[], animate?: boolean | AnimationOptions) => {
        if (isControlled) return;
        
        if (animate === false || animate === undefined) {
          const normalized = normalizeSizes(newSizes);
          setInternalSizes(normalized);
        } else {
          const options = typeof animate === 'boolean' ? {} : animate;
          animateToSizes(newSizes, options);
        }
      },
      resetSizes: (animate?: boolean | AnimationOptions) => {
        if (isControlled) return;
        
        const resetSizes = Array(children.length).fill(1 / children.length);
        
        if (animate === false || animate === undefined) {
          setInternalSizes(resetSizes);
        } else {
          const options = typeof animate === 'boolean' ? {} : animate;
          animateToSizes(resetSizes, options);
        }
      },
      animateToSizes,
      isAnimating: () => isAnimating,
    }), [currentSizes, isControlled, children.length, animateToSizes, isAnimating]);

    // Memoized data attributes for performance
    const containerDataAttributes = useMemo(() => ({
      'data-resizable-panels': '',
      'data-direction': direction,
      'data-panel-count': children.length,
      'data-resizing': isResizing ? 'true' : 'false',
      'data-animating': isAnimating ? 'true' : 'false',
      'data-disabled': disabled ? 'true' : 'false',
    }), [direction, children.length, isResizing, isAnimating, disabled]);

    // Memoized function to generate panel data attributes
    const getPanelDataAttributes = useCallback((index: number, size: number) => ({
      'data-resizable-panel': '',
      'data-panel-index': index,
      'data-panel-size': Math.round(size * 10000) / 100, // Round to 2 decimal places for performance
      'data-panel-size-percent': `${Math.round(size * 100)}%`,
      'data-panel-min-size': getMinSize(index),
      'data-panel-max-size': getMaxSize(index) === Infinity ? 'unlimited' : getMaxSize(index),
      'data-panel-is-first': index === 0 ? 'true' : 'false',
      'data-panel-is-last': index === children.length - 1 ? 'true' : 'false',
    }), [children.length, getMinSize, getMaxSize]);

    // Memoized function to generate handle data attributes
    const getHandleDataAttributes = useCallback((index: number) => ({
      'data-resizable-handle': '',
      'data-handle-index': index,
      'data-handle-direction': direction,
      'data-handle-panel-left': index,
      'data-handle-panel-right': index + 1,
      'data-handle-disabled': disabled ? 'true' : 'false',
      'data-handle-resizing': isResizing ? 'true' : 'false',
      'data-handle-snap-size': snapToSize,
      'data-handle-snap-threshold': snapThreshold,
    }), [direction, disabled, isResizing, snapToSize, snapThreshold]);

    // Render
    if (children.length === 0) return null;

    const containerStyle: CSSProperties = {
      display: 'flex',
      flexDirection: direction === 'horizontal' ? 'row' : 'column',
      width: '100%',
      height: '100%',
      ...style,
    };

    const panelBaseStyle: CSSProperties = {
      position: 'relative',
      overflow: 'hidden',
      ...panelStyle,
    };

    const handleBaseStyle: CSSProperties = {
      position: 'relative',
      backgroundColor: '#ccc',
      cursor: direction === 'horizontal' ? 'col-resize' : 'row-resize',
      zIndex: 1,
      flexShrink: 0,
      ...handleStyle,
    };

    if (direction === 'horizontal') {
      handleBaseStyle.width = handleStyle.width || '4px';
      // handleBaseStyle.height = handleStyle.height || '100%';
    } else {
      handleBaseStyle.height = handleStyle.height || '4px';
      handleBaseStyle.width = handleStyle.width || '100%';
    }

    return (
      <div
        ref={containerRef}
        className={`resizable-panels ${className}`}
        style={containerStyle}
        {...containerDataAttributes}
      >
        {children.map((child, index) => {
          const isLast = index === children.length - 1;
          const panelSize = currentSizes[index] || 0;

          const panelStyle: CSSProperties = {
            ...panelBaseStyle,
            flex: `${panelSize} 0 0`,
            minWidth: direction === 'horizontal' ? `${getMinSize(index)}px` : undefined,
            minHeight: direction === 'vertical' ? `${getMinSize(index)}px` : undefined,
            maxWidth: direction === 'horizontal' ? `${getMaxSize(index)}px` : undefined,
            maxHeight: direction === 'vertical' ? `${getMaxSize(index)}px` : undefined,
            // Add transition for smooth animations when not manually resizing
            transition: isResizing ? 'none' : `flex ${animationDuration}ms ${animationEasing}`,
          };

          return (
            <React.Fragment key={index}>
              <div
                className={`resizable-panel ${panelClassName}`}
                style={panelStyle}
                {...getPanelDataAttributes(index, panelSize)}
              >
                {child}
              </div>
              {!isLast && (
                renderHandle ? (
                  renderHandle({
                    panelIndex: index,
                    direction,
                    onMouseDown: (e) => handleResizeStart(e, index),
                    onTouchStart: (e) => handleResizeStart(e, index),
                    className: handleClassName,
                    style: handleBaseStyle,
                    ...getHandleDataAttributes(index),
                  })
                ) : (
                  <div
                    className={`resizable-handle ${handleClassName}`}
                    style={handleBaseStyle}
                    onMouseDown={(e) => handleResizeStart(e, index)}
                    onTouchStart={(e) => handleResizeStart(e, index)}
                    {...getHandleDataAttributes(index)}
                  />
                )
              )}
            </React.Fragment>
          );
        })}
      </div>
    );
  }
);

ResizablePanels.displayName = 'ResizablePanels';

export default ResizablePanels;