<svg width="66" height="66" viewBox="0 0 66 66" fill="none" xmlns="http://www.w3.org/2000/svg">
    <foreignObject x="-13.995" y="-13.995" width="93.99" height="93.99">
        <div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(6.93px);clip-path:url(#j0x9oubrea);height:100%;width:100%"/>
    </foreignObject>
    <g filter="url(#tygjjx0wob)" data-figma-bg-blur-radius="13.865">
        <circle cx="33" cy="33" r="32.3" fill="#000" fill-opacity=".25"/>
        <circle cx="33" cy="33" r="32.647" stroke="url(#5hpshz50bc)" stroke-opacity=".2" stroke-width=".693"/>
    </g>
    <path d="M23.903 22.505c0-1.616 1.75-2.626 3.149-1.819L45.243 31.18c1.4.808 1.4 2.829 0 3.637L27.052 45.309c-1.4.807-3.149-.203-3.149-1.819V22.505z" fill="#fff"/>
    <defs>
        <linearGradient id="5hpshz50bc" x1="57.753" y1="5.135" x2="33.007" y2="65.307" gradientUnits="userSpaceOnUse">
            <stop stop-color="#fff"/>
            <stop offset="1" stop-color="#fff" stop-opacity="0"/>
        </linearGradient>
        <clipPath id="j0x9oubrea" transform="translate(13.995 13.995)">
            <circle cx="33" cy="33" r="32.3"/>
        </clipPath>
        <filter id="tygjjx0wob" x="-13.995" y="-13.995" width="93.99" height="93.99" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feMorphology radius="1.012" operator="dilate" in="SourceAlpha" result="effect1_innerShadow_924_7500"/>
            <feOffset dx="-.506" dy=".506"/>
            <feGaussianBlur stdDeviation=".253"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
            <feBlend in2="shape" result="effect1_innerShadow_924_7500"/>
        </filter>
    </defs>
</svg>
