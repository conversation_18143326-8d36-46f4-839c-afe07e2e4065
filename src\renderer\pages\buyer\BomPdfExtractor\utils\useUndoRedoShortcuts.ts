import { useEffect, useCallback } from "react";

const useUndoRedoShortcuts = (onUndo: () => void, onRedo: () => void) => {
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    const isMac = /(Mac|iPhone|iPod|iPad)/i.test(navigator.platform);
    const ctrlOrCmd = isMac ? e.metaKey : e.ctrlKey;

    if (ctrlOrCmd && e.key.toLowerCase() === "z") {
      e.preventDefault();
      if (e.shiftKey) {
        onRedo();
      } else {
        onUndo();
      }
    }
    if (!isMac && e.ctrlKey && e.key.toLowerCase() === "y") {
      e.preventDefault();
      onRedo();
    }
  }, [onUndo, onRedo]);

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [handleKeyDown]);
};

export default useUndoRedoShortcuts;
