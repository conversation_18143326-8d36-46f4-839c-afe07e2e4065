// @ts-nocheck
import { useEffect, useState } from 'react';
import styles from './order.module.scss'
import { routes, purchaseOrder, orderPageConst, commomKeys } from '../../common';
import { useNavigate } from 'react-router';
import { ReactComponent as YellowWarningIcon } from '../../assets/images/Icon.svg';
import { ReactComponent as OrderCheckIcon } from '../../assets/images/Order-Checkmark.svg';
import { Dialog, Fade, Tooltip } from '@mui/material';
import { AcceptTooltip, activeOrderTooltip, orderPageTooltip, pendingOrderTooltip } from '../../tooltip';
import clsx from 'clsx';
import { useGlobalStore, useSellerOrderStore } from '@bryzos/giss-ui-library';
import moment from 'moment';
import 'moment-timezone';
import { getPurchaseOrderFilteredList } from '../../helper/commonFunctionality';
import { formatToTwoDecimalPlaces } from '../../helper';
import axios from 'axios';
import { ReactComponent as QuestionIcon } from '../../assets/images/setting-question.svg';
import { ReactComponent as QuestionHoverIcon } from '../../assets/images/question-white-hover.svg';
import { CommonTooltip } from '../../component/Tooltip/tooltip';
import DialogPopup from '../../component/DialogPopup/DialogPopup';
import SearchHeader from '../SearchHeader';
import { useRightWindowStore } from '../RightWindow/RightWindowStore';
import OrderActionsRightWindow from 'src/renderer/component/OrderActionsRightWindow/OrderActionsRightWindow';

const Order = () => {
    const navigate = useNavigate();
    const purchaseOrdersList = useSellerOrderStore(state => state.ordersCart);
    const setShowAlertForNewPo = useSellerOrderStore(state => state.setShowAlertForNewPo);
    const filterPoStoreBy = useSellerOrderStore(state => state.filterPoStoreBy);
    const setFilterPoStoreBy = useSellerOrderStore(state => state.setFilterPoStoreBy);
    const { userData, backNavigation, setBackNavigation, navigationStateForNotification, setNavigationStateForNotification, setViewedOrdersListForBadgeCount, referenceData, productMapping } = useGlobalStore();
    const stateRef = referenceData?.ref_states;
    const filteredPoList = useSellerOrderStore(state => state.filteredPoList);
    const setFilteredPoList = useSellerOrderStore(state => state.setFilteredPoList);
    const setOrderToBeShownInOrderAccept = useSellerOrderStore(state => state.setOrderToBeShownInOrderAccept);
    const searchByProductOrZipValue = useSellerOrderStore(state => state.searchByProductOrZipValue);
    const setSearchByProductOrZipValue = useSellerOrderStore(state => state.setSearchByProductOrZipValue);
    const availableAfter = referenceData?.ref_general_settings?.find(setting => setting.name === "SELLER_AVAIL_IN_MINUTES")?.value;

    const [popupMessage, setPopupMessage] = useState("");
    const [dialogTitle, setDialogTitle] = useState('');
    const [dialogContent, setDialogContent] = useState('');
    const [dialogBtnTitle, setDialogBtnTitle] = useState('');
    const [dialogType, setDialogType] = useState('');
    const [showDialogPopup, setShowDialogPopup] = useState(false);
    const { setShowLoader } = useGlobalStore();
    const { setLoadComponent, setProps} = useRightWindowStore();
    const [selectedOrder, setSelectedOrder] = useState(null);
    const [selectedOrderIndex, setSelectedOrderIndex] = useState(null);

    console.log("orderr page")

    useEffect(() => {
        setShowAlertForNewPo(false);
        return (() => {
            setBackNavigation(-1)
        })
    }, [])


      useEffect(() => {
            setLoadComponent(<OrderActionsRightWindow />);
            return () => {
                setLoadComponent(null);  
                
            }
        }, []);
    

    useEffect(() => {
        const poNumber = navigationStateForNotification?.referenceId;
        if (poNumber && purchaseOrdersList) {
            const index = purchaseOrdersList.findIndex(order => order.buyer_po_number === poNumber);
            setNavigationStateForNotification(null);
            if (index >= 0) {
                navigateToAcceptOrder(null, purchaseOrdersList[index], index, false);
            }
            else {
                console.log("Error");
                setDialogTitle(commomKeys.info);
                setDialogContent(orderPageConst.orderNotAvaialableMsg);
                setDialogBtnTitle(commomKeys.errorBtnTitle);
                setDialogType(commomKeys.actionStatus.error);
                setShowDialogPopup(true);
            }
        }
    }, [navigationStateForNotification]);

    useEffect(() => {
        setShowLoader(true);
        if (purchaseOrdersList) {
            const viewedOrderList = purchaseOrdersList.reduce((list, currentOrder) => {
                if (currentOrder.claimed_by === purchaseOrder.readyToClaim) {
                    list.push(currentOrder.buyer_po_number);
                }
                return list;
            }, []);
            const payload = {
                data: {
                    "viewedOrdersListForBadgeCount": viewedOrderList
                }
            }
            try {
                axios.post(import.meta.env.VITE_API_NOTIFICATION_SERVICE + '/notification/saveUserUiEventData', payload).then(() => {
                    setViewedOrdersListForBadgeCount(viewedOrderList);
                });
            } catch (error) {
                console.error('error updating userJsonData: ', error);
            }
            const poListToBeShown = getPurchaseOrderFilteredList(purchaseOrdersList, filterPoStoreBy, searchByProductOrZipValue, productMapping);
            setFilteredPoList(poListToBeShown);
            setShowLoader(false);
        }
    }, [filterPoStoreBy, searchByProductOrZipValue, purchaseOrdersList]);

    const changePoOrderFilterBy = (filter) => {
        if (filter === filterPoStoreBy) setFilterPoStoreBy('');
        else setFilterPoStoreBy(filter);
    }

    const navigateToAcceptOrder = ($event, purchaseOrder, index, showPopup) => {
        $event?.stopPropagation();
        setPopupMessage("");
        setOrderToBeShownInOrderAccept(purchaseOrder);
        const data = { showPopup, index };
        navigate(routes.acceptOrderPage, { state: data });
    };

    const purchaseOrderTemplate = filteredPoList.map((order, index) => {
        const state = stateRef?.find(stateDetail => stateDetail.id == order.state_id).code;
        const deliveryDate = moment.utc(order.delivery_date).tz('America/Chicago').format('M/DD/YY');
        let iconStatus =
            <Tooltip
                title={activeOrderTooltip()}
                arrow
                placement={'bottom-start'}
                disableInteractive
                TransitionComponent={Fade}
                TransitionProps={{ timeout: 600 }}
                classes={{
                    tooltip: 'orderPageTooltip',
                }}
            >
                <div>
                    <OrderCheckIcon />
                </div>
            </Tooltip>;
        let showAvailableTime = false;
        let availableTime = '';
        const materialValue = +order.seller_po_price;
        const salesTaxValue = +order.seller_sales_tax;
        const totalOrderValue = (materialValue + salesTaxValue).toFixed(2);
        const createdDate = order.payment_method === purchaseOrder.paymentMethodBNPL ? order.created_date : order.ach_po_approved_date;
        const distinctHeaderSet = new Set();
        order.items.forEach((item) => distinctHeaderSet.add(item.shape));
        let poHeader = '';
        let loop = 0;
        distinctHeaderSet.forEach((item) => {
            poHeader += item;
            if (loop < distinctHeaderSet.size - 1) poHeader += ', ';
            loop++;
        })
        if (order.claimed_by === purchaseOrder.pending) {
            iconStatus =
                <Tooltip
                    title={pendingOrderTooltip()}
                    arrow
                    placement={'bottom-start'}
                    disableInteractive
                    TransitionComponent={Fade}
                    TransitionProps={{ timeout: 600 }}
                    classes={{
                        tooltip: 'orderPageTooltip',
                    }}
                >
                    <div>
                        <YellowWarningIcon />
                    </div>
                </Tooltip>;
            showAvailableTime = true;
            availableTime = moment.utc(createdDate)
                .local()
                .add((+availableAfter) + 1, 'minutes')
                .format('h:mm a');
        }
        return (<li key={index}>
            <button className={clsx(styles.liOfOrder, selectedOrder === order ? styles.selectedOrder : '')} id='orderListElement' key={index} onClick={($event) => handleClickOrder($event, order, index, false)}>
                <div className={styles.availOrderMain}>
                    <div className={styles.iconBox}> {iconStatus}</div>
                    <div className={styles.availOrderRightCol}>
                        <div className={styles.availOrderTitle}>
                            {poHeader}
                        </div>
                        <div className={styles.availOrderDetails}>
                            <div className={styles.colLeft}>
                                <p className={styles.deliverTo}><span>Deliver to:</span><span>{order.city}, {state}</span></p>
                                <p className={styles.deliverTo}><span>Delivery by:</span><span>{deliveryDate}</span></p>
                            </div>
                            <div className={styles.colLeft}>
                                <p className={styles.deliverTo}><span>Order Value:</span> <span>$ {formatToTwoDecimalPlaces(totalOrderValue)}</span></p>
                                <p className={styles.deliverTo}><span>Total Wgt (lb):</span> <span>{formatToTwoDecimalPlaces(order.total_weight)}</span></p>
                            </div>
                            <div className={styles.acceptOrderBtnSection}>
                                {!showAvailableTime && <div className={styles.btnOfAHText}>
                                    <Tooltip
                                        title={order.is_order_view && AcceptTooltip()}
                                        arrow
                                        placement={'bottom'}
                                        disableInteractive
                                        TransitionComponent={Fade}
                                        TransitionProps={{ timeout: 200 }}
                                        classes={{
                                            tooltip: 'acceptTooltip',
                                        }}
                                    >
                                        <span>
                                            <button className={styles.accpetRejectBtn} onClick={($event) => navigateToAcceptOrder($event, order, index, true)} disabled={!order.is_order_view}>A</button>
                                        </span>
                                    </Tooltip>
                                    <button className={styles.accpetRejectBtn} onClick={$event => $event.stopPropagation()} disabled={true}>H</button>
                                </div>}
                                {showAvailableTime && <div className={styles.availcliam}><span className={styles.claimTitle}>Available to Claim</span>
                                <span className={styles.claimTime}>@ {availableTime}</span> </div>}
                            </div>

                        </div>


                    </div>

                </div>

            </button>

        </li>);
    });

    const handleClickOrder = (e, order, index, showPopup) => {
        e.stopPropagation();
        if(index === selectedOrderIndex) {
            setSelectedOrder(null);
            setSelectedOrderIndex(null);
        }
        else {
            setSelectedOrder(order);
            setSelectedOrderIndex(index);
        }
    }

    useEffect(() => {
        setProps({
            selectedOrder, selectedOrderIndex,
            navigateToAcceptOrder
        })
    },[selectedOrder, selectedOrderIndex])

    return (
        <div>
            <div className={clsx(styles.orderContent)}>
                {/* <div className={clsx(styles.orderSearch, 'orderSearchBg')}>
                    <input 
                    type='search' 
                    name='search' 
                    className={styles.orderSearch} 
                    placeholder='Search Orders by Product or Zip' 
                    value={searchByProductOrZipValue} 
                    onChange={ $event => setSearchByProductOrZipValue($event.target.value) }
                    autoComplete="off"
                    />
                    <div className={styles.btnOfOrderSearch}>
                        <CommonTooltip
                            title={"Click here to quickly see what orders are currently available to pick up"}
                            tooltiplabel={<button className={filterPoStoreBy === purchaseOrder.readyToClaim ? styles.activeBtn : ''} onClick={()=>{changePoOrderFilterBy(purchaseOrder.readyToClaim)}}>Available</button>}
                            placement={'left-start'}
                            classes={{
                                popper: 'tooltipPopper',
                                tooltip: 'tooltipMain2 tooltipOrder',
                                arrow: 'tooltipLeftArrow'
                            }}
                            localStorageKey="availableBtnOfTooltip"
                        />
                        <CommonTooltip
                            title={"Click here to see upcoming orders that are still in preview mode"}
                            tooltiplabel={<button className={filterPoStoreBy === purchaseOrder.pending ? styles.activeBtn : ''} onClick={()=>{changePoOrderFilterBy(purchaseOrder.pending)}}>Upcoming</button>}
                            placement={'left-start'}
                            classes={{
                                popper: 'tooltipPopper',
                                tooltip: 'tooltipMain2 tooltipOrder',
                                arrow: 'tooltipLeftArrow'
                            }}
                            localStorageKey="upcomingBtnOfTooltip"
                        />
                    </div>
                </div> */}
                {/* <div className={styles.orderHeading}>
                    <div className={styles.bigHeading}>Click Accept or <span className={styles.aTag}>A</span> to make a sale...that simple
                        <Tooltip
                            title={orderPageTooltip()}
                            arrow
                            placement={'bottom'}
                            disableInteractive
                            TransitionComponent={Fade}
                            TransitionProps={{ timeout: 600 }}
                            classes={{
                                tooltip: 'orderPageTooltip',
                            }}
                        >
                            <span className='questionIconPriceChange'>
                                <QuestionIcon className='questionIcon1' />
                                <QuestionHoverIcon className='questionIcon2' />
                            </span>
                        </Tooltip>
                    </div>
                    <div className={styles.smallHeading}>Click on the order to see the line item detail</div>
                </div> */}
                <div className={styles.orderAcceptNote}>
                    <p>These orders are ready to claim for your fulfillment. Click Accept to make a sale... that simple.</p>
                    <p>Click on any order to view specific line item details.</p>
                </div>
                <div className={styles.listOfOrder}>
                    <ul id='ordersList'>
                        {purchaseOrderTemplate}
                    </ul>
                </div>

            </div>
            <Dialog
                open={!!popupMessage}
                transitionDuration={200}
                hideBackdrop
                classes={{
                    root: styles.ErrorDialog,
                    paper: styles.dialogContent
                }}
            >
                <>
                    <p>{popupMessage}</p>
                    <button className={styles.submitBtn} onClick={() => setPopupMessage('')}>Ok</button>
                </>
            </Dialog>
            <DialogPopup
                dialogTitle={dialogTitle}
                dialogContent={dialogContent}
                dialogBtnTitle={dialogBtnTitle}
                type={dialogType}
                open={showDialogPopup}
                onClose={() => setShowDialogPopup(false)}
            />
        </div>
    )
}
export default Order;