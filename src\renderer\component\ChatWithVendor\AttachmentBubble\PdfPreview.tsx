import React, { useEffect, useRef, useState } from 'react';
import * as pdfjsLib from 'pdfjs-dist';
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.min?url'

// Set up PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker

interface PdfPreviewProps {
  url: string;
  name: string;
  extension: string;
}

const PdfPreview: React.FC<PdfPreviewProps> = ({ url, name, extension }) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const [canvasReady, setCanvasReady] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pdfLoaded, setPdfLoaded] = useState(false);

  useEffect(() => {
    const loadPdf = async () => {
      try {
        setLoading(true);
        setError(null);
        setPdfLoaded(false);
        
        // Load only the first page
        const pdf = await pdfjsLib.getDocument(url).promise;
        
        const page = await pdf.getPage(1); // Always load page 1
        const viewport = page.getViewport({ scale: 0.8 });
        
        const canvas = canvasRef.current;
        if (!canvas || !canvasReady) {
            console.error('Canvas not ready');
            setError('Canvas not ready');
            setLoading(false);
          return;
        }
        
        const context = canvas.getContext('2d');
        if (!context) {
          console.error('Canvas context not available');
          setError('Canvas context not available');
          setLoading(false);
          return;
        }
        
        // Set canvas dimensions
        canvas.height = viewport.height;
        canvas.width = viewport.width;
    
        
        const renderContext = {
          canvasContext: context,
          viewport: viewport,
        };
        
        // Render the page
        const renderTask = page.render(renderContext);
        await renderTask.promise;
        
        
        setPdfLoaded(true);
        setLoading(false);
      } catch (err) {
        console.error('Error loading PDF:', err);
        setError('Failed to load PDF preview');
        setLoading(false);
        setPdfLoaded(false);
      }
    };

    if (url && canvasReady) {
      loadPdf();
    }
  }, [url, canvasReady]);

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      gap: 0
    }}>
     
      
      {/* PDF Preview Frame */}
      <div style={{
        height: '300px',
        width: '100%',
        border: 'none',
        overflow: 'hidden',
        position: 'relative',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        {loading && (
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center', 
            height: '100%',
            color: '#ffffff',
            fontSize: '14px'
          }}>
            Loading PDF...
          </div>
        )}
        
        {error && (
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center', 
            height: '100%',
            color: '#ff6b6b',
            fontSize: '14px',
            textAlign: 'center',
            padding: '20px'
          }}>
            {error}
          </div>
        )}
        
       
         <canvas 
         ref={(el) => {
           canvasRef.current = el;
           if (el) setCanvasReady(true);
         }}
         style={{
            display: loading ? 'none' : 'block',
           maxWidth: '100%',
           maxHeight: '100%',
           border: 'none',
           background: '#ffffff',
           borderRadius: '4px'
         }}
       />
       
      </div>
      <div style={{
        padding: '8px',
      }}>
        <div style={{
          fontWeight: 600,
          fontSize: '12px',
          color: '#ffffff',
          wordBreak: 'break-word',
          lineHeight: 1.4,
          marginBottom: '4px'
        }}>
          {name}
        </div>
        <div style={{
          fontSize: '10px',
          color: '#a0a0a0',
          fontWeight: 500,
          textTransform: 'uppercase',
          letterSpacing: '0.5px'
        }}>
          {extension.toUpperCase()} Document
        </div>
      </div>
    </div>
  );
};

export default PdfPreview;