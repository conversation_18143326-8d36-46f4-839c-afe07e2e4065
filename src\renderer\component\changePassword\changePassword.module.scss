.resetPasscontainer {
    font-family: Inter;
    display: flex;
    flex-direction: column;
    display: flex;
    align-items: center;
    flex-direction: column;
    width: 560px;
    padding: 80px 40px 40px;
    border-radius: 20px;
    background: url(../../assets/images/Login-BG.svg) no-repeat;
    margin: 112px auto 150px auto;
    background-size: cover;


    &.changePassPopup {
        min-height: auto;
        padding: 24px 40px 0px 40px;
        // width: 100%;
        margin: 0;

        .resetPassTitle {
            margin-bottom: 16px;
            padding: 0px;
        }

        .changePassInnerContent {
            padding: 0px;
        }

        .btnSection {
            border-top: 0.5px solid transparent;
            margin-top: 40px;

            button {
                width: 100%;
                border-radius: 10px;

                height: 50px;

                text-transform: uppercase;

                &.saveBtnChangePass {
                    box-shadow: 0 -4px 4px 0 rgba(0, 0, 0, 0.8);
                    background-color: #fff;
                    font-family: Syncopate;
                    font-size: 18px;
                    font-weight: bold;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.3;
                    letter-spacing: -0.72px;
                    text-align: center;
                    color: #fff;
                }

                &[disabled] {
                    cursor: not-allowed;
                    font-family: Syncopate;
                    font-size: 18px;
                    font-weight: bold;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.3;
                    letter-spacing: -0.72px;
                    text-align: center;
                    color: rgba(255, 255, 255, 0.4);
                    box-shadow: none;
                }

                &:not([disabled]):hover {
                    color: #fff;
                }
            }
        }
    }

    .resetPassTitle {
        font-family: Syncopate;
        font-size: 16px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: 1.28px;
        text-align: center;
        color: #fff;
        text-transform: uppercase;
        margin-bottom: 48px;
    }

    .bryzosTitle {
        font-family: Syncopate;
        font-size: 28px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: -1.12px;
        text-align: left;
        color: #fff;
        margin-bottom: 40px;
    }


    .noteText {
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.4;
        text-align: left;
        color: rgba(255, 255, 255, 0.75);
        margin-bottom: 16px;
        padding: 0px 24px;
    }

    .changePassInnerContent {
        flex-grow: 1;
        width: 100%;

        .FormInputGroup {
            display: flex;
            margin-bottom: 12px;

            .lblInput {
                font-family: Inter;
                font-size: 14px;
                font-weight: normal;
                line-height: 1.6;
                text-align: left;
                color: #fff;
                width: 152px;
                height: 34px;
                display: flex;
                flex-direction: row;
                justify-content: flex-start;
                align-items: center;
                padding: 6px 4px 6px 10px;
                border: solid 0.5px #000;
                background-color: rgba(255, 255, 255, 0.1);
                border-radius: 4px 0px 0px 4px;
                border-right: 0px;
            }

            .inputSection.inputSection {
                font-family: Inter;
                height: 34px;
                display: flex;
                flex-direction: row;
                justify-content: flex-start;
                align-items: center;
                padding: 6px 6px 6px 10px;
                border: solid 0.5px #000;
                background-color: rgba(255, 255, 255, 0.2);
                border-radius: 0px 4px 4px 0px;
                flex: 1 1;

                &.errorEmail.errorEmail {
                    border: solid 2px #f00;
                    color: rgba(0, 0, 0, 0.75);

                    &:focus {
                        border: solid 2px #f00;
                    }
                }

                &.paddingLR0 {
                    padding-left: 0px;
                    padding-right: 0px;
                }


                &.bdrRadius0 {
                    border-radius: 0px;
                }

                &.bdrRight0 {
                    border-right: 0px;
                }


                input {
                    font-family: Inter;
                    font-size: 14px;
                    font-weight: normal;
                    text-align: left;
                    color: #fff;
                    border: 0px;
                    background-color: transparent;
                    padding: 0px;
                    width: 100%;

                    &:focus {
                        outline: none;
                        box-shadow: none;
                    }

                    &::placeholder {
                          color: rgba(255, 255, 255, 0.2);

                        font-weight: normal;
                    }
                }

                .showPassBtn {
                    padding: 0px;
                    display: flex;
                }
            }
        }

        .currentPassword.currentPassword {
            flex-direction: column;
            display: flex;

            .FormInputGroup {
                margin-bottom: 0px;
            }
        }

        .forgotPasswordTxt {
            font-family: Inter;
            font-size: 12px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: normal;
            letter-spacing: normal;
            text-align: right;
            color: #bff0b2;
            width: 100%;
            padding-top: 8px;
            padding-bottom: 24px;

            span {
                cursor: pointer;
            }
        }
    }

    .btnSection.btnSection {
      display: flex;
      width: 100%;

      button {
        width: 100%;
        height: 50px;
        margin: 48px 0 81px;
        padding: 14px 20px 12px 20px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: Syncopate;
        font-size: 20px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.2;
        letter-spacing: -0.8px;
        text-align: left;
        text-transform: uppercase;
        box-shadow: 0 -5.3px 5.3px 0 rgba(0, 0, 0, 0.8);
        background-image: linear-gradient(115deg, #1c40e7 -7%, #16b9ff 106%);
        color: #fff;

        &:disabled {
          cursor: not-allowed;
          color: rgba(255, 255, 255, 0.2);
          background: url(../../assets/images/Login-Btn-BG.svg) no-repeat;
          box-shadow: none;
          background-size: cover;
          background-color: transparent;
        }
      }
    }
}



.passwordErrorContainer {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 16px;

    .errorBorder {
        position: absolute;
        right: -18px;
        top: 16px;
        border-radius: 0px 3px 3px 0px;
        border: solid 2px #f00;
        width: 20px;
        height: 46px;
        border-left: 0;

        svg {
            position: relative;
            top: 12px;
            right: -10px;
        }
    }
}

.confirmPasswordInput.confirmPasswordInput {
    input{
        width: 100%;
        font-family: Inter;
        font-size: 16px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: 0.64px;
        text-align: left;
        color: #fff;
        background-color: rgba(255, 255, 255, 0.06);

        &:focus {
          background: url(../renderer/assets/images/emailInput.svg);
          background-repeat: no-repeat;
          background-position: bottom right;
          background-size: cover;
          box-shadow: none;
          z-index: 2;
        }
    }
    .passwordInput {
        &:focus-within {
            background: url(../../assets/images/emailInput.svg) no-repeat;
            background-size: cover;
            background-position: bottom right;
        }
    }
}