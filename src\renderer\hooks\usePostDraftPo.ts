import { useGlobalStore } from "@bryzos/giss-ui-library";
import { useMutation } from "@tanstack/react-query";
import axios from "axios";
import { useGlobalSearchStore } from "../pages/GlobalSearchField/globalSearchStore";

const usePostDraftPo = () => {
  const { setCreatePoUpdatedData } = useGlobalSearchStore();
  return useMutation(async (data: any) => {
    try {
      setCreatePoUpdatedData(data);
      const url = data?.id ? `${import.meta.env.VITE_API_ORDER_SERVICE}/buyer/update-draft` : `${import.meta.env.VITE_API_ORDER_SERVICE}/buyer/save-draft`;
      const response = await axios.post(
        url,
        {data}
      );

      if (response.data?.data) {
        return response.data.data;
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default usePostDraftPo;
