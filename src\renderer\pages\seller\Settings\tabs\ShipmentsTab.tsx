import React, { useEffect, useRef, useState } from 'react';
import styles from './TabContent.module.scss';
import { Controller, useForm } from 'react-hook-form';
import CustomToggleCheckbox from 'src/renderer/component/CustomToggleCheckbox';
import clsx from 'clsx';
import { Dialog } from '@mui/material';
import axios from 'axios';
import { useGlobalStore, useSellerSettingStore } from '@bryzos/giss-ui-library';
import CustomAddressComponent from '../components/CustomAddressComponent';
import { ReactComponent as CloseIcon } from '../../../../assets/images/close-icon.svg';
import { unformatPhoneNumber } from 'src/renderer/helper';
import useSaveUserSettings from 'src/renderer/hooks/useSaveUserSettings';
import StateSelector from '../components/StateSelector/StateSelector';
import { yupResolver } from '@hookform/resolvers/yup';
import { shipmentSchema } from '../schemas/shipmentSchema';
import InputWrapper from 'src/renderer/component/InputWrapper';
import CustomTextField from 'src/renderer/component/CustomTextField';
import SingleStateSelector from 'src/renderer/pages/buyer/newSettings/components/StateSelector/SingleStateSelector';
import { useNavigate } from 'react-router-dom';

interface InputFocusState {
  stockingAddress: boolean;
}

const ShipmentsTab= ({setSaveFunctions, locationState}: any) => {
  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    clearErrors,
    setError,
    setValue,
    reset,
    watch,
    control,
    getValues,
    trigger,
    resetField,
    formState: { errors, dirtyFields, isDirty, isValid, isSubmitting },
    getFieldState,
  } = useForm({
    resolver: yupResolver(shipmentSchema),
    mode: 'onSubmit',
  });
  const { userData, showLoader, setShowLoader, referenceData }: any = useGlobalStore();
  const [States, setStates] = useState<any>([]);
  const [customAddressComponentOpen, setCustomAddressComponentOpen] = useState(false);
  const [focusedInput, setFocusedInput] = useState<string | null>(null);
  const [validationInProgress, setValidationInProgress] = useState(true);
  const shipmentPopupRef = useRef(null);
  const { mutateAsync: saveUserSettings } = useSaveUserSettings();
  const [isAddressContainerClicked, setIsAddressContainerClicked] = useState(false);
  const {sellerSettings , setShowFadeLoader}: any = useSellerSettingStore();
  const addressContainerRef = useRef<HTMLDivElement>(null);
  const line1InputRef = useRef<HTMLInputElement>(null);
  const line2InputRef = useRef<HTMLInputElement>(null);
  const cityInputRef = useRef<HTMLInputElement>(null);
  const stateInputRef = useRef<HTMLInputElement>(null);
  const zipInputRef = useRef<HTMLInputElement>(null);
  const [isStateSelectorFocused, setIsStateSelectorFocused] = useState(false);
  const isButtonDisabled =  isSubmitting || !isDirty;

  
  useEffect(() => {
    if(sellerSettings) {
        setValue('stockingAddress', {
          line1: sellerSettings?.stocking_address?.line1 || '',
          line2: sellerSettings?.stocking_address?.line2 || '',
          city: sellerSettings?.stocking_address?.city || '',
          state: sellerSettings?.stocking_address?.state_id || '',
          stateCode: sellerSettings?.stocking_address?.state_code || '',
          zip: sellerSettings?.stocking_address?.zip || '',
        });
        setValue('orderFulfillmentStates', sellerSettings?.order_fulfillment_states || []);
        setValue('orderClaimPreferences', sellerSettings?.order_claim_preferences || false);
    }
}, [sellerSettings]);

  useEffect(() => {
    setSaveFunctions({
        onSave: () => handleSubmit(handleSaveCompany)(),
        isDisabled: isButtonDisabled,
    });
  }, [isButtonDisabled, handleSubmit]);


  useEffect(() => {   
    setTimeout(() => {
        const defaultFocusSpan = document.getElementById('defaultFocusSpan');
        if (defaultFocusSpan) {
            defaultFocusSpan.focus();
        }
    }, 100)
  }, []);

  useEffect(() => {
    if (userData.data.id && referenceData) {
      setStates(referenceData.ref_states);
    }
  }, [referenceData, userData]);

    useEffect(() => {
      setSaveFunctions({
          onSave: () => handleSubmit(handleSaveCompany)(),
          isDisabled: isButtonDisabled,
      });
  }, [isButtonDisabled, handleSubmit]);

  useEffect(() => {
    if(dirtyFields.stockingAddress){
      handleStateZipValidation('stockingAddress.zip', 'stockingAddress.state')
    }
  }, [watch('stockingAddress.state'), watch('stockingAddress.zip')])

  const [isInputFocused, setIsInputFocused] = useState<InputFocusState>({
    stockingAddress: false,
  });

  const handleInputFocus = (inputName: keyof InputFocusState): void => {
    setIsInputFocused((prevState) => ({
      ...prevState,
      [inputName]: true,
    }));
  };

  const handleInputBlur = (inputName: keyof InputFocusState): void => {
    setIsInputFocused((prevState) => ({
      ...prevState,
      [inputName]: false,
    }));
  };

  const handleAddressFieldClick = (fieldType: 'line1' | 'line2' | 'city' | 'state' | 'zip') => {
    setIsAddressContainerClicked(true);

    // Focus on the appropriate input after dialog opens
    setTimeout(() => {
        switch(fieldType) {
            case 'line1':
                line1InputRef?.current?.focus();
                break;
            case 'line2':
                line2InputRef?.current?.focus();
                break;
            case 'city':
                cityInputRef?.current?.focus();
                break;
            case 'state':
                stateInputRef?.current?.focus();
                break;
            case 'zip':
                zipInputRef?.current?.focus();
                break;
            default:
                line1InputRef?.current?.focus();
        }
    }, 100);
  };

  const handleAddressContainerClick = () => {
    if(!(watch('stockingAddress.line1') || watch('stockingAddress.line2') || watch('stockingAddress.city') || watch('stockingAddress.stateCode') || watch('stockingAddress.zip'))){
        setIsAddressContainerClicked(true);
        setTimeout(() => {
            line1InputRef?.current?.focus();
        }, 100);
    }
  };

  const handleStateZipValidation = async (zipCode: any, stateCode: any) => {
    try {
      if (getValues(zipCode)?.length > 4 && getValues(stateCode)) {
        const payload = {
          data: {
            state_id: Number(getValues(stateCode)),
            zip_code: parseInt(getValues(zipCode)),
          },
        };
        const checkStateZipResponse = await axios.post(
          import.meta.env.VITE_API_SERVICE + "/user/checkStateZip",
          payload
        );
        if (checkStateZipResponse.data.data === true) {
          clearErrors([stateCode, zipCode]);
          return true;
        } else {
          setError(stateCode, { message: "The zip code and state code do not match" });
          setError(zipCode, { message: "The zip code and state code do not match" });
          return false;
        }
      }
    } catch (error) {
      console.error(error)
    }
  };


  const handleCheckBoxKeyDown = (e: any) => {
    if (e.key === 'Tab') {
      if(!e.shiftKey){
        e.preventDefault();
      const saveButton = document.getElementById('settings-save-button') as HTMLButtonElement;
      if (saveButton) {
        if (saveButton.disabled) {
          const companyButton = document.getElementById('COMPANY')
          if (companyButton) {
            (companyButton as HTMLElement).focus();
          }
        } else {
          setTimeout(() => {
            saveButton.focus();
          }, 0);
        }
      }
    }
    }
  }

  const handleSelectAllStates = () => {
    setValue('orderFulfillmentStates', States.map((s: any) => s.id), { shouldDirty: true });
  }

  const handleDeselectAllStates = () => {
    setValue('orderFulfillmentStates', [], { shouldDirty: true });
  }

  
  const handleShipmentAddressContainerClickAway = () => {
    handleInputBlur('stockingAddress')
    if (!isStateSelectorFocused) {
      if(!(errors?.stockingAddress?.line1 || errors?.stockingAddress?.line2 || errors?.stockingAddress?.city || errors?.stockingAddress?.state || errors?.stockingAddress?.zip || errors?.stockingAddress?.stateCode)){
        setIsAddressContainerClicked(false)
      }else{
        setIsAddressContainerClicked(true)
      }
    }
  }

  // Custom clickaway handler
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (!isAddressContainerClicked) return;
      
      const target = event.target as HTMLElement;
      
      // Check if click is inside the address container
      if (addressContainerRef.current && addressContainerRef.current.contains(target)) {
        return;
      }
      
      // Check if click is inside any state selector
      const stateSelectorElement = document.querySelector('[data-state-selector]');
      if (stateSelectorElement && stateSelectorElement.contains(target)) {
        return;
      }
      
      // If we get here, the click was outside both the container and state selector
      handleShipmentAddressContainerClickAway();
    };

    // Add event listener when address container is clicked
    if (isAddressContainerClicked) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isAddressContainerClicked]);


  useEffect(() => {
    if(errors?.stockingAddress){
        setIsAddressContainerClicked(true)
    }
  }, [errors])

  const handleSaveCompany = async (data: any) => {
    try{
      const isZipValid = await handleStateZipValidation('stockingAddress.zip', 'stockingAddress.state')
      if(!isZipValid){
        return
      }
        setShowFadeLoader(true)
        const payload = {
          stocking_address: {
              line1: data.stockingAddress.line1,
              line2: data.stockingAddress.line2?.trim() || null,
              city: data.stockingAddress.city,
              state_id: Number(data.stockingAddress.state),
              zip: data.stockingAddress.zip
          },
          order_fulfillment_states: data.orderFulfillmentStates.map((state: any) => Number(state)),
          order_claim_preferences: Boolean(data.orderClaimPreferences)
        }
        await saveUserSettings({ route: 'user/seller/settings/shipment', data: payload })
        setShowFadeLoader(false)

        if (locationState?.previousPath) {
          navigate(locationState.previousPath);
        }
        setTimeout(() => {
            reset({
              ...data,
              orderFulfillmentStates: data.orderFulfillmentStates.map((state: any) => Number(state)),
            }); 
        }, 100)
    }catch(err){
        console.error(err)
        setShowFadeLoader(false)
    }
}
  return (
    <div className={clsx(styles.tabContent, styles.tabContentShipment)} ref={shipmentPopupRef} data-hover-video-id="settings-shipping">
      <div className={styles.scrollerContainer}>
        <div className={styles.formContainer}>

          <span tabIndex={0} id="defaultFocusSpan" onKeyDown={(e) => {
            if (e.key === 'Tab') {
              if(!e.shiftKey){
                e.preventDefault();
                setIsAddressContainerClicked(true)
                setTimeout(() => {
                  line1InputRef.current?.focus();
                }, 100)
              }
            }
          }}></span>

          <div className={clsx(styles.formGroupInput, styles.deliveryAddressContainer)}>
            <span className={styles.col1}>
              <label htmlFor="stockingAddress">
                MAIN STOCKING LOCATION
              </label>
            </span>
            <span className={clsx(styles.col1, styles.locationAddressContainer)}>
              {
                (isAddressContainerClicked || errors?.stockingAddress) ? (
                  <div className={clsx(styles.customAddressContainer)} ref={addressContainerRef}>
                    <span className={clsx(styles.addresInputMain)}>
                      <InputWrapper>
                        <CustomTextField
                          className={clsx(styles.inputCreateAccount, errors?.stockingAddress?.line1 && styles.error)}
                          type='text'
                          register={register('stockingAddress.line1')}
                          placeholder='Address 1'
                          onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                            register('stockingAddress.line1').onBlur(e);
                            handleInputBlur('stockingAddress')
                          }}
                          onFocus={() => handleInputFocus('stockingAddress')}
                          errorInput={errors?.stockingAddress?.line1}
                          onKeyDown={(e) => {
                            if(e.key === 'Tab'){
                              if(e.shiftKey){
                                setIsAddressContainerClicked(false)
                              }
                            }
                          }}
                          inputRef={(e: any) => {
                            line1InputRef.current = e;
                          }}
                        />
                      </InputWrapper>
                    </span>

                    <span className={clsx(styles.addresInputMain)}>
                      <InputWrapper>
                        <CustomTextField
                          className={clsx(styles.inputCreateAccount, errors?.stockingAddress?.line2 && styles.error)}
                          type='text'
                          // autoFocus={true}
                          register={register('stockingAddress.line2')}
                          placeholder='Address 2'
                          onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                            register('stockingAddress.line2').onBlur(e);
                            handleInputBlur('stockingAddress')
                          }}
                          onFocus={() => handleInputFocus('stockingAddress')}
                          errorInput={errors?.stockingAddress?.line2}
                          inputRef={(e: any) => {
                            line2InputRef.current = e;
                          }}
                        />
                      </InputWrapper>

                    </span>


                    <span className={styles.zipInputContainer}>
                      <span className={clsx(styles.col1, styles.addresInputMain)}>
                        <InputWrapper>
                          <CustomTextField
                            className={clsx(styles.inputCreateAccount, errors?.stockingAddress?.city && styles.error)}
                            type='text'
                            register={register('stockingAddress.city')}
                            placeholder='City'
                            onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                              register('stockingAddress.city').onBlur(e);
                              handleInputBlur('stockingAddress')
                            }}
                            onFocus={() => handleInputFocus('stockingAddress')}
                            errorInput={errors?.stockingAddress?.city}
                            inputRef={(e: any) => {
                              cityInputRef.current = e;
                            }}
                          />
                        </InputWrapper>
                      </span>
                      <span className={clsx(styles.inputSection, styles.yourLocationAdressState, styles.col2, styles.bdrRadius0, styles.bdrRight0)}>
                        <Controller
                          name="stockingAddress.state"
                          control={control}
                          render={({ field }) => (
                            <>
                              <SingleStateSelector
                                states={States.map((state: any) => ({ state_code: state.code }))}
                                value={field.value}
                                onChange={(stateCode) => {
                                  const selectedState = States.find((state: any) => state.code === stateCode);
                                  if (selectedState) {
                                    field.onChange(selectedState.id);
                                    setValue('stockingAddress.stateCode', selectedState.code);
                                    // Clear any exis ting errors for the state field
                                    if (errors?.stockingAddress?.state) {
                                      clearErrors('stockingAddress.state');
                                    }
                                    // Trigger validation after setting the value
                                    setTimeout(() => {
                                      trigger('stockingAddress.state');
                                    }, 0);
                                  } else {
                                    console.error('State not found for code:', stateCode);
                                  }
                                }}
                                onBlur={() => {
                                  field.onBlur();
                                  handleInputBlur('stockingAddress');
                                }}
                                error={!!errors?.stockingAddress?.state}
                                placeholder="State"
                                stateIdToCode={(stateId) => {
                                  const state = States.find((s: any) => s.id === stateId);
                                  return state ? state.code : watch('stockingAddress.stateCode');
                                }}
                                onFocus={() => handleInputFocus('stockingAddress')}
                                inputRef={stateInputRef}
                              />

                            </>
                          )}
                        />

                      </span>
                      <span className={clsx(styles.col3, styles.addresInputMain)}>
                        <InputWrapper>
                          <CustomTextField
                            className={clsx(styles.inputCreateAccount, (errors?.stockingAddress?.zip || errors?.stockingAddress?.state) && styles.error)}
                            type='text'
                            maxLength={5}
                            register={register('stockingAddress.zip')}
                            placeholder='Zip Code'
                            onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                              register('stockingAddress.zip').onBlur(e);
                              handleInputBlur('stockingAddress');
                            }}
                            onFocus={() => handleInputFocus('stockingAddress')}
                            errorInput={errors?.stockingAddress?.zip || errors?.stockingAddress?.state}
                            mode="wholeNumber"
                            inputRef={(e: any) => {
                              zipInputRef.current = e;
                            }}
                            onKeyDown={(e) => {
                              if (e.key === 'Tab') {
                                if (!e.shiftKey) {
                                  handleShipmentAddressContainerClickAway()
                                }
                              }
                            }}
                          />
                        </InputWrapper>
                      </span>
                    </span>

                  </div>
                ) : (
                  <div className={styles.addressDisplayContainer} onClick={handleAddressContainerClick}>
                    {
                      (watch('stockingAddress.line1') || watch('stockingAddress.line2') || watch('stockingAddress.city') || watch('stockingAddress.state') || watch('stockingAddress.zip')) ? (
                        <div className={styles.valueDiv}>
                          <p className={clsx(styles.addressInputs, styles.hideInputBackground)}
                             onClick={(e) => {
                                 e.stopPropagation();
                                 handleAddressFieldClick('line1');
                             }}>
                             {watch('stockingAddress.line1') ? `${watch('stockingAddress.line1')}` : ''}
                          </p>
                          <p className={clsx(styles.addressInputs, styles.hideInputBackground)}
                             onClick={(e) => {
                                 e.stopPropagation();
                                 handleAddressFieldClick('line2');
                             }}>
                             {watch('stockingAddress.line2') ? `${watch('stockingAddress.line2')}` : ''}
                          </p>
                          <span className={styles.lastAddressFiled}>
                            <p className={clsx(styles.addressInputsCol1, styles.hideInputBackground)}
                               onClick={(e) => {
                                   e.stopPropagation();
                                   handleAddressFieldClick('city');
                               }}>
                               {watch('stockingAddress.city') ? `${watch('stockingAddress.city')}` : ''}
                            </p>
                            <p className={clsx(styles.addressInputsCol2, styles.hideInputBackground)}
                               onClick={(e) => {
                                   e.stopPropagation();
                                   handleAddressFieldClick('state');
                               }}>
                               {watch('stockingAddress.stateCode') ? `${watch('stockingAddress.stateCode')}` : ''}
                            </p>
                            <p className={clsx(styles.addressInputsCol3, styles.hideInputBackground)}
                               onClick={(e) => {
                                   e.stopPropagation();
                                   handleAddressFieldClick('zip');
                               }}>
                               {watch('stockingAddress.zip') ? `${watch('stockingAddress.zip')}` : ''}
                            </p>
                          </span>
                        </div>
                      ) : (
                        <span className={clsx(styles.valueDiv, styles.placeHolderDiv)} onClick={handleAddressContainerClick}></span>
                      )
                    }
                  </div>
                )
              }
            </span>
          </div>
          <div className={clsx(styles.formGroupInput, styles.whereCanYouFulfillOrders)}>
            <div className={styles.whereCanYouFulfillOrdersHeader}>
              <span className={styles.col1}>
                <label>
                  WHERE CAN YOU FULFILL ORDERS?
                </label>
              </span>
              <div className={styles.stateDropdownContainer}>
                <div className={styles.selectTabLabel}>Select the states that apply to your delivery area.</div>
                <div className={styles.radioGroupContainer}>
                  <button type="button" className={clsx(styles.radioButtonLeft, styles.radioButton)} onClick={handleSelectAllStates} disabled={watch('orderFulfillmentStates')?.length === States.length}
                    onKeyDown={(e) => {
                      if (e.key === 'Tab') {
                        if(e.shiftKey){
                          e.preventDefault();
                          setIsAddressContainerClicked(true)
                          setTimeout(() => {
                            line1InputRef.current?.focus();
                          }, 100)
                        }
                      }
                    }}
                  >Select All</button>
                  <button type="button" className={clsx(styles.radioButtonRight, styles.radioButton)} onClick={handleDeselectAllStates} disabled={watch('orderFulfillmentStates')?.length === 0}>Deselect All</button>
                </div>
              </div>
            </div>
            <div className={styles.stateSelectorContainer}>
              <Controller
                name="orderFulfillmentStates"
                control={control}
                render={({ field }) => (
                  <StateSelector
                    allStates={States}
                    selectedStates={field.value || []}
                    onChange={(newSelection) => {
                      field.onChange(newSelection);
                      setValue('orderFulfillmentStates', newSelection, { shouldDirty: true });
                    }}
                  />
                )}
              />
            </div>


          </div>

          <div className={clsx(styles.formGroupInput, styles.orderClaimPreferencesMain)}>
            <span className={styles.col1}>
              <label>
                ORDER CLAIM PREFERENCES
              </label>
              <span className={styles.col1}>

              </span>
            </span>

            <span className={styles.chkNote}>
              <span className={styles.chkNote1}>
                <CustomToggleCheckbox
                  name="orderClaimPreferences"
                  control={control}
                  onKeyDown={handleCheckBoxKeyDown}
                  onChange={
                    (e: any) => {
                      setValue('orderClaimPreferences', e);
                    }
                  }
                />
                This setting will apply your selected locations to your Available Orders screen.
              </span>

              <span>
              You will only see Purchase Orders destined for delivery to your selected locations above. Select ‘Yes’ to apply this setting. 
              </span>

            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShipmentsTab;


