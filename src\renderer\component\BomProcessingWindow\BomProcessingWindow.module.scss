.container {
    width: 322px;
    height: 520px;
    padding: 20px 20px 19px;
    border-radius: 20px;
    background-origin: border-box;
    position: relative;
    overflow: hidden;
    background: url(../../assets/images/BOM-Upload-Leaderboard-BG-1.svg) no-repeat transparent;
  }
  .createPOPageContainer{
    width: 322px;
    height: 450px;
    padding: 20px 20px 19px;
    border-radius: 20px;
    background-origin: border-box;
    position: relative;
    overflow: hidden;
    margin-top: 30px;
    background: url(/src/renderer/assets/images/BOM-Upload-Leaderboard-BG-1-createPO.svg) no-repeat transparent;
    .processingSection {
      margin-top: 15px;
    }
    .orderButton.orderButton {
      margin-top: 155px;
    }
  }
  
  .orderButton.orderButton {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 12px 0 0;
    border-radius: 10px;
    text-transform: uppercase;
    transition: all 0.1s;
    opacity: unset;
    background-color: #fff;
    font-family: Syncopate;
    font-size: 18px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.3;
    letter-spacing: -0.72px;
    text-align: center;
    color: #191a20;
    &:disabled {
      cursor: not-allowed;
      color: rgba(255, 255, 255, 0.4);
      background-color: #222329;
    }
  }
  
  // Animation classes
  .fadeIn {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .slideUp {
    // animation: slideUp 0.5s ease-out;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
  
    to {
      opacity: 1;
    }
  }
  
  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
  
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  // Leaderboard Styles
  .leaderboard {
    width: 100%;
    margin-bottom: 28px;
  }
  
  .leaderboardTitle {
    font-family: Syncopate;
    font-size: 17px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.2;
    letter-spacing: 1.53px;
    text-align: left;
    color: #fff;
    padding-top: 16px;
    padding-left: 47px;
  }
  
  .scoreContainer {
    background: url(../../assets/images/Scroe-Board-BG-With-Lines.svg) no-repeat transparent;
    margin: 11px 16px 0px 16px;
    width: 250px;
    height: 130px;
  }
  
  .scoreRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    padding: 0px 12px;
  }
  .scoreRow1 {
    display: flex;
    .scoreLabel1 {
      display: flex;
      flex-direction: column;
      height: 50px;
      width: 58px;
      padding: 7px;
      align-items: center;
      justify-content: center;
      .place {
        font-family: Syncopate;
        font-size: 24px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: normal;
        text-align: left;
        color: #0f0f14;
        
        sup {
          font-family: Inter;
          font-size: 8.3px;
          font-weight: normal;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.73;
          letter-spacing: normal;
          text-align: left;
          color: #0f0f14;
        }
      }
      .placeLabel {
        font-family: Syncopate;
        font-size: 12px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: -0.96px;
        text-align: left;
        color: #0f0f14;
      }
    }
    .scoreValue {
      display: flex;
      justify-content: space-between;
      height: 50px;
      width: 192px;
      padding: 8px 12px 0px 4px;
    }
  }
  
  .scoreLabel {
    font-family: Syncopate;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.25;
    letter-spacing: -0.96px;
    text-align: left;
    color: #0f0f14;
    display: flex;
    align-items: center;
    
  }
  
  .score {
    font-family: Syncopate;
    font-size: 36px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: normal;
    text-align: right;
    color: #0f0f14;
  }


  .currentGameScore{
    margin-top: 0.1vh;
  }
  
  // Processing Section Styles
  .processingSection {
    width: 282px;
    height: 190px;
    padding: 16px 12px 15px;
    border-radius: 20px;
    background-color: #212227;
  }
  
  .processingSectionTitle {
    font-family: Syncopate;
    font-size: 17px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: 1.7px;
    text-align: center;
    color: #fff;
    margin-bottom: 16px;
  }
  
  .progressContainer {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .cancelUploadButton{
    font-family: Syncopate;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.25;
    letter-spacing: 0.48px;
    text-align: center;
    color: rgba(255, 255, 255, 0.4);
    width: 100%;
    padding-top: 10px;
    &:hover{
      color: #fff;
    }
    &:focus{
      color: #fff;
    }
  }
