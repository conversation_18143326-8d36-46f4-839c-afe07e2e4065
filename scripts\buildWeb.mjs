/**
 * Client Build Script
 * Usage: node scripts/buildClient.mjs <client-id> <environment>
 * Example: node scripts/buildClient.mjs bryzos demo
 */


import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const args = process.argv.slice(2);

let clientId, environment;

if (args.length >= 2) {
    console.log(`args: ${args}`);
    [clientId, environment] = args;
} else {
    // Fall back to environment variables (for npm script usage)
    console.log(`process.env: ${JSON.stringify(process.env)}`);
    clientId = process.env.CLIENT_ID;
    environment = process.env.NODE_ENV;
}

/**
 * Client Configuration Manager
 * This script handles loading and applying client-specific configurations
 * Each client has:
 * - client.config.json: metadata (name, version, icons, etc.)
 * - config.{environment}.json: environment-specific settings
 */


const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

/**
 * Get client metadata configuration
 * @param {string} clientId - The client identifier (e.g., 'bryzos', 'example-client')
 * @returns {object} Client metadata configuration object
 */
export function getClientConfig(clientId) {
  const clientConfigPath = path.join(rootDir, 'clients', clientId, 'client.config.json');
  
  if (!fs.existsSync(clientConfigPath)) {
    throw new Error(`Client configuration not found for: ${clientId}. Path: ${clientConfigPath}`);
  }
  
  const configContent = fs.readFileSync(clientConfigPath, 'utf-8');
  return JSON.parse(configContent);
}

/**
 * Get environment-specific configuration for a client
 * @param {string} clientId - The client identifier
 * @param {string} environment - The environment (development, demo, qa, staging, production)
 * @returns {object} Environment configuration object
 */
export function getEnvironmentConfig(clientId, environment) {
  const envConfigPath = path.join(rootDir, 'clients', clientId, `config.${environment}.json`);
  
  if (!fs.existsSync(envConfigPath)) {
    throw new Error(`Environment config not found for client '${clientId}' in environment '${environment}'. Path: ${envConfigPath}`);
  }
  
  const configContent = fs.readFileSync(envConfigPath, 'utf-8');
  return JSON.parse(configContent);
}

/**
 * Update package.json with client-specific information
 * @param {string} clientId - The client identifier
 */
export function updatePackageJson(clientId) {
  const clientConfig = getClientConfig(clientId);
  const packageJsonPath = path.join(rootDir, 'package.json');
  
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'));
  
  // Update with client-specific values
  packageJson.name = clientConfig.packageName;
  packageJson.productName = clientConfig.productName;
  packageJson.version = clientConfig.version;
  
  // Backup original package.json if not exists
  const backupPath = path.join(rootDir, 'package.json.backup');
  if (!fs.existsSync(backupPath)) {
    fs.copyFileSync(packageJsonPath, backupPath);
  }
  
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n', 'utf-8');
  console.log(`✓ Updated package.json for client: ${clientConfig.clientName}`);
}

/**
 * Copy environment files from client folder to root folder
 * @param {string} clientId - The client identifier
 * @param {string} environment - The environment (development, demo, qa, staging, production)
 */
export function copyEnvFiles(clientId, environment) {
  const clientDir = path.join(rootDir, 'clients', clientId);
  
  // List of possible env files to copy
  const envFiles = [
    `.env.${environment}`,  // Environment-specific file
    '.env',                 // General .env file
  ];
  
  let copiedCount = 0;
  
  envFiles.forEach(filename => {
    const sourcePath = path.join(clientDir, filename);
    const destPath = path.join(rootDir, filename);
    
    if (fs.existsSync(sourcePath)) {
      // Copy the env file (overwrites if exists)
      fs.copyFileSync(sourcePath, destPath);
      copiedCount++;
      console.log(`✓ Copied ${filename} to root folder`);
    }
  });
  
  if (copiedCount === 0) {
    console.log(`ℹ No .env files found in clients/${clientId}/`);
  }
}

/**
 * Copy all web asset files from client folder to src/assets/images folder
 * @param {string} clientId - The client identifier
 */
export function copyWebAssetFiles(clientId) {
  const clientWebAssetDir = path.join(rootDir, 'clients', clientId, 'web-asset');
  const srcAssetsImagesDir = path.join(rootDir, 'src', 'renderer', 'assets', 'images');
  
  if (!fs.existsSync(clientWebAssetDir)) {
    console.log(`ℹ No web-asset directory found in clients/${clientId}/`);
    return;
  }
  
  // Ensure src/assets/images directory exists
  if (!fs.existsSync(srcAssetsImagesDir)) {
    fs.mkdirSync(srcAssetsImagesDir, { recursive: true });
  }
  
  // Read all files in client web-asset directory
  const files = fs.readdirSync(clientWebAssetDir);
  let copiedCount = 0;
  
  files.forEach(filename => {
    const sourcePath = path.join(clientWebAssetDir, filename);
    const destPath = path.join(srcAssetsImagesDir, filename);
    
    // Check if it's a file (not a directory)
    if (fs.statSync(sourcePath).isFile()) {
      // Copy the file (overwrites if exists)
      fs.copyFileSync(sourcePath, destPath);
      copiedCount++;
    }
  });
  
  console.log(`✓ Copied ${copiedCount} web asset files to src/assets/images folder`);
}

/**
 * Generate forge config data for client
 * @param {string} clientId - The client identifier
 * @returns {object} Forge config overrides
 */
export function getForgeConfigOverrides(clientId) {
  const clientConfig = getClientConfig(clientId);
  
  return {
    name: clientConfig.appName,
    icon: `./public/${clientConfig.icon.base}`,
    squirrel: {
      loadingGif: `./public/${clientConfig.squirrel.loadingGif}`,
      authors: clientConfig.authors,
      description: clientConfig.description,
      iconUrl: clientConfig.squirrel.iconUrl
    },
    dmg: {
      icon: `./public/${clientConfig.icon.icns}`,
      background: `./public/${clientConfig.dmg.background}`,
      iconSize: clientConfig.dmg.iconSize
    }
  };
}

/**
 * Restore package.json from backup
 */
export function restorePackageJson() {
  const packageJsonPath = path.join(rootDir, 'package.json');
  const backupPath = path.join(rootDir, 'package.json.backup');
  
  if (fs.existsSync(backupPath)) {
    fs.copyFileSync(backupPath, packageJsonPath);
    console.log(`✓ Restored package.json from backup`);
  }
}

/**
 * Main function to setup client configuration
 * @param {string} clientId - The client identifier
 * @param {string} environment - The environment
 */
export function setupWebConfig(clientId, environment) {
  console.log(`\n🔧 Setting up configuration for client: ${clientId} (${environment})\n`);
  try {
    // Copy environment files
    copyEnvFiles(clientId, environment);

    //Copy web asset files
    copyWebAssetFiles(clientId);
    
    // Update package.json
    updatePackageJson(clientId);
    
    console.log(`\n✅ Client configuration setup complete!`);
    console.log(`   Next: Run configMoveElectron.mjs to generate src/main/config.js\n`);
  } catch (error) {
    console.error(`\n❌ Error setting up client config: ${error.message}\n`);
    process.exit(1);
  }
}


console.log(`clientId: ${clientId}, environment: ${environment}`);

// Setup client configuration
setupWebConfig(clientId, environment);

