.lineStatusContainer{
    position: relative;
    .lineStatusOverlay{
        svg {
            border-radius: 8px;
            border-style: solid;
            border-width: 0.4px;
            border-image-source: linear-gradient(298deg, #ffffff3d -22%, #1b1c21 75%);
            background-image: linear-gradient(to bottom, #131314, #131314), linear-gradient(298deg, #ffffff9e -105%, #1b1c21 60%);
            background-origin: border-box;
            background-clip: content-box, border-box;
            overflow: hidden;
        }
    }
    .lineStatusConfidence{
        position: absolute;
        top: 24px;
        left: 11px;
        width: 104px;
        height: 60px;
        opacity: 0.3;
        transition: opacity 0.3s ease;
        // border-radius: 5.1px;
        // background-color: #17171b;
    }
    .lineStatusConfidenceHovered{
        opacity: 1;
    }
    .lineStatusActionHovered.lineStatusActionHovered{
        opacity: 1;
    }
    .lineStatusAction{
        position: absolute;
        top: 90px;
        left: 11px;
        display: flex;
        flex-direction: column;
        gap: 5px;
        opacity: 0.3;
        transition: opacity 0.3s ease;
        .lineStatusActionButton{
            width: 104px;
            height: 26px;
            border-radius: 4px;
            font-family: Syncopate;
            font-size: 12px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: normal;
            letter-spacing: 0.48px;
            border-radius: 4px;
            color: #ffffff33;
            background-image: url('../../assets/images/BOM-Line-Status/buttonBG.svg');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }
        .skipButton{
            &:focus{
                color: #459fff;
            }
            &:hover{
                color: #459fff;
            }
        }
        .approveButton{
            &:focus{
                color: #43f776;
            }
            &:hover{
                color: #43f776;
            }
            &:disabled{
                color: #181818;
                opacity: 1;
            }
        }
        .pendingButton{
            &:focus{
                color: #ff9e20;
            }
            &:hover{
                color: #ff9e20;
            }
        }
        .deleteButton{
            &:focus{
                color: #fc3030;
            }
            &:hover{
                color: #fc3030;
            }
        }
    }
}