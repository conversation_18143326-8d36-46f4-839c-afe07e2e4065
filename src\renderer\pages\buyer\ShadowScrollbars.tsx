import React, { useRef, useCallback } from 'react';
import { Scrollbars, positionValues } from 'react-custom-scrollbars-2';

interface ShadowScrollbarsProps {
  style?: React.CSSProperties;
  children?: React.ReactNode;
}

const ShadowScrollbars: React.FC<ShadowScrollbarsProps> = ({
  style,
  children,
  ...props
}) => {
  const shadowTopRef = useRef<HTMLDivElement>(null);
  const shadowBottomRef = useRef<HTMLDivElement>(null);

  const handleUpdate = useCallback((values: positionValues) => {
    const { scrollTop, scrollHeight, clientHeight } = values;
    const shadowTop = shadowTopRef.current;
    const shadowBottom = shadowBottomRef.current;

    if (shadowTop && shadowBottom) {
      const shadowTopOpacity = Math.min(scrollTop, 20) / 20;
      const bottomScrollTop = scrollHeight - clientHeight;
      const shadowBottomOpacity =
        (bottomScrollTop - Math.max(scrollTop, bottomScrollTop - 20)) / 20;

      shadowTop.style.opacity = shadowTopOpacity.toString();
      shadowBottom.style.opacity = shadowBottomOpacity.toString();
    }
  }, []);

  const containerStyle: React.CSSProperties = {
    ...style,
    position: 'relative',
    height: '100%',
  };

  const shadowStyleBase: React.CSSProperties = {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 10,
    pointerEvents: 'none',
    opacity: 0,
  };

  const shadowTopStyle: React.CSSProperties = {
    ...shadowStyleBase,
    top: 0,
    background:
      'linear-gradient(to bottom, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0))',
  };

  const shadowBottomStyle: React.CSSProperties = {
    ...shadowStyleBase,
    bottom: 0,
    height:200,
    background:
      'linear-gradient(to top, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0))',
  };

  return (
    <div style={containerStyle}>
      <Scrollbars
        onUpdate={handleUpdate}
          renderTrackHorizontal={props => <div {...props} className="track-horizontal"/>}
        renderTrackVertical={props => <div {...props} className="track-vertical"/>}
        renderThumbHorizontal={props => <div {...props} className="thumb-horizontal"/>}
        renderThumbVertical={props => <div {...props} className="thumb-vertical"/>}
        autoHide={false} // Always show scrollbar
        style={{ height: '100%' }} // Full height scroll area
        {...props}
      >
        {children} 
      </Scrollbars>
     
    </div>
  );
};

export default ShadowScrollbars;
