import { useMutation } from '@tanstack/react-query';
import { axios } from '@bryzos/giss-ui-library';
import useDialogStore from 'src/renderer/component/DialogPopup/DialogStore';

interface SetupIntentResponse {
  data: {
    client_secret: string;
    setup_intent_id?: string;
    error_message?: {
      reason: string;
      email?: string[];
      message?: string;
    };
  };
}

/**
 * Hook for creating a Stripe setup intent for ACH payments
 */
const usePostCreateSetupIntent = () => {
  const { showCommonDialog, resetDialogStore } = useDialogStore();

  return useMutation<SetupIntentResponse, Error>(
    async () => {
      const response = await axios.post<SetupIntentResponse>(
        import.meta.env.VITE_API_SERVICE + '/subscription/ach/create-setup-intent'
      );

      // Check if the response contains an error message
      if (response.data?.data?.error_message) {
        const errorData = response.data.data.error_message;
          const errorMessage = errorData.message || 'Failed to create setup intent. Please try again.';
          showCommonDialog(null, errorMessage, null, resetDialogStore, [
            { name: 'OK', action: resetDialogStore }
          ]);
      }

      return response.data;
    }
  );
};

export default usePostCreateSetupIntent;
