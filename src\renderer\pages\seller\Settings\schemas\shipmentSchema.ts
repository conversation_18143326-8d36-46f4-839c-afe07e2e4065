import * as yup from 'yup';

export const shipmentSchema = yup.object().shape({
    stockingAddress: yup.object().shape({
        line1: yup.string().trim().required('Line 1 is required'),
        line2: yup.string().trim().optional(),
        city: yup.string().trim().required('City is required'),
        state: yup.string().required('State is required'),
        stateCode: yup.string(),
        zip: yup.string().trim().required('Zip is required')
      }).required('Company address is required'),
    orderFulfillmentStates: yup.array().of(yup.string()).default([]),
    orderClaimPreferences: yup.boolean().default(false),
});

export type ShipmentFormData = yup.InferType<typeof shipmentSchema>;
