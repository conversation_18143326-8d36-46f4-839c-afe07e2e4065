import React from 'react'
import styles from './StataZipDispute.module.scss'
import usePostDisputeOrder from 'src/renderer/hooks/usePostDisputeOrder';
import useDialogStore from 'src/renderer/component/DialogPopup/DialogStore';
import { commomKeys, useCreatePoStore, useGlobalStore, useOrderManagementStore } from '@bryzos/giss-ui-library';

const StateZipDispute = () => {

    const { mutateAsync: postDisputeOrder } = usePostDisputeOrder();
    const { showCommonDialog, resetDialogStore } = useDialogStore();
    const { setShowLoader, userData } = useGlobalStore();
    const selectedQuote = useCreatePoStore(state => state.selectedQuote);
    const lastCounterData = selectedQuote?.order_level_dispute?.deliver_to?.[selectedQuote?.order_level_dispute?.deliver_to?.length - 1];

    const handleSubmit = async (mode: string = "accept") => {
        try {
            setShowLoader(true);
            const payload = {
                "data": {
                    "po_number": selectedQuote?.seller_po_number,
                    "order_level": {
                        "deliver_to": {
                            "line1": lastCounterData?.line1,
                            "line2": lastCounterData?.line2 || null,
                            "city": lastCounterData?.city,
                            "state_id": lastCounterData?.state_id,
                            "zip": lastCounterData?.zip,
                            "counter_id": lastCounterData?.counter_id,
                            "action": mode
                        }
                    }
                }
            }
            const response = await postDisputeOrder(payload);
            if (
                typeof response?.data === "object" &&
                "error_message" in response.data
            ) {
                throw new Error(response.data.error_message);
            }
        } catch (error) {
            console.log("error @>>>>>>>", error);
            showCommonDialog(null, error?.message || commomKeys.errorContent, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
            setShowLoader(false);
        } finally {
        }
    };

    const handleAcceptCancellation = () => {
        handleSubmit("accept");
    }
    const handleRejectCancellation = () => {
        handleSubmit("cancel_order");
    }
    return (
        <div className={styles.buyerCancellationContainer}>
            <>
                <h2>Buyer Changed Shipping Address – Cancelation Option Available</h2>
                <p className={styles.buyerCancellationDescription}>The buyer has updated their shipping address (State or ZIP code), which may affect the order's pricing, taxes, or shipping terms.</p>
            </>
            <div className={styles.buyerCancellationButtons}>
                <button onClick={handleAcceptCancellation}>Accept Change</button>
                <button onClick={handleRejectCancellation}>Cancel Order</button>
            </div>
        </div>
    )
}

export default StateZipDispute
