import React from "react";
import {
  Badge,
  Box,
  Button,
  ClickAwayListener,
  Divider,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Popover,
  Stack,
  Typography,
  CircularProgress,
} from "@mui/material";
import NotificationsNoneIcon from "@mui/icons-material/NotificationsNone";
import { ReactComponent as NotificationIcon } from "../../assets/images/notification-icon.svg";
import { ReactComponent as NotificationIconHover } from '../../assets/images/Header-Icons/notification-btn-hover.svg';
import { ReactComponent as NotificationIconActive } from '../../assets/images/Header-Icons/notification-btn-active.svg';
import CloseIcon from "@mui/icons-material/Close";
import { NotificationItem, useNotificationStore } from "./NotificationsStore";
import { formatWhen } from "src/renderer/helper";

export default function NotificationBell() {
  const {
    anchorEl,
    open,
    loading,
    notifications,
    setAnchor,
    toggleOpen,
    load,
    markAsRead,
    clearAll,
    removeOne,
  } = useNotificationStore();

  const unreadCount = notifications.filter((n) => !n.is_read_in_app).length;

  const handleBellClick = async (e: React.MouseEvent<HTMLElement>) => {
    setAnchor(e.currentTarget);
    toggleOpen(true);
    if (notifications.length === 0 && !loading) {
      await load();
    }
  };

  const handleClose = () => {
    toggleOpen(false);
    setAnchor(null);
  };

  const handleItemClick = (n: NotificationItem) => {
    // mark read then navigate if needed
    useNotificationStore.getState().markAsRead(n.id);
    handleClose();
  };

  // --- order: unread → Clear all button → rest (read) ---
  const sorted = React.useMemo(
    () =>
      [...notifications].sort(
        (a, b) =>
          new Date(b.created_date.replace(" ", "T")).getTime() -
          new Date(a.created_date.replace(" ", "T")).getTime()
      ),
    [notifications]
  );
  const unread = sorted.filter((n) => !n.is_read_in_app);
  const read = sorted.filter((n) => n.is_read_in_app);

  const renderRow = (n: NotificationItem) => (
    <ListItem
      key={n.id}
      disablePadding
      secondaryAction={
        <IconButton edge="end" size="small" onClick={() => removeOne(n.id)}>
          <CloseIcon fontSize="small" />
        </IconButton>
      }
      sx={{ "&:not(:last-child)": { borderBottom: 1, borderColor: "divider" } }}
    >
      <ListItemButton onClick={() => handleItemClick(n)} sx={{ alignItems: "flex-start", py: 1.25, gap: 1 }}>
        <Box
          sx={{
            mt: 0.6,
            width: 8,
            height: 8,
            borderRadius: "50%",
            bgcolor: n.is_read_in_app ? "transparent" : "primary.main",
            border: (theme) => (n.is_read_in_app ? `1px solid ${theme.palette.divider}` : "none"),
            flexShrink: 0,
          }}
        />
        <ListItemText
          primary={
            <Stack direction="row" justifyContent="space-between" alignItems="baseline">
              <Typography variant="subtitle2" noWrap>
                {n.notification_title}
              </Typography>
              <Typography variant="caption" color="text.secondary" sx={{ pl: 1, flexShrink: 0 }}>
                {formatWhen(n.created_date)}
              </Typography>
            </Stack>
          }
          secondary={
            <Typography variant="body2" color="text.secondary" sx={{ mt: 0.25 }}>
              {n.message}
            </Typography>
          }
        />
      </ListItemButton>
    </ListItem>
  );

  return (
    <>
      {/* <IconButton onClick={handleBellClick} aria-label="notifications">
        <Badge color="primary" badgeContent={unreadCount}>
          <NotificationsNoneIcon />
        </Badge>
      </IconButton> */}

    <div className={styles.iconDiv}>
        <NotificationsNoneIcon className={styles.iconDivImg1} />
        <NotificationIconHover className={styles.iconDivImg2} />
        <NotificationIconActive className={styles.iconDivImg3} />
    </div>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        transformOrigin={{ vertical: "top", horizontal: "right" }}
        PaperProps={{ sx: { width: 360, borderRadius: 2, overflow: "visible" } }}
      >
        <ClickAwayListener onClickAway={handleClose}>
          <Box sx={{ p: 1.5 }}>
            <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ px: 0.5, pb: 1 }}>
              <Typography variant="subtitle1">Notifications</Typography>
            </Stack>

            <Divider />

            {loading ? (
              <Stack alignItems="center" justifyContent="center" sx={{ py: 4 }}>
                <CircularProgress size={24} />
              </Stack>
            ) : sorted.length === 0 ? (
              <Box sx={{ py: 3, textAlign: "center" }}>
                <Typography variant="body2" color="text.secondary">
                  You’re all caught up.
                </Typography>
              </Box>
            ) : (
              <List sx={{ maxHeight: 380, overflowY: "auto", px: 0.5 }}>
                {/* Unread first */}
                {unread.map(renderRow)}

                {/* Centered Clear All */}
                {sorted.length > 0 && (
                  <>
                    {unread.length > 0 && <Divider sx={{ my: 1 }} />}
                    <Stack alignItems="center" sx={{ py: 0.5 }}>
                      <Button size="small" color="error" onClick={clearAll} disabled={sorted.length === 0}>
                        Clear all
                      </Button>
                    </Stack>
                    <Divider sx={{ my: 1 }} />
                  </>
                )}

                {/* Then the rest (read) */}
                {read.map(renderRow)}
              </List>
            )}
          </Box>
        </ClickAwayListener>
      </Popover>
    </>
  );
}
