import { useMutation, useQueryClient } from '@tanstack/react-query';
import { axios } from '@bryzos/giss-ui-library';
import useDialogStore from 'src/renderer/component/DialogPopup/DialogStore';
import { reactQueryKeys } from '../common';

interface StorePaymentMethodParams {
  data: {
    user_count: number;
    payment_method: string;
    payment_details: {
      account_name: string;
      account_type: string;
      bank_name: string;
    };
    payment_method_id: string;
    setup_intent_id: string;
  };
}

interface StorePaymentMethodResponse {
  data: {
    customer_id?: string;
    status?: string;
    message?: string;
    error_message?: {
      reason: string;
      email?: string[];
      message?: string;
    };
  };
}

/**
 * Hook for storing a payment method for ACH payments
 */
const usePostStorePaymentMethod = () => {
  const queryClient = useQueryClient();
  const { showCommonDialog, resetDialogStore } = useDialogStore();

  return useMutation<StorePaymentMethodResponse, Error, StorePaymentMethodParams>(
    async (params) => {
      const response = await axios.post<StorePaymentMethodResponse>(
        import.meta.env.VITE_API_SERVICE + '/subscription/ach/store-payment-method',
        params
      );

      // Check if the response contains an error message
      if (response.data?.data?.error_message) {
        const errorData = response.data.data.error_message;
          // For general errors, show a dialog
          const errorMessage = errorData || 'Failed to store payment method. Please try again.';
          showCommonDialog(null, errorMessage, null, resetDialogStore, [
            { name: 'OK', action: resetDialogStore }
          ]);
      }
      // Invalidate user subscription data to trigger a refetch
      queryClient.invalidateQueries([reactQueryKeys.getUserSubscription]);
      return response.data;
    }
  );
};

export default usePostStorePaymentMethod;
