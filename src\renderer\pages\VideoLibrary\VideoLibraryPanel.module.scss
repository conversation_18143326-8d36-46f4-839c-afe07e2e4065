/* VideoLibraryPanel.module.scss */
:global(:root) {
  --bg-panel: #15161a;
  --bg-section: #1c1d22;
  --bg-row-hover: #23252c;
  --text: #e7e7ea;
  --text-dim: #a7a8ad;
  --accent: #a18aff; /* purple-ish per screenshot vibe */
  --divider: #2a2c34;
  --btn: #2a2c34;
}

.panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  border-image-source: linear-gradient(268deg, #fff -221%, #1b1b21 53%);
  border-image-slice: 1;
  background-color: #191a20;
  border-style: solid;
  border-width: 1px;
  padding-right: 5px;
}

.toolbar {
  padding: 12px 12px 8px 12px;
}

.createBtn {
  height: 36px;
  border-radius: 9999px;
  border: none;
  cursor: pointer;
  padding: 0 4px 0 16px;
  display: flex;
  align-items: center;
  transition: background-color 0.2s;
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: left;
  background-color: #fff;
  color: #393e47;
  opacity: 0.5;
  &:hover {
    opacity: 1;
}

.createButtonIcon {
    margin-left: 8px;
    width: 26px;
    height: 26px;
}
}

.libraryLabel {
  padding: 6px 14px 10px 14px;
  font-family: Syncopate;
  font-size: 16px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: -0.64px;
  text-align: left;
  color: #c67dff;
  text-transform: uppercase;
}

.sectionsWrap {
  overflow: auto;
  padding: 0px 5px 0px 16px;
  &::-webkit-scrollbar { 
    width: 6px; 
  }
}

.section { margin-bottom: 10px; }
.sectionHeader {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #303136;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  padding: 14px 10px 12px 12px;
}
.sectionTitle {
  font-family: Syncopate;
  font-size: 12px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.2;
  letter-spacing: 0.84px;
  text-align: left;
  color: #fff;
  text-transform: uppercase;
}

.sectionList {
  background-color: #222329;
}

.empty {
  color: var(--text-dim);
  font-size: 12px;
  padding: 8px 6px;
}

.itemRow {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px;
  margin-bottom: 2px;
  height: 121px;
  cursor: pointer;
  &:hover {
    background-color: #434449;
  }
}

.itemHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}
.itemTitleLine {
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: 0.98px;
  text-align: left;
  color: #fff;
}
.duration {
  font-family: Inter;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: 0.84px;
  text-align: right;
  color: #fff;

}

.itemBody {
  display: grid;
  grid-template-columns: 48px 1fr auto;
  gap: 10px;
  align-items: flex-start;
}
.thumb {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  overflow: hidden;
  background: #0f1013;
  border: 1px solid #2b2d36;
}
.thumb img { width: 100%; height: 100%; object-fit: cover; display: block; }
.thumbEmpty { background: #0d0e11; }

.metaText { min-width: 0; }
.primaryText {
  font-family: Inter;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: 0.84px;
  text-align: left;
  color: #9b9eac;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.muted { color: var(--text-dim); font-style: italic; }

.iconBtn {
  border: none;
  background: transparent;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 6px;
  color: var(--text);
}
.iconBtn:hover { background: #2a2c34; }
.iconBtnDisabled { opacity: 0.5; pointer-events: none; }

.icon { display: inline-block; }
.chevron { transition: transform 0.18s ease; color: var(--text-dim); }
.open { transform: rotate(180deg); }
.playIcon { }

.selected {
  background-color: #434449;
  .iconBtn {
    &.iconBtnDisabled {
      cursor: not-allowed;
    }
    svg {
      path {
        fill: #fff;
      }
    }
  }
  .primaryText{
    color: #fff;
  }
}

/* End of VideoLibraryPanel.module.scss */