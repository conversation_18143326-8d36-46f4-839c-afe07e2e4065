<svg width="18" height="26" viewBox="0 0 18 26" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="0.360214" y="0.36" width="17.2796" height="25.28" rx="3.64" fill="url(#paint0_linear_25_290)" stroke="url(#paint1_linear_25_290)" stroke-width="0.72"/>
<rect x="-1.52588e-05" y="12" width="17.9996" height="1" fill="#FFC44F"/>
<g opacity="0.45" filter="url(#filter0_i_25_290)">
<rect y="4.68701" width="17.9996" height="1" fill="#323232"/>
</g>
<g opacity="0.45" filter="url(#filter1_i_25_290)">
<rect x="-3.05176e-05" y="6.56201" width="17.9996" height="1" fill="#323232"/>
</g>
<g opacity="0.45" filter="url(#filter2_i_25_290)">
<rect x="-3.05176e-05" y="8.43701" width="17.9996" height="1" fill="#323232"/>
</g>
<g opacity="0.45" filter="url(#filter3_i_25_290)">
<rect x="-3.05176e-05" y="16.8745" width="17.9996" height="1" fill="#323232"/>
</g>
<g opacity="0.45" filter="url(#filter4_i_25_290)">
<rect x="-3.05176e-05" y="18.749" width="17.9996" height="1" fill="#323232"/>
</g>
<g opacity="0.45" filter="url(#filter5_i_25_290)">
<rect x="-3.05176e-05" y="20.624" width="17.9996" height="1" fill="#323232"/>
</g>
<defs>
<filter id="filter0_i_25_290" x="0" y="4.68701" width="17.9996" height="1.24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.24"/>
<feGaussianBlur stdDeviation="0.24"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_25_290"/>
</filter>
<filter id="filter1_i_25_290" x="-3.05176e-05" y="6.56201" width="17.9996" height="1.24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.24"/>
<feGaussianBlur stdDeviation="0.24"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_25_290"/>
</filter>
<filter id="filter2_i_25_290" x="-3.05176e-05" y="8.43701" width="17.9996" height="1.24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.24"/>
<feGaussianBlur stdDeviation="0.24"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_25_290"/>
</filter>
<filter id="filter3_i_25_290" x="-3.05176e-05" y="16.8745" width="17.9996" height="1.24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.24"/>
<feGaussianBlur stdDeviation="0.24"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_25_290"/>
</filter>
<filter id="filter4_i_25_290" x="-3.05176e-05" y="18.749" width="17.9996" height="1.24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.24"/>
<feGaussianBlur stdDeviation="0.24"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_25_290"/>
</filter>
<filter id="filter5_i_25_290" x="-3.05176e-05" y="20.624" width="17.9996" height="1.24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.24"/>
<feGaussianBlur stdDeviation="0.24"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_25_290"/>
</filter>
<linearGradient id="paint0_linear_25_290" x1="9.00002" y1="0" x2="9.00002" y2="26" gradientUnits="userSpaceOnUse">
<stop stop-color="#1B1B1B"/>
<stop offset="1" stop-color="#4B4B4B"/>
</linearGradient>
<linearGradient id="paint1_linear_25_290" x1="9.00002" y1="0" x2="9.00002" y2="26" gradientUnits="userSpaceOnUse">
<stop stop-color="#B2B2B2"/>
<stop offset="0.140625" stop-color="#0E0E0E" stop-opacity="0"/>
<stop offset="0.484375" stop-color="#0E0E0E" stop-opacity="0"/>
<stop offset="0.864583" stop-color="#0E0E0E" stop-opacity="0"/>
<stop offset="1" stop-color="#0E0E0E"/>
</linearGradient>
</defs>
</svg>
