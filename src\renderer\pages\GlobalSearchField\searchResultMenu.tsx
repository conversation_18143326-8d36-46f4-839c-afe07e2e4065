import React, { useState, useRef, useEffect, useMemo } from "react";
import { Popper, Paper, ClickAwayListener, Divider } from "@mui/material";
import styles from "./searchBox.module.scss";

export type MenuItem = {
  id: string;
  label: string;
  date?: string;
  // optional metadata used by the parent for routing:
  __category?: "pricing" | "quote" | "purchase" | "order" | "preview_order" | "claim_order";
  __indexWithinCategory?: number;
  __raw?: any;
};

const categoryMap = {
  pricing: "Instant Price Search",
  quote: "Quoting",
  purchase: "Purchasing",
  order: "Order Management",
  preview_order: "Preview Orders",
  claim_order: "Claim Orders",
};

// const highlight = (text: string, term?: string) => {
//   if (!term) return text;
//   try {
//     const re = new RegExp(`(${term.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")})`, "ig");
//     return text.split(re).map((part, i) =>
//       re.test(part) ? <mark key={i}>{part}</mark> : <React.Fragment key={i}>{part}</React.Fragment>
//     );
//   } catch {
//     return text;
//   }
// };

const Header: React.FC<{ label: string }> = ({ label }) => (
  <div className={styles.searchResultGroupHeader}>{label}</div>
);

const SearchResultMenu = ({
  anchorEl,
  open,
  onClose,
  items,
  onItemClick,
  isLoading = false,
  showLoadingBackground = false,
  showCategoryHeaders = true,
  searchTerm = "",
}: {
  anchorEl: HTMLElement | null;
  open: boolean;
  onClose: () => void;
  items: MenuItem[];
  onItemClick: (item: MenuItem) => void;
  isLoading?: boolean;
  showLoadingBackground?: boolean;
  showCategoryHeaders?: boolean;
  searchTerm?: string;
}) => {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setSelectedIndex(0);
    if (items.length === 0) return;
    items.forEach((item, index) => {
      let date = item?.date.split("-");
      if (date[0].length === 4) {
        date = [date[1], date[2], date[0]];
      }
      item.date = date.join("-");
    });
  }, [items]);

  // Auto-scroll to keep selected item in view
  useEffect(() => {
    if (selectedIndex >= 0 && containerRef.current) {
      const container = containerRef.current;
      const selectedItem = container.querySelector<HTMLElement>(
        `[data-row-index="${selectedIndex}"]`
      );
      if (!selectedItem) return;

      const containerHeight = container.clientHeight;
      const itemTop = selectedItem.offsetTop;
      const itemHeight = selectedItem.offsetHeight;
      const scrollTop = container.scrollTop;

      if (itemTop < scrollTop) {
        container.scrollTop = itemTop;
      } else if (itemTop + itemHeight > scrollTop + containerHeight) {
        container.scrollTop = itemTop + itemHeight - containerHeight;
      }
    }
  }, [selectedIndex]);

  // Keyboard nav
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!open) return;

      switch (event.key) {
        case "ArrowUp":
          event.preventDefault();
          setSelectedIndex((prev) => (prev > 0 ? prev - 1 : Math.max(items.length - 1, 0)));
          break;
        case "ArrowDown":
          event.preventDefault();
          setSelectedIndex((prev) => (prev < items.length - 1 ? prev + 1 : 0));
          break;
        case "Enter":
          event.preventDefault();
          if (selectedIndex >= 0 && selectedIndex < items.length) {
            onItemClick(items[selectedIndex]);
          }
          break;
        case "Escape":
          event.preventDefault();
          onClose();
          break;
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [open, selectedIndex, items, onItemClick, onClose]);

  // Group by category if requested and categories exist
  const grouped = useMemo(() => {
    if (!showCategoryHeaders) return { flat: items, groups: null as null | any };
    const hasCategory = items.some((i) => i.__category);
    if (!hasCategory) return { flat: items, groups: null };

    const order: Array<NonNullable<MenuItem["__category"]>> = [
        "preview_order",
      "claim_order",
      "pricing",
      "quote",
      "purchase",
      "order",
    ];
    const buckets: Record<string, MenuItem[]> = {};
    for (const item of items) {
      const key = item.__category || "Other";
      (buckets[key] ||= []).push(item);
    }

    // Build a flat list with header rows (not focusable/clickable)
    const flat: MenuItem[] = [];
    const indexMap: number[] = []; // maps visible row -> item index in items
    const sections: Array<{ header: string; start: number; end: number }> = [];

    let row = 0;
    for (const key of order) {
      const arr = buckets[key];
      if (!arr || arr.length === 0) continue;
      sections.push({ header: key, start: row, end: row });
      // we’ll render a header visually; not inserting fake MenuItem in data model
      // push the items and remember their row indices
      for (const it of arr) {
        indexMap.push(items.indexOf(it));
        flat.push(it);
        row++;
      }
    }

    return { flat, groups: { sections, indexMap, buckets } };
  }, [items, showCategoryHeaders]);

  return (
    <Popper
      open={open}
      anchorEl={anchorEl}
      keepMounted
      placement="bottom-start"
      style={{ zIndex: 1300 }}
      modifiers={[{ name: "preventOverflow", options: { boundary: "viewport" } }]}
      disablePortal={false}
    >
      <ClickAwayListener onClickAway={onClose}>
        <Paper
          ref={containerRef}
          className={styles.searchResultMenuPaper}
          elevation={3}
        >
          {/* Loading overlay layer */}
          <div
            className={`${styles.loadingOverlay} ${(isLoading || showLoadingBackground) ? styles.loadingOverlayVisible : styles.loadingOverlayHidden
              }`}
            aria-hidden={!(isLoading || showLoadingBackground)}
          />

          {/* Content layer (always mounted) */}
          <div
            className={`${styles.contentLayer} ${(isLoading || showLoadingBackground) ? styles.contentLayerHidden : styles.contentLayerVisible
              }`}
          >
            {items.length === 0 ? (
              <div className={styles.searchResultEmpty}>
                No result found for text {searchTerm}
              </div>
            ) : grouped.groups ? (
              <>
                {(["preview_order", "claim_order", "pricing", "quote", "purchase", "order", ] as const).map((cat) => {
                  const arr = grouped.groups!.buckets[cat];
                  if (!arr || arr.length === 0) return null;
                  return (
                    <div key={cat}>
                      <Header label={categoryMap[cat]} />
                      {arr.map((item: MenuItem) => {
                        const globalIndex = items.indexOf(item);
                        const rowIndex = grouped.groups!.indexMap.indexOf(globalIndex);
                        return (
                          <div
                            key={item.id}
                            data-row-index={rowIndex}
                            className={`${styles.searchResultMenuItem} ${selectedIndex === rowIndex ? styles.selected : ""
                              }`}
                            onClick={() => onItemClick(item)}
                            role="button"
                            tabIndex={-1}
                          >
                            <span className={styles.searchResultMenuItemLabel}>
                              {item.label}
                            </span>
                            {item.date && (
                              <span className={styles.searchResultMenuItemDateTime}>
                                {item.date}
                              </span>
                            )}
                          </div>
                        );
                      })}
                      <Divider className={styles.searchResultDivider} />
                    </div>
                  );
                })}
              </>
            ) : (
              items.map((item, index) => (
                <div
                  className={`${styles.searchResultMenuItem} ${selectedIndex === index ? styles.selected : ""
                    }`}
                  key={item.id}
                  data-row-index={index}
                  onClick={() => onItemClick(item)}
                  role="button"
                  tabIndex={-1}
                >
                  <span className={styles.searchResultMenuItemLabel}>
                    {item.label}
                  </span>
                  {item.date && (
                    <span className={styles.searchResultMenuItemDateTime}>{item.date}</span>
                  )}
                </div>
              ))
            )}
          </div>
        </Paper>

      </ClickAwayListener>
    </Popper>
  );
};

export default SearchResultMenu;
