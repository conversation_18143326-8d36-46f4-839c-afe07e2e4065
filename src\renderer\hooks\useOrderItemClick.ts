import { useCallback } from 'react';
import { useCreatePoStore, useGlobalStore, useOrderManagementStore, useChatWithVendorStore, userRole } from '@bryzos/giss-ui-library';
import { handleOrderManagementNavigation, updateOrderManagementData } from 'src/renderer/helper';
import { useLocation } from 'react-router-dom';
import useGetOrderLines from './useGetOrderLines';
import { routes } from '../common';
import { useRightWindowStore } from '../pages/RightWindow/RightWindowStore';

interface UseOrderItemClickProps {
  isSearchMode?: boolean;
  setSelectedSavedSearchIdList?: any;
}

export const useOrderItemClick = ({ isSearchMode = false, setSelectedSavedSearchIdList }: UseOrderItemClickProps) => {
  const location = useLocation();
  const setShowLoader = useGlobalStore(state => state.setShowLoader);
  const userData = useGlobalStore(state => state.userData);
  const selectedQuote = useCreatePoStore(state => state.selectedQuote);
  const setIsEditingPo = useOrderManagementStore(state => state.setIsEditingPo);
  const setSelectedCancelLines = useOrderManagementStore(state => state.setSelectedCancelLines);
  const setOrderManageMentInitialData = useOrderManagementStore(state => state.setOrderManageMentInitialData);
  const isEditingPo = useOrderManagementStore(state => state.isEditingPo);
  const setSelectedQuote = useCreatePoStore(state => state.setSelectedQuote);
  const setCreatePoData = useCreatePoStore(state => state.setCreatePoData);
  const setIsCreatePoDirty = useCreatePoStore(state => state.setIsCreatePoDirty);
  const setMessages = useChatWithVendorStore(state => state.setMessages);
  const setPoNumber = useChatWithVendorStore(state => state.setPoNumber);
  const { mutateAsync: getOrderLines } = useGetOrderLines();

  const isSeller = userData?.data?.type === userRole.sellerUser;

  const handleLoadQuoteData = useCallback(async (item: any) => {
    setIsEditingPo(false);
    setSelectedCancelLines([]);
    setSelectedQuote(null);
    setMessages([]);
    setPoNumber('');
    setShowLoader(true);
    setIsCreatePoDirty(false);
    
    try {
      const orderData = await getOrderLines(item.id);
      updateOrderManagementData(item, orderData?.data);
    } catch (error) {
      console.error('Error loading quote data:', error);
      setShowLoader(false);
    }
  }, [
    setIsEditingPo,
    setSelectedCancelLines,
    setSelectedQuote,
    setMessages,
    setPoNumber,
    setShowLoader,
    setIsCreatePoDirty,
    getOrderLines
  ]);

  const handleItemClick = useCallback(async (item: any, index?: number, event?: React.MouseEvent) => {
    try {
     if (setSelectedSavedSearchIdList) {
      setSelectedSavedSearchIdList([]);
     }
      // Prevent clicking on the same item
      if (selectedQuote?.id === item.id) {
        return;
      }

      const { props } = useRightWindowStore.getState();

      const repliesCount = (() => {
        if (!props?.finalDisputePayload) return 0;
        
        let count = 0;
        try {
            if (props.finalDisputePayload?.order_level && typeof props.finalDisputePayload?.order_level === 'object') {
                count += Object.keys(props.finalDisputePayload?.order_level).length;
            }
            if (props.finalDisputePayload?.order_line_level && Array.isArray(props.finalDisputePayload?.order_line_level)) {
                count += props.finalDisputePayload?.order_line_level.length;
            }
        } catch (error) {
            console.warn('Error calculating replies count:', error);
            return 0;
        }
        return count;
    })();

    const hasPendingReplies = repliesCount > 0;
    const isClaimOrderPage = location.pathname === routes.orderManagementPage || 
                            location.pathname === routes.previewOrderPage || 
                            location.pathname === routes.deleteOrderPage;

      // Handle navigation logic
      if ((isEditingPo && !selectedQuote?.seller_company_name && !isSeller) || (hasPendingReplies && isClaimOrderPage)) {
        handleOrderManagementNavigation(null, location.pathname, handleLoadQuoteData, [item]);
      } else {
        await handleLoadQuoteData(item);
      }

      if (isSearchMode) {
        setShowLoader(false);
      }
    } catch (error) {
      setShowLoader(false);
      console.error('Error in handleItemClick:', error);
    }
  }, [
    selectedQuote?.id,
    selectedQuote?.seller_company_name,
    isEditingPo,
    isSeller,
    location.pathname,
    handleLoadQuoteData,
    isSearchMode,
    setShowLoader
  ]);

  return {
    handleItemClick,
    handleLoadQuoteData
  };
};

export default useOrderItemClick;