import { useForm, UseFormProps, UseFormReturn } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

export function useGenericForm(
    schema: yup.ObjectSchema<any>,
    options?: UseFormProps
): {
    control: UseFormReturn['control'];
    getValues: UseFormReturn['getValues'];
    setValue: UseFormReturn['setValue'];
    setError: UseFormReturn['setError'];
    watch: UseFormReturn['watch'];
    clearErrors: UseFormReturn['clearErrors'];
    reset: UseFormReturn['reset'];
    trigger: UseFormReturn['trigger'];
    register: UseFormReturn['register'];
    handleSubmit: UseFormReturn['handleSubmit'];
    errors: UseFormReturn['formState']['errors'];
    isDirty: UseFormReturn['formState']['isDirty'];
    isValid: UseFormReturn['formState']['isValid'];
    form: UseFormReturn;
} {
    const form = useForm({
        resolver: yupResolver(schema),
        ...options,
    });

    const {
        control,
        register,
        handleSubmit,
        getValues,
        setValue,
        setError,
        watch,
        clearErrors,
        reset,
        trigger,
        formState: { errors, isDirty, isValid }
    } = form;

    return {
        control,
        register,
        handleSubmit,
        getValues,
        setValue,
        setError,
        watch,
        clearErrors,
        reset,
        trigger,
        errors,
        isDirty,
        isValid,
        form, // in case you need access to reset, setValue, etc.
    };
}
