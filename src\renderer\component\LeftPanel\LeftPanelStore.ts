// @ts-nocheck
import { localStorageKeys } from 'src/renderer/common';
import { getLocal, setLocal } from 'src/renderer/helper';
import { create } from 'zustand';

interface MenuState {
  openLeftPanel: boolean;
  closeWithoutAnimation: boolean;
  leftPanelData: any;
  displayLeftPanel: boolean;
  clickedCreateNewButton: any;
  clickThisId: string;
  isDisableLeftPanel: boolean;
  setClickThisId: (clickThisId: string) => void;
  setCloseWithoutAnimation: (closeWithoutAnimation: boolean) => void;
  resetLeftPanelStore: () => void;
  setOpenLeftPanel: (openLeftPanel: boolean) => void;
  setLeftPanelData: (leftPanelData: any) => void; 
  setDisplayLeftPanel: (displayLeftPanel: boolean) => void;
  setClickedCreateNewButton: (clickedCreateNewButton: any) => void;
  setIsDisableLeftPanel: (isDisableLeftPanel: boolean) => void;
}

const commonStore = {
    openLeftPanel:false,
    closeWithoutAnimation:false,
    leftPanelData: null,
    displayLeftPanel: false,
    leftPanelViewPoHistoryData: null,
    clickedCreateNewButton: null,
    clickThisId: null,
    isDisableLeftPanel: false,
}
  
  
  export const useLeftPanelStore = create<MenuState>((set, get) => ({
    ...commonStore,
    setClickThisId: (clickThisId: string) => set({ clickThisId }),
    setOpenLeftPanel: (openLeftPanel: boolean) => set({ openLeftPanel }),
    setCloseWithoutAnimation: (closeWithoutAnimation: boolean) => set({ closeWithoutAnimation }),
    setLeftPanelData: (leftPanelData: any) => set({ leftPanelData }),
    setDisplayLeftPanel: (displayLeftPanel: boolean) => set({ displayLeftPanel }),
    setLeftPanelViewPoHistoryData: (leftPanelViewPoHistoryData: any) => set({ leftPanelViewPoHistoryData }),
    setClickedCreateNewButton: (clickedCreateNewButton: any) => set({ clickedCreateNewButton }),
    setIsDisableLeftPanel: (isDisableLeftPanel: boolean) => set({ isDisableLeftPanel }),
    resetLeftPanelStore: () => set(state => ({
      ...commonStore
    })),
  }));
    