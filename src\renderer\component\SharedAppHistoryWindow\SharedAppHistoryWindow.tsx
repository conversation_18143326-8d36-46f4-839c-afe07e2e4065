// @ts-nocheck
import React, { useState, useEffect, useMemo } from 'react';
import styles from './SharedAppHistoryWindow.module.scss';
import clsx from 'clsx';
import { ReactComponent as SearchIconSvg } from '../../assets/images/Search.svg';
import { useRightWindowStore } from 'src/renderer/pages/RightWindow/RightWindowStore';
import { ReactComponent as BackIcon } from '../../assets/images/Create-Account/arrow-left.svg';
import useGetSharedAppHistory from '../../hooks/useGetSharedAppHistory';
import Loader from 'src/renderer/Loader/Loader';
import { Tooltip, Fade } from '@mui/material';
import { shareEmailTypes } from 'src/renderer/common';
import ShareEmailWindow from '../ShareEmailWindow/ShareEmailWindow';

interface HistoryItem {
  shared_app_date_time: string;
  id: string;
  to_email: string;
  email_content?: string;
}

const SharedAppHistoryWindow: React.FC = () => {
  const { setIsSharedAppHistory, setLoadComponent, setShareEmailWindowProps, setShareEmailType } = useRightWindowStore();
  const [searchValue, setSearchValue] = useState<string>('');
  const [selectedItem, setSelectedItem] = useState<HistoryItem | null>(null);
  const [historyItems, setHistoryItems] = useState<HistoryItem[]>([]);
  
  // Use the API hook
  const { 
    data: sharedAppHistoryData, 
    isLoading, 
    error,
    refetch: refetchSharedAppHistory
  } = useGetSharedAppHistory();

  useEffect(() => {
    setIsSharedAppHistory(true);

    return () => {
      setIsSharedAppHistory(false);
    }
  }, []);

  // Update historyItems whenever data changes
  useEffect(() => {
    if (sharedAppHistoryData && Array.isArray(sharedAppHistoryData)) {
      setHistoryItems(sharedAppHistoryData);
    } else {
      setHistoryItems([]);
    }
  }, [sharedAppHistoryData, isLoading]);

  const filteredItems = useMemo(() => {
    if (!searchValue) return historyItems;
    
    const lowercasedSearch = searchValue.toLowerCase();
    return historyItems.filter(item => {
      const toEmail = item.to_email?.toLowerCase() || '';
      const dateTime = item.shared_app_date_time?.toLowerCase() || '';

      return toEmail.includes(lowercasedSearch) ||
             dateTime.includes(lowercasedSearch);
    });
  }, [searchValue, historyItems]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };

  const handleItemClick = (item: HistoryItem) => {
    setSelectedItem(item);
  };

  const handleBackClick = () => {
    setSelectedItem(null);
  };

  const handleBackToAppShare = () => {
    setIsSharedAppHistory(false);
    setShareEmailType(shareEmailTypes.inviteUser);
    setShareEmailWindowProps({isSharePrice: false});
    setLoadComponent(<ShareEmailWindow />)
  };

  const renderEmailContent = (content: string) => {
    return (
      <div 
        className={styles.emailContent}
        dangerouslySetInnerHTML={{ __html: content.replace(/\n/g, '<br>') }}
      />
    );
  };

  const renderHistoryItem = (item: HistoryItem) => {
    return (
      <div
        key={item.id}
        className={clsx(styles.historyItem)}
        onClick={() => handleItemClick(item)}
      >
        <div className={styles.historyItemTitle}>
          To: <span>{item.to_email}</span>
        </div>
        <div className={styles.historyItemDetails}>
          <div className={styles.detailsRow}>
            <span>{item.shared_app_date_time}</span>
          </div>
        </div>
      </div>
    );
  };  

  const renderHistoryList = () => (
    <div className={styles.historyList}>
      <div className={clsx(styles.backButton,styles.backInviteButton)} onClick={handleBackToAppShare}>
        <BackIcon/><span>Back to App Share</span>
      </div>
      <div className={styles.historyItemList}>
        {isLoading ? (
          <div className={styles.loading}><Loader/></div>
        ) : error ? (
          <div className={styles.error}>{'Failed to load shared app history'}</div>
        ) : filteredItems.length > 0 ? (
          filteredItems.map(renderHistoryItem)
        ) : (
          <div className={styles.noResults}>No matching results</div>
        )}
      </div>
    </div>
  );

  const renderDetailView = () => {
    if (!selectedItem) return null;

    return (
      <>
        <div className={styles.backButton} onClick={handleBackClick}>
          <BackIcon/> Back to App Share History
        </div>

        <div className={styles.historyItemExpanded}>
          <div className={styles.expandedHistoryItemTitle}>
            To: <Tooltip
              title={selectedItem.to_email}
              placement="top-start"
              TransitionComponent={Fade}
              TransitionProps={{ timeout: 200 }}
              classes={{
                tooltip: styles.emailTooltip
              }}
            >
              <span>{selectedItem.to_email}</span>
            </Tooltip>
          </div>
          <div className={styles.historyItemDetails}>
            <div className={styles.detailsRow}>
              <span>{selectedItem.shared_app_date_time}</span>
            </div>
          </div>
        </div>

        <div className={styles.appList}>
          <div className={styles.appItemList}>
            <div className={styles.appItem}>
              {selectedItem.email_content && renderEmailContent(selectedItem.email_content)}
            </div>
          </div>
        </div>
      </>
    );
  };

  return (
    <div className={styles.appSearchHistory}>
      <div className={styles.headerMain}>
        <div className={styles.header}>SHARED APP HISTORY</div>
        
        <div className={styles.searchBox}>
          <div className={styles.searchIcon}><SearchIconSvg/></div>
          <input
            type="text"
            className={styles.searchInput}
            placeholder="Search History"
            value={searchValue}
            onChange={handleSearchChange}
            disabled={!!selectedItem}
          />
        </div>
      </div>
  
      {selectedItem ? renderDetailView() : renderHistoryList()}
    </div>
  );
};

export default SharedAppHistoryWindow;