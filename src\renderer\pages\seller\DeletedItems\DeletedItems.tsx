import styles from './DeletedItems.module.scss';
import Home from '../../search/home';
import CreatePo from '../../buyer/CreatePo/CreatePo';
import { useCreatePoStore, useSearchStore } from '@bryzos/giss-ui-library';
import { useEffect, } from 'react';
import { useLocation } from 'react-router-dom';
import { routes } from 'src/renderer/common';
import { useRightWindowStore } from '../../RightWindow/RightWindowStore';
import UploadReviewWindow from 'src/renderer/component/UploadReviewWindow/UploadReviewWindow';
import { useRestoreDeletedItem } from '../../../hooks/useRestoreDeletedItems';


export const Undelete = () => {
    const { restoredItem, handleRestoreDeletedItem, isLoading } =
    useRestoreDeletedItem();

  return (
    <div>
      {!!restoredItem && (
        <button
          className={styles.undeleteButton}
          key={restoredItem.id}
          disabled={isLoading || !restoredItem}
          onClick={() => handleRestoreDeletedItem()}
        >
          {isLoading ? 'RESTORING...' : 'UNDELETE'}
        </button>
      )}
    </div>
  );
};

export default function BuyerDeletedItems() {
  const location = useLocation();
  const isBuyerDeletedItemsPage =
    location.pathname === routes.buyerDeleteOrderPage;

  const { selectedSavedSearch } = useSearchStore();
  const selectedQuote = useCreatePoStore((state) => state.selectedQuote);
  const { setLoadComponent } = useRightWindowStore();

  const restoredItem = selectedSavedSearch || selectedQuote;

  useEffect(() => {
    if (
      isBuyerDeletedItemsPage &&
      restoredItem?.order_type === 'INSTANT_PRICING'
    ) {
      setLoadComponent(<Undelete />);
      return () => setLoadComponent(null);
    } else if (
      isBuyerDeletedItemsPage &&
      (restoredItem?.order_type === 'QUOTE' ||
        restoredItem?.order_type === 'PO')
    ) {
      setLoadComponent(<UploadReviewWindow key={location.pathname}/>);
      return () => setLoadComponent(null);
    } else {
      setLoadComponent(null);
    }
  }, [isBuyerDeletedItemsPage, setLoadComponent, restoredItem]);

  return (
    <div className={styles.root}>
      {!(restoredItem && isBuyerDeletedItemsPage) && (
        <p className={styles.emptyStateMessage}>
          This is the viewing pane.
          <br /> Select from the list on the left to
          <br /> review deleted item.
        </p>
      )}

      {restoredItem?.order_type === 'INSTANT_PRICING' && <Home />}
      {(restoredItem?.order_type === 'QUOTE' ||
        restoredItem?.order_type === 'PO') && <CreatePo />}
    </div>
  );
}
