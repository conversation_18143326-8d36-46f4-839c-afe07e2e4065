// @ts-nocheck
import { useQuery } from "@tanstack/react-query";
import { reactQueryKeys } from "../common";
import axios from "axios";
import { useGlobalStore } from "@bryzos/giss-ui-library";

const useGetSubscriptionsPricing = () => {
  const { setShowLoader } = useGlobalStore();

  return useQuery(
    [reactQueryKeys.getSubscriptionsPricing],
    async () => {
      setShowLoader(true);
      try {
        const response = axios.get(import.meta.env.VITE_API_SERVICE + '/subscription/pricing');
        const responseData = await response;
        
        if (responseData.data && responseData.data.data) {
          if (
            typeof responseData.data.data === "object" &&
            "error_message" in responseData.data.data
          ) {
            throw new Error(responseData.data.data.error_message);
          } else {
            
            return responseData.data.data;
          }
        } else {
          return null;
        }
      } catch (error) {
        throw new Error(error?.message ?? error);
      } finally {
        // setShowLoader(false);
      }
    },
    {
      retry: false,
      refetchOnWindowFocus: false,
    }
  );
};

export default useGetSubscriptionsPricing;
