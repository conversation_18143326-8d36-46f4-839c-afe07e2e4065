.attachmentBubble {
  max-width: 80%;
  width: 100%;
  font-family: 'Inter';
}

.attachmentCard {
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

.othersMessage .attachmentCard {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid rgba(0, 0, 0, 0.08);
  color: #212529;

  &:hover {
    border-color: rgba(0, 0, 0, 0.12);
  }
}

.attachmentContent {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.fileIconContainer {
  flex-shrink: 0;
  margin-right: 8px;
}

.documentIcon{
  svg{
    width: 25px;
    height: 25px;

    path {
      fill: #ffffff;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
    }
  }
}

.attachmentInfo {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.attachmentName {
  font-weight: 600;
  font-size: 12px;
  line-height: 1.4;
  color: #ffffff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}

.othersMessage .attachmentName {
  color: #212529;
}

.attachmentMeta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  line-height: 1.3;
}

.attachmentExtension {
  font-size: 10px;
  color: #a0a0a0;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.othersMessage .attachmentExtension {
  font-size: 10px;
  color: #6c757d;
}

.attachmentSize {
  font-size: 10px;
  color: #888888;
  font-weight: 400;
}

.othersMessage .attachmentSize {
  color: #6c757d;
}

.attachmentActions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .previewButton{
    color: #fff;
    cursor: pointer;
    margin-right: 8px;
    font-size: 12px;
    font-weight: 500;
    letter-spacing: 0.5px;
    margin-left: 3px;
  }
}

.downloadButton {
  display: inline-flex;
  align-items: center;
}

.downloadIcon {
  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    width: 18px;
    height: 18px;

    path {
      fill: #ffffff;
    }
  }
}

.othersMessage {
  .attachmentActions {
  .previewButton{
    color: #000;
 
  }
}

  .downloadIcon {
    svg {

      path {
        fill: #000;
      }
    }
  }

}

// File type icons
.documentIcon,
.imageIcon,
.spreadsheetIcon,
.presentationIcon,
.archiveIcon,
.unknownIcon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 700;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

.fileTypeText {
  color: #ffffff;
  font-size: 10px;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.documentIcon {
  background: linear-gradient(135deg, #4285F4 0%, #1a73e8 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

// PDF-specific styling
.pdfIcon {
  background: linear-gradient(135deg, #DB4437 0%, #c23321 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  border-radius: 8px;

  &::after {
    content: 'PDF';
    position: absolute;
    bottom: 2px;
    right: 2px;
    font-size: 6px;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.3px;
  }
}

.imageIcon {
  background: linear-gradient(135deg, #0F9D58 0%, #0d7c47 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);

  svg {
    width: 25px;
    height: 25px;

    path {
      fill: #ffffff;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
    }
  }
}

.spreadsheetIcon {
  background: linear-gradient(135deg, #F4B400 0%, #ea8600 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);

  svg {
    width: 25px;
    height: 25px;

    path {
      fill: #ffffff;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
    }
  }
}

.presentationIcon {
  background: linear-gradient(135deg, #DB4437 0%, #c23321 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.archiveIcon {
  background: linear-gradient(135deg, #673AB7 0%, #512da8 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.unknownIcon {
  background: linear-gradient(135deg, #757575 0%, #616161 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.imageTooltip {
  padding: 12px;
  max-width: 250px;
  width: 250px;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(20, 20, 20, 0.95) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.imagePreviewContainer {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.imagePreview {
  max-width: 100%;
  max-height: 200px;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.imagePreviewInfo {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.imagePreviewName {
  font-weight: 600;
  font-size: 14px;
  color: #ffffff;
  word-break: break-word;
  line-height: 1.4;
}

.imagePreviewType {
  font-size: 12px;
  color: #a0a0a0;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.attachmentText {
    font-family: Inter;
    color: #dbdcde;
    font-size: 0.85rem;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: left;
    word-wrap: break-word;
    margin-top: 16px;
    padding-top: 8px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}