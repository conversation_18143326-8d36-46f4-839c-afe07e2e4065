import { useMutation } from '@tanstack/react-query';
import { axios } from '@bryzos/giss-ui-library';
import useDialogStore from 'src/renderer/component/DialogPopup/DialogStore';

interface VerifyBankAccountParams {
  data: {
    setup_intent_id: string;
    descriptor_code?: string;
    amounts?: number[];
  };
}

interface VerifyBankAccountResponse {
  data: {
    verification_status: boolean;
    message?: string;
    error_message?: {
      reason: string;
      email?: string[];
      message?: string;
    };
  };
}

/**
 * Hook for verifying a bank account using either descriptor code or micro-deposits
 */
const usePostVerifyBankAccount = () => {
  const { showCommonDialog, resetDialogStore } = useDialogStore();

  return useMutation<VerifyBankAccountResponse, Error, VerifyBankAccountParams>(
    async (params) => {
      const response = await axios.post<VerifyBankAccountResponse>(
        import.meta.env.VITE_API_SERVICE + '/subscription/ach/verify-bank-account',
        params
      );

      // Check if the response contains an error message
      if (response.data?.data?.error_message) {
        const errorData = response.data.data.error_message;
          // For general errors, show a dialog
          const errorMessage = errorData || 'Verification failed. Please try again.';
          showCommonDialog(null, errorMessage, null, resetDialogStore, [
            { name: 'OK', action: resetDialogStore }
          ]);
      }

      return response.data;
    }
  );
};

export default usePostVerifyBankAccount;
