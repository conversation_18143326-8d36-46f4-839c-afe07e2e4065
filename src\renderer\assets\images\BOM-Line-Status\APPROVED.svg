<svg width="126" height="189" viewBox="0 0 126 189" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_851_1341)">
<g clip-path="url(#clip0_851_1341)">
<rect width="126" height="189" rx="8" fill="#131314"/>
<g filter="url(#filter1_f_851_1341)">
<ellipse cx="29.8316" cy="-34.6305" rx="31.4019" ry="28.3578" fill="#9786FF"/>
<ellipse cx="29.8316" cy="-34.6305" rx="31.4019" ry="28.3578" fill="url(#paint0_linear_851_1341)" fill-opacity="0.8"/>
<g clip-path="url(#paint1_angular_851_1341_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.0314019 0.0371387 -0.0294314 0.0518875 29.8316 -32.3273)"><foreignObject x="-940.952" y="-940.952" width="1881.9" height="1881.9"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><ellipse cx="29.8316" cy="-34.6305" rx="31.4019" ry="28.3578" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:62.803733825683594,&#34;m01&#34;:-58.862773895263672,&#34;m02&#34;:27.861078262329102,&#34;m10&#34;:74.277313232421875,&#34;m11&#34;:103.77507019042969,&#34;m12&#34;:-121.35349273681641},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<g opacity="0.8" filter="url(#filter2_f_851_1341)">
<circle cx="97.704" cy="211.704" r="14.7041" fill="#9786FF"/>
<circle cx="97.704" cy="211.704" r="14.7041" fill="url(#paint2_linear_851_1341)" fill-opacity="0.8"/>
<g clip-path="url(#paint3_angular_851_1341_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.014704 0.0192571 -0.0137814 0.0269046 97.7041 212.898)"><foreignObject x="-952.598" y="-952.598" width="1905.2" height="1905.2"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><circle cx="97.704" cy="211.704" r="14.7041" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:29.408096313476562,&#34;m01&#34;:-27.562726974487305,&#34;m02&#34;:96.781364440917969,&#34;m10&#34;:38.514160156250,&#34;m11&#34;:53.809295654296875,&#34;m12&#34;:166.73655700683594},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<path d="M25.7285 16.4766H21.2402L20.4844 18H18.0586L22.2715 9.94922H24.6973L28.9102 18H26.4844L25.7285 16.4766ZM22.0195 14.9062H24.9609L23.4961 11.9414L22.0195 14.9062ZM39.2754 12.6973C39.2754 13.0996 39.2168 13.4707 39.0996 13.8105C38.9824 14.1465 38.793 14.4375 38.5312 14.6836C38.2734 14.9258 37.9375 15.1152 37.5234 15.252C37.1094 15.3887 36.6055 15.457 36.0117 15.457H32.4023V18H30.2285V9.94922H36.0117C36.6055 9.94922 37.1094 10.0176 37.5234 10.1543C37.9375 10.2871 38.2734 10.4766 38.5312 10.7227C38.793 10.9648 38.9824 11.2539 39.0996 11.5898C39.2168 11.9258 39.2754 12.2949 39.2754 12.6973ZM37.0781 12.7324C37.0781 12.5449 37.0508 12.3848 36.9961 12.252C36.9453 12.1152 36.8594 12.0039 36.7383 11.918C36.6172 11.832 36.459 11.7695 36.2637 11.7305C36.0684 11.6875 35.8281 11.666 35.543 11.666H32.4023V13.7402H35.543C35.8281 13.7402 36.0684 13.7246 36.2637 13.6934C36.459 13.6582 36.6172 13.6016 36.7383 13.5234C36.8594 13.4414 36.9453 13.3359 36.9961 13.207C37.0508 13.0781 37.0781 12.9199 37.0781 12.7324ZM50.127 12.6973C50.127 13.0996 50.0684 13.4707 49.9512 13.8105C49.834 14.1465 49.6445 14.4375 49.3828 14.6836C49.125 14.9258 48.7891 15.1152 48.375 15.252C47.9609 15.3887 47.457 15.457 46.8633 15.457H43.2539V18H41.0801V9.94922H46.8633C47.457 9.94922 47.9609 10.0176 48.375 10.1543C48.7891 10.2871 49.125 10.4766 49.3828 10.7227C49.6445 10.9648 49.834 11.2539 49.9512 11.5898C50.0684 11.9258 50.127 12.2949 50.127 12.6973ZM47.9297 12.7324C47.9297 12.5449 47.9023 12.3848 47.8477 12.252C47.7969 12.1152 47.7109 12.0039 47.5898 11.918C47.4688 11.832 47.3105 11.7695 47.1152 11.7305C46.9199 11.6875 46.6797 11.666 46.3945 11.666H43.2539V13.7402H46.3945C46.6797 13.7402 46.9199 13.7246 47.1152 13.6934C47.3105 13.6582 47.4688 13.6016 47.5898 13.5234C47.7109 13.4414 47.7969 13.3359 47.8477 13.207C47.9023 13.0781 47.9297 12.9199 47.9297 12.7324ZM54.1055 18H51.9316V9.94922H57.3809C57.9668 9.94922 58.4629 10.0098 58.8691 10.1309C59.2793 10.252 59.6113 10.4238 59.8652 10.6465C60.123 10.8691 60.3086 11.1387 60.4219 11.4551C60.5391 11.7676 60.5977 12.1172 60.5977 12.5039C60.5977 12.8438 60.5488 13.1406 60.4512 13.3945C60.3574 13.6484 60.2285 13.8672 60.0645 14.0508C59.9043 14.2305 59.7168 14.3828 59.502 14.5078C59.2871 14.6328 59.0605 14.7363 58.8223 14.8184L61.459 18H58.916L56.4785 15.0352H54.1055V18ZM58.4004 12.4922C58.4004 12.3359 58.3789 12.2031 58.3359 12.0938C58.2969 11.9844 58.2266 11.8965 58.125 11.8301C58.0234 11.7598 57.8867 11.709 57.7148 11.6777C57.5469 11.6465 57.3359 11.6309 57.082 11.6309H54.1055V13.3535H57.082C57.3359 13.3535 57.5469 13.3379 57.7148 13.3066C57.8867 13.2754 58.0234 13.2266 58.125 13.1602C58.2266 13.0898 58.2969 13 58.3359 12.8906C58.3789 12.7812 58.4004 12.6484 58.4004 12.4922ZM73.377 13.9805C73.377 14.6719 73.25 15.2832 72.9961 15.8145C72.7422 16.3418 72.3789 16.7832 71.9062 17.1387C71.4336 17.4941 70.8594 17.7637 70.1836 17.9473C69.5117 18.127 68.7559 18.2168 67.916 18.2168C67.0762 18.2168 66.3164 18.127 65.6367 17.9473C64.9609 17.7637 64.3848 17.4941 63.9082 17.1387C63.4316 16.7832 63.0645 16.3418 62.8066 15.8145C62.5488 15.2832 62.4199 14.6719 62.4199 13.9805C62.4199 13.2891 62.5488 12.6797 62.8066 12.1523C63.0645 11.6211 63.4316 11.1777 63.9082 10.8223C64.3848 10.4668 64.9609 10.1992 65.6367 10.0195C66.3164 9.83594 67.0762 9.74414 67.916 9.74414C68.7559 9.74414 69.5117 9.83594 70.1836 10.0195C70.8594 10.1992 71.4336 10.4668 71.9062 10.8223C72.3789 11.1777 72.7422 11.6211 72.9961 12.1523C73.25 12.6797 73.377 13.2891 73.377 13.9805ZM71.1797 13.9805C71.1797 13.6797 71.1309 13.3809 71.0332 13.084C70.9395 12.7832 70.7695 12.5137 70.5234 12.2754C70.2812 12.0371 69.9492 11.8438 69.5273 11.6953C69.1055 11.5469 68.5684 11.4727 67.916 11.4727C67.4785 11.4727 67.0938 11.5078 66.7617 11.5781C66.4297 11.6445 66.1426 11.7383 65.9004 11.8594C65.6582 11.9805 65.4551 12.123 65.291 12.2871C65.127 12.4473 64.9961 12.6211 64.8984 12.8086C64.8008 12.9922 64.7305 13.1855 64.6875 13.3887C64.6484 13.5879 64.6289 13.7852 64.6289 13.9805C64.6289 14.1797 64.6484 14.3809 64.6875 14.584C64.7305 14.7871 64.8008 14.9824 64.8984 15.1699C64.9961 15.3535 65.127 15.5254 65.291 15.6855C65.4551 15.8457 65.6582 15.9863 65.9004 16.1074C66.1426 16.2246 66.4297 16.3184 66.7617 16.3887C67.0938 16.4551 67.4785 16.4883 67.916 16.4883C68.5684 16.4883 69.1055 16.4141 69.5273 16.2656C69.9492 16.1172 70.2812 15.9238 70.5234 15.6855C70.7695 15.4473 70.9395 15.1797 71.0332 14.8828C71.1309 14.582 71.1797 14.2812 71.1797 13.9805ZM80.6426 18H78.1113L74.0391 9.94922H76.4648L79.3945 16.0078L82.3184 9.94922H84.7441L80.6426 18ZM86.0684 18V9.94922H94.2188V11.666H88.3008V12.9961H93.9141V14.7129H88.3008V16.2832H94.3008V18H86.0684ZM106.266 13.9688C106.266 14.4844 106.209 14.9434 106.096 15.3457C105.982 15.7441 105.822 16.0918 105.615 16.3887C105.412 16.6855 105.166 16.9375 104.877 17.1445C104.592 17.3516 104.273 17.5176 103.922 17.6426C103.57 17.7676 103.191 17.8594 102.785 17.918C102.379 17.9727 101.955 18 101.514 18H96.4043V9.94922H101.502C101.943 9.94922 102.367 9.97852 102.773 10.0371C103.18 10.0918 103.559 10.1816 103.91 10.3066C104.266 10.4316 104.588 10.5977 104.877 10.8047C105.166 11.0078 105.412 11.2598 105.615 11.5605C105.822 11.8574 105.982 12.2051 106.096 12.6035C106.209 13.002 106.266 13.457 106.266 13.9688ZM104.074 13.9688C104.074 13.582 104.023 13.2461 103.922 12.9609C103.824 12.6758 103.664 12.4414 103.441 12.2578C103.223 12.0703 102.936 11.9316 102.58 11.8418C102.225 11.748 101.791 11.7012 101.279 11.7012H98.5781V16.248H101.279C101.791 16.248 102.225 16.2031 102.58 16.1133C102.936 16.0195 103.223 15.8789 103.441 15.6914C103.664 15.5 103.824 15.2617 103.922 14.9766C104.023 14.6914 104.074 14.3555 104.074 13.9688Z" fill="url(#paint4_radial_851_1341)"/>
</g>
<rect x="0.183801" y="0.183801" width="125.632" height="188.632" rx="7.8162" stroke="url(#paint5_linear_851_1341)" stroke-opacity="0.4" stroke-width="0.367601"/>
</g>
<defs>
<filter id="filter0_ii_851_1341" x="-2" y="-1" width="132" height="194" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_851_1341"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2" dy="-1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_851_1341" result="effect2_innerShadow_851_1341"/>
</filter>
<filter id="filter1_f_851_1341" x="-38.3304" y="-99.7484" width="136.325" height="130.235" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="18.3801" result="effect1_foregroundBlur_851_1341"/>
</filter>
<clipPath id="paint1_angular_851_1341_clip_path"><ellipse cx="29.8316" cy="-34.6305" rx="31.4019" ry="28.3578"/></clipPath><filter id="filter2_f_851_1341" x="60.9439" y="174.944" width="73.5184" height="73.5204" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="11.028" result="effect1_foregroundBlur_851_1341"/>
</filter>
<clipPath id="paint3_angular_851_1341_clip_path"><circle cx="97.704" cy="211.704" r="14.7041"/></clipPath><linearGradient id="paint0_linear_851_1341" x1="29.8316" y1="-45.8584" x2="70.3543" y2="-21.3588" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_851_1341" x1="97.704" y1="205.882" x2="117.665" y2="216.78" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint4_radial_851_1341" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(21 5.5) rotate(18.0685) scale(222.471 19.9974)">
<stop stop-color="#43F776"/>
<stop offset="1" stop-color="#3F4046"/>
</radialGradient>
<linearGradient id="paint5_linear_851_1341" x1="140.415" y1="99.8338" x2="20.3616" y2="25.4353" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#1B1C21"/>
</linearGradient>
<clipPath id="clip0_851_1341">
<rect width="126" height="189" rx="8" fill="white"/>
</clipPath>
</defs>
</svg>
