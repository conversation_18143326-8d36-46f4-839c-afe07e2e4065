import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './renderer/App';
import './renderer/index.scss';
import { 
    QueryClient,
    QueryClientProvider,
  } from '@tanstack/react-query'
  import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { BrowserRouter } from 'react-router-dom';
import log from 'electron-log/renderer';
import './renderer/App.scss';

const queryClient = new QueryClient();

if(import.meta.env.VITE_ENVIRONMENT !== 'dev'){
  console.log = log.log;
  console.error = log.error;
  console.warn = log.warn;
}
const root = ReactDOM.createRoot(document.getElementById('root')!);

root.render(
  <QueryClientProvider client={queryClient}>
    <BrowserRouter> 
       <App />
    </BrowserRouter>
  </QueryClientProvider>
);
