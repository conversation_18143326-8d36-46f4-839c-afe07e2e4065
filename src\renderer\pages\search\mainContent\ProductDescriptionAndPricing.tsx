import React, { useEffect, useRef, useState } from 'react'
import ProductDescription from 'src/renderer/component/ProductDescription/ProductDescription'
import ProductPricing from './productPricing'
import { ProductPricingModel } from 'src/renderer/types/Search'
import { useGlobalStore, useSearchStore } from '@bryzos/giss-ui-library'
import clsx from 'clsx'
import { ReactComponent as UsaFlag } from '../../../assets/images/ButtonUsaMade.svg';

import styles from '../home.module.scss';
import { getLocal, setLocal } from 'src/renderer/helper'
import { localStorageKeys } from 'src/renderer/common'

type ProductDescriptionAndPricingState = {
    i: number
    selectedSearchProduct: ProductPricingModel
};

const ProductDescriptionAndPricing: React.FC<ProductDescriptionAndPricingState> = ({ i, selectedSearchProduct }) => {

    const { productMapping }: any = useGlobalStore();
    const { selectedProductsData, setSelectedProductsData, productFeedbackInProgress, focusSingleProduct, setFocusSingleProduct, shortListedSearchProductsData, filterShortListedSearchProductsData } = useSearchStore();
    const { selectedSavedSearch } = useSearchStore();
    const productDescriptionAndPricingRef = useRef(null);
    const [mouseOverProductId, setMouseOverProductId] = useState<any>(null);
    const [isMouseOverOnProduct, setIsMouseOverOnProduct] = useState(false)
    const [showInfoPopup, setShowInfoPopup] = useState(false);

    const handleSelectSingleProduct = (
        singleProducts: ProductPricingModel,
        focusSingleProduct: { [key: number]: boolean },
        setFocusSingleProduct: React.Dispatch<{ [key: number]: boolean }>
    ) => {
        if(!(productFeedbackInProgress && productFeedbackInProgress.has(singleProducts.id))){
            const singleProduct: ProductPricingModel[] = selectedProductsData ?? [];
            const isDuplicate = singleProduct.some((data: ProductPricingModel) => data.id === singleProducts.id);
            let singleProductData;
            if (isDuplicate) {
                delete focusSingleProduct[singleProducts.id];
                singleProductData = singleProduct.filter((data: ProductPricingModel) => data.id !== singleProducts.id)
            } else {
                singleProductData = [...singleProduct, singleProducts]
                setFocusSingleProduct({ ...focusSingleProduct, [singleProducts.id]: true });
            }
            setSelectedProductsData(singleProductData);
        }
    }
    
    const handleSelectAllLinesOnClick = () => {
        const spreadSelectedSearchProductsData = [...shortListedSearchProductsData];
        let selectAllLinesObj: any = {};
        const filterSelectedProductData = spreadSelectedSearchProductsData.filter(({ id }) => {
            if (!(productFeedbackInProgress && productFeedbackInProgress.has(id))) {
                selectAllLinesObj[id] = true;
                return true;
            }
            return false;
        })
        setSelectedProductsData(filterSelectedProductData);
        setFocusSingleProduct(selectAllLinesObj);
    }

    const handleUpdateSelectedProductsData = () => {
        const selectedUpdatedProducts = shortListedSearchProductsData.filter(product => 
            selectedProductsData.some(selectedProduct => selectedProduct.id === product.id)
        );
        
        let selectAllLinesObj: any = {};
        const filterSelectedProductData = selectedUpdatedProducts.filter(({ id }) => {
            if (!(productFeedbackInProgress && productFeedbackInProgress.has(id))) {
                selectAllLinesObj[id] = true;
                return true;
            }
            return false;
        })
        setSelectedProductsData(filterSelectedProductData);
        setFocusSingleProduct(selectAllLinesObj);
    }

    const handleUnselectAllLinesOnClick = () => {
        setSelectedProductsData([]);
        setFocusSingleProduct({});
    }

    const handleCloseInfoPopup = () => {
        setShowInfoPopup(false);
        setLocal(localStorageKeys.hasSeenProductSelectionPopup, true);
    }

    const handleMouseEnter = () => {
        const hasSeenProductSelectionPopup = getLocal(localStorageKeys.hasSeenProductSelectionPopup, false);
        if (i === 0 && !hasSeenProductSelectionPopup) {
            setShowInfoPopup(true);
        }
    }

    const handleMouseLeave = () => {
        if (i === 0) {
            setShowInfoPopup(false);
        }
    }

    useEffect(() => {
        if(selectedProductsData.length > 0){
            handleUpdateSelectedProductsData()
        }
    }, [shortListedSearchProductsData])

    return (
        <div
            className={clsx(styles.selectedSearchProductTop, !!selectedSavedSearch?.pricing_expired && styles.expiredSearchOverlay)}
            onMouseOver={() => { 
                setMouseOverProductId(selectedSearchProduct.id);
                handleMouseEnter();
            }}
            onMouseOut={() => { 
                setMouseOverProductId(null);
                handleMouseLeave();
            }}
            ref={productDescriptionAndPricingRef}

            data-hover-video-id='price-search-line'
        >
            {showInfoPopup && i === 0 && !getLocal(localStorageKeys.hasSeenProductSelectionPopup, false) && (
                <div className={styles.infoPopup}>
                    <div className={styles.infoPopupContent}>
                        <div className={styles.infoPopupText}>
                            <h4>PRO TIP</h4>
                        </div>
                        <div className={styles.proTipGrid}>
                         <p>Click line(s) to activate options: Add to PO, Share Pricing, Export & Save List.</p>
                        <button 
                            className={styles.infoPopupButton}
                            onClick={handleCloseInfoPopup}
                        >
                            Got It
                        </button>
                        </div>
                        
                    </div>
                </div>
            )}
            {((mouseOverProductId === selectedSearchProduct.id && focusSingleProduct[selectedSearchProduct.id]) && ((filterShortListedSearchProductsData.length - (productFeedbackInProgress?.size ?? 0)) > 1)) &&
                (
                    <>
                        {((selectedProductsData.length + (productFeedbackInProgress?.size ?? 0)) === filterShortListedSearchProductsData.length) ?
                            <div className={styles.selectAllLines} onClick={handleUnselectAllLinesOnClick}>UNSELECT ALL LINES</div>
                            :
                            <div className={styles.selectAllLines} onClick={handleSelectAllLinesOnClick}>SELECT ALL LINES</div>
                        }
                    </>
                )
            }
            <div
                className={clsx(styles.productDescriptionMain, (!(productFeedbackInProgress && productFeedbackInProgress.has(selectedSearchProduct.id)) &&focusSingleProduct[selectedSearchProduct.id]) ? styles.clickToShare : '')}
                key={selectedSearchProduct.id}
                onClick={() => handleSelectSingleProduct(selectedSearchProduct, focusSingleProduct, setFocusSingleProduct)}
                onMouseOver={()=> {setIsMouseOverOnProduct(true)}}
                onMouseLeave={()=>{setIsMouseOverOnProduct(false)}}
            >
                <ProductDescription product={selectedSearchProduct} />
                <ProductPricing 
                    product={selectedSearchProduct}
                    productDescriptionAndPricingRef={productDescriptionAndPricingRef} 
                    isMouseOverOnProduct = {isMouseOverOnProduct}
                    showInfoPopup={showInfoPopup}
                />
            </div>
        </div>
    )
}

export default ProductDescriptionAndPricing;