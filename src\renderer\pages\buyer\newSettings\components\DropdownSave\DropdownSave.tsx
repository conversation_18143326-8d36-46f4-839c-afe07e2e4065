import React, { useState, useRef } from 'react';
import { ClickAwayListener } from '@mui/material';
import styles from './Dropdown.module.scss';
import { ReactComponent as DropdownIcon } from '../../../../../assets/images/StateIconDropDpown.svg';
import clsx from 'clsx';
import { navigatePage } from 'src/renderer/helper';
import { routes } from 'src/renderer/common';

interface DropdownSaveButtonProps {
    onSave: () => void | null | undefined;
    isDisabled?: boolean;
    position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
    className?: string;
    buttonText?: string;
}

const DropdownSave: React.FC<DropdownSaveButtonProps> = ({
    onSave,
    isDisabled = false,
    className = '',
    buttonText = 'Save',
}) => {


    return (
        <div className={className}>
            <div className={styles.buttonContainer}>
                <button
                    id='settings-save-button'
                    disabled={isDisabled}
                    onClick={onSave}
                    className={styles.saveButton}
                    onKeyDown={(e) => {
                        if (e.key === 'Tab' && e.shiftKey) {
                            // Find the last focusable element based on the current active tab
                            e.preventDefault();
                            const lastFocusableElement = document.querySelector('[data-last-focusable]') as HTMLElement;
                            if (lastFocusableElement) {
                                lastFocusableElement.focus();
                            }else{
                                const companyButton = document.getElementById('COMPANY')
                                if (companyButton) {
                                  (companyButton as HTMLElement).focus();
                                }
                            }
                        }
                    }}
                >
                    {buttonText}
                </button>
            </div>
        </div>
    );
};

export default DropdownSave;
