.cassDialog {
    .dialogContent {
        max-width: 370px;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding: 30px 25px 30px 25px;
        object-fit: contain;
        border-radius: 10px;
        -webkit-backdrop-filter: blur(24px);
        backdrop-filter: blur(24px);
        box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
        background-color: rgba(0, 0, 0, 0.72);
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.6;
        text-align: center;
        color: #fff;

        p {
            margin-bottom: 15px;
        }


        .submitBtn {
            height: 40px;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            padding: 10px 24px;
            border-radius: 4px;
            border: solid 0.5px #fff;
            background-color: transparent;
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.4;
            text-align: center;
            color: #fff;
            margin-top: 20px;
            transition: all 0.1s;

            &:hover {
                background-color: #70ff00;
                border: solid 0.5px #70ff00;
                color: #000;
            }

            &:disabled {
                opacity: 0.5;
                cursor: not-allowed;

                &:hover {
                    border: solid 0.5px #fff;
                    background-color: transparent;
                    color: #fff;
                }
            }
        }
    }

}