import styles from '../../buyer/CompleteYourAccountSetup/CompleteYourAccount.module.scss';
import { ReactComponent as CheckIcon } from '../../../assets/images/check-filled.svg';
import { ReactComponent as UncheckIcon } from '../../../assets/images/check-outlined.svg';
import { useNavigate } from 'react-router-dom';
import { routes } from 'src/renderer/common';

export default function CompleteYourAccountSetup({
  errors,
}: {
  errors: Record<string, string[]>;
}) {
  const navigate = useNavigate();
  const steps = [
    {
      key: 'company',
      label: 'Add Company Information',
    },
    {
      key: 'user',
      label: 'Add User Information',
    },
    {
      key: 'shipments',
      label: 'Add Shipments Details',
    },
    {
      key: 'payment',
      label: 'Add Payments',
    },
  ];

  const firstErrorKey = Object.keys(errors).find(
    (key) => errors[key].length > 0
  );

  const getTabFromKey = (key: string) => {
    switch (key) {
      case 'company':
        return 'COMPANY';
      case 'user':
        return 'USER';
      case 'shipments':
        return 'SHIPMENTS';
      case 'payment':
        return 'PAYMENTS';
      default:
        return '';
    }
  };

  const handleContinue = () => {
    if (firstErrorKey) {
      const tab = getTabFromKey(firstErrorKey);
      navigate(routes.sellerSettingPage, { state: { tab } });
    }
  };

  return (
    <div className={styles.root}>
      <h3 className={styles.title}>Complete Your Account Setup</h3>
      <p className={styles.description}>
        To ensure a smooth transaction, please complete your account setup.
      </p>
      <ul className={styles.steps}>
        {steps.map((step) => (
          <li
            key={step.key}
            role={errors[step.key as keyof typeof errors].length > 0 ? 'button' : undefined}
            onClick={() => {
              if (errors[step.key as keyof typeof errors].length > 0) {
                navigate(routes.sellerSettingPage, { state: { tab: getTabFromKey(step.key) } });
              }
            }}
            data-error={errors[step.key as keyof typeof errors].length > 0}
          >
            {errors[step.key as keyof typeof errors].length === 0 ? (
              <CheckIcon/>
            ) : (
              <UncheckIcon />
            )}
            {step.label}
          </li>
        ))}
      </ul>
      <button className={styles.continueButton} onClick={handleContinue}>
        CONTINUE
      </button>
    </div>
  );
}
