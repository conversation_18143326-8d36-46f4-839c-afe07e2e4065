// VideoSection.tsx
// React + TypeScript + SCSS Modules (no UI libs)
// Renders a horizontal row of video cards for a section.
// - Uses thumbnail_s3_url_map.thumbnail_app ONLY for the thumbnail
// - Sorts by numeric `sequence` DESC (largest first)
// - Shows title and "<n> views"
// - Optional "NOW PLAYING" overlay when `activeId` matches the item's id
// - Emits onItemClick (card) and onPlayClick (play button)

import React, { memo, useMemo } from "react";
import styles from "./VideoSection.module.scss";
import { ReactComponent as PlayIcon } from '../../assets/images/smallWindowVideoPlayButton.svg';
import { ReactComponent as EyeIcon } from '../../assets/images/eyeIconVideoViews.svg';

type ThumbMap = {
  thumbnail_app?: string;
  [k: string]: string | undefined;
};

export type VideoItem = {
  id: string;
  title: string;
  view_count?: number | null;
  sequence?: string | number | null;
  share_video_url?: string | null;
  thumbnail_s3_url_map?: ThumbMap | null;
  show_on_ui?: number | boolean;
  is_active?: number | boolean;
};

export interface VideoSectionProps {
  section: string;
  sectionList: VideoItem[];
  activeId?: string; // highlights "NOW PLAYING"
  onItemClick?: (item: VideoItem, index: number) => void;
  onPlayClick?: (item: VideoItem, index: number) => void;
  interactedVideos?: any[];
}
function formatViews(n?: number | null) {
  const v = typeof n === "number" ? n : 0;
  return `${v} ${v === 1 ? "view" : "views"}`;
}

export const VideoSection: React.FC<VideoSectionProps> = memo(
  ({ section, sectionList, activeId, onItemClick, onPlayClick, interactedVideos }) => {
    const data = useMemo(() => {
      // If your backend always sends strings like "12.00", parse them safely.
      const toNum = (x: VideoItem) =>
        typeof x.sequence === "number"
          ? x.sequence
          : parseFloat(String(x.sequence ?? "0"));

      // (Optional) keep only items intended for UI if those flags exist.
      const filtered = sectionList.filter((it) => {
        const show =
          typeof it.show_on_ui === "boolean"
            ? it.show_on_ui
            : it.show_on_ui === 1 || it.show_on_ui === undefined;
        const active =
          typeof it.is_active === "boolean"
            ? it.is_active
            : it.is_active === 1 || it.is_active === undefined;
        return show && active;
      });

      return [...filtered].sort((a, b) => toNum(a) - toNum(b));
    }, [sectionList]);

    return (
      <section className={styles.section}>
        <h3 className={styles.header}>{section}</h3>
        <div className={styles.scroller} role="list">
          {data.map((item, idx) => {
            const thumb = item.thumbnail_s3_url_map?.thumbnail_app || "";
            const isActive = activeId && activeId === item.id;

            const getViewCount = () => {
              if (interactedVideos && item.video_id) {
                const interactedVideo = interactedVideos.find(video => video.video_id === item.video_id);
                if (interactedVideo && typeof interactedVideo.view_count === "number") {
                  return interactedVideo.view_count;
                }
              }
              return item.view_count;
            };

            return (
              <div
                role="listitem"
                key={item.id}
                className={styles.card}
                onClick={() => onItemClick?.(item, idx)}
              >
                <div className={styles.thumbWrap}>
                  {thumb ? (
                    <img
                      className={styles.thumb}
                      src={thumb}
                      alt={item.title}
                      loading="lazy"
                      draggable={false}
                    />
                  ) : (
                    <div className={styles.thumbFallback}>
                      <PlayIcon />
                    </div>
                  )}

                  {/* Play button (stays visible on hover and on fallback) */}
                  

                  {isActive ? (
                    <>
                      <div className={styles.nowPlaying}></div>
                      <div className={styles.nowPlayingText}><span>NOW <br /> PLAYING</span></div>
                    </>
                  ):(
                    <button
                    className={styles.playBtn}
                    aria-label={`Play ${item.title}`}
                    onClick={(e) => {
                      e.stopPropagation();
                      onPlayClick?.(item, idx);
                    }}
                    type="button"
                  >
                    <PlayIcon />
                  </button>
                  )
                  }
                </div>

                <div className={styles.meta}>
                  <div className={styles.title} title={item.title}>
                    {item.title}
                  </div>
                  <div className={styles.views}>
                    <EyeIcon />
                    <span>{formatViews(getViewCount())}</span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </section>
    );
  }
);

export default VideoSection;
