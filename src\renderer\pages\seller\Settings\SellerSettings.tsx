import React, { useEffect, useState } from 'react';
import styles from './NewSetting.module.scss';
import SearchHeader from '../../SearchHeader';
import TabNavigation from './tabs/TabNavigation';
import CompanyTab from './tabs/CompanyTab';
import UserTab from './tabs/UserTab';
import ShipmentsTab from './tabs/ShipmentsTab';
import PaymentsTab from './tabs/PaymentsTab';
import NotificationsTab from './tabs/NotificationsTab';
import axios from 'axios';
import { formatCurrency, useGlobalStore, useSellerSettingStore } from '@bryzos/giss-ui-library';
import useGetBuyingPreference from 'src/renderer/hooks/useGetBuyingPreference';
import { yupResolver } from '@hookform/resolvers/yup';
import { set, useForm } from 'react-hook-form';
import { settingSchema } from './schemas';
import {
  formatPhoneNumberRemovingCountryCode,
  formatPhoneNumberWithHyphen,
} from 'src/renderer/helper';
import {
  defaultResaleCertificateLine,
  RecevingHoursFrom,
  RecevingHoursTo,
  sellerSettingTabConstants,
  userRole,
} from 'src/renderer/common';
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import useGetDeliveryAddress from 'src/renderer/hooks/useGetDeliveryAddress';
import useGetCompanyLists from 'src/renderer/hooks/useGetCompanyLists';
import { useLocation } from 'react-router-dom';
import useGetSellerSetting from 'src/renderer/hooks/useGetSellerSetting';
import DropdownSave from '../../buyer/newSettings/components/DropdownSave';
import clsx from 'clsx';
import { useAppStore } from 'src/renderer/store/AppStore';

const SellerSettings: React.FC<{
  mainWrapperRef: React.RefObject<HTMLDivElement>;
  routerContainerRef: React.RefObject<HTMLDivElement>;
}> = ({ mainWrapperRef, routerContainerRef }) => {
  const {
    register,
    handleSubmit,
    clearErrors,
    setError,
    setValue,
    reset,
    watch,
    control,
    getValues,
    trigger,
    resetField,
    formState: { errors, dirtyFields, isDirty, isValid },
    getFieldState,
  } = useForm({
    resolver: yupResolver(settingSchema),
    mode: 'onBlur',
  });

  const [activeTab, setActiveTab] = useState<string>('COMPANY');
  const location = useLocation();
  const [fadeLoaderMessage, setFadeLoaderMessage] = useState('');
  const setMainWindowWidth = useAppStore(state => state.setMainWindowWidth);
  const [saveFunctions, setSaveFunctions] = useState<{
    onSave: (() => void) | null;
    isDisabled: boolean;
  }>({
    onSave: null,
    isDisabled: true,
  });
  const {showFadeLoader} = useSellerSettingStore();
  const {appVersion} = useGlobalStore();
  const rendererDeployVersion = import.meta.env.VITE_RENDERER_DEPLOY_VERSION;


  useEffect(() => {
    if(showFadeLoader){
      setFadeLoaderMessage('Saving...');
    }else{
      if(fadeLoaderMessage === 'Saving...'){
        console.log("fadeLoaderMessage", fadeLoaderMessage)
        setFadeLoaderMessage('Saved');
      }
    }
  }, [showFadeLoader]);

  useEffect(() => {
    if(location?.state?.tab){
      setActiveTab(location?.state?.tab);
    }
  }, [location.state?.tab]);


  useEffect(() => {
    setMainWindowWidth(74.33)
    return () => {
      setMainWindowWidth(null)
    }
  }, [])

  

  const renderTabContent = () => {
    switch (activeTab) {
      case sellerSettingTabConstants.company:
        return (
          <CompanyTab
          setSaveFunctions={setSaveFunctions}
          locationState={location.state}
          />
        );
      case sellerSettingTabConstants.user:
        return (
          <UserTab 
          setSaveFunctions={setSaveFunctions}
          routerContainerRef={routerContainerRef}
          locationState={location.state}
          />
        );
      case sellerSettingTabConstants.shipments:
        return (
          <ShipmentsTab
          setSaveFunctions={setSaveFunctions}
          locationState={location.state}
          />
        );
      case sellerSettingTabConstants.payments:
        return (
           <PaymentsTab
            setSaveFunctions={setSaveFunctions}
            setActiveTab={setActiveTab}
            locationState={location.state}
            />
        );
      case sellerSettingTabConstants.notifications:
        return (
          <NotificationsTab 
          setSaveFunctions={setSaveFunctions}
          locationState={location.state}
          />
        );

      default:
        return (
          <CompanyTab
          setSaveFunctions={setSaveFunctions}
          locationState={location.state}
          />
        );
    }
  };

  return (
    <div className={styles.newSetting}>
      <div className={clsx(styles.newSettingContent, showFadeLoader && styles.fadeLoaderOpen)}>
      {Boolean(saveFunctions.onSave) && (
          <div className={styles.saveButtonContainer}>
            {
              (
                <DropdownSave
                  onSave={saveFunctions.onSave}
                  isDisabled={saveFunctions.isDisabled}
                />
              )
            }
            {
              <span className={clsx(styles.fadeLoaderMessage, fadeLoaderMessage === 'Saved' && styles.fadeLoaderMessageSaved)}>{fadeLoaderMessage}</span>
            }
            <div className={styles.settingTitle}>settings</div>
          </div>
        )}
        <TabNavigation activeTab={activeTab} setActiveTab={setActiveTab} />
        <div className={styles.tabContentContainer}>  
          {renderTabContent()}
        </div>
        <div className={styles.AppInfo}>
          <div>{appVersion} | {rendererDeployVersion}</div>
        </div>
      </div>
    </div>
  );
};

export default SellerSettings;
