.SelectDropdownPanel.SelectDropdownPanel {
    &.SelectDropdownBtmPanel {
        .MuiPaper-root {
            border-radius: 0px 0px 4px 4px;
            margin-top: 10px;
            margin-bottom: 0px;
        }
    }

    &.SelectExpirationState1{
        .MuiPaper-root {
            width: 96px;
            border-radius: 4px 4px 0px 0px;
            margin-bottom: 27px;
            margin-left: -9px;
        }
    }

    &.SelectExpirationState2{
        .MuiPaper-root {
            width: 96px;
            border-radius: 0px 0px 4px 4px;
            margin-top: 10px;
            margin-right: -10px;
        }
    }

    .MuiPaper-root {
        width: 60px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding: 6px;
        backdrop-filter: blur(24px);
        background-color: #ffffff4c;
        box-shadow: 0px 8px 30px #000000cc;
        margin-bottom: 34px;
        border-radius: 4px 4px 0px 0px;

        ul {
            padding: 0px;
            width: 100%;

            li {
                font-family: Inter;
                font-size: 12px;
                font-weight: normal;
                line-height: 1.4;
                text-align: left;
                color: #fff;
                padding: 4px 8px;
                justify-content: center;

                &:hover {
                    border-radius: 2px;
                    background-color: #fff;
                    color: #000;
                }
            }
        }
    }
}
