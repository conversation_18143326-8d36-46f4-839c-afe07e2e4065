/* VideoSection.module.scss */
/* Dark row of rounded cards, horizontal scroll, clean and compact. */

.section {
}

.header {
  margin-bottom: 16px;
  text-transform: uppercase;
  font-family: Syncopate;
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.3;
  letter-spacing: 0.56px;
  text-align: left;
  color: #fff;
}

.scroller {
  display: grid;
  grid-auto-flow: column;
  gap: 14px;
  overflow-x: auto;
  padding-bottom: 20px;
  &::-webkit-scrollbar {
    height: 6px;
  }
}

.card {
  overflow: hidden;
  width: 152px;
  height: 172px;
}

.thumbWrap {
  position: relative; 
  cursor: pointer;
  background: #3a3d46;
  height: 112px;
  width: 152px;
  border-radius: 8px;
}

.thumb {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  border-radius: 8px;
}

.thumbFallback {
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
}

.playBtn {
  position: absolute;
  top: 40px;
  left: 60px;
  cursor: pointer;
}

.nowPlaying {
  position: absolute;
  top: 0;
  border-radius: 8px;
  background-color: #c3c4ca;
  opacity: 0.5;
  width: 100%;
  height: 100%;
  span {
    font-family: Syncopate;
    font-size: 12px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: center;
    color: #0f0f14;
  }
}

.nowPlayingText {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  span {
    font-family: Syncopate;
    font-size: 12px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: center;
    color: #0f0f14;
  }
}

.meta {
  display: grid;
  gap: 6px;
  padding: 10px;
}

.title {
  font-family: Inter;
  font-size: 12px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.3;
  letter-spacing: 0.48px;
  text-align: left;
  color: #fff;
}

.views {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-family: Inter;
  font-size: 10px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: 0.4px;
  text-align: left;
  color: #71737f;
}

.eyeIcon {
  width: 16px;
  height: 16px;
  fill: currentColor;
}