import { isEmail } from "src/renderer/helper";
import * as yup from "yup";

export const schema = yup.object({
    userType: yup.string().trim().required('User Type is not valid'),
    companyName: yup.string().trim().required('Company Name is not valid'),
    companyEntity: yup.string().trim(),
    companyNameObj: yup.object(),
    firstName: yup.string().trim().required('First Name is not valid'),
    lastName: yup.string().trim().required('Last Name is not valid'),
    zipCode: yup.string().trim().required('Zip is not valid').min(5,'Zip is not valid'),
    emailAddress: yup.string().trim().required('Email is required').test('valid-emails', 'Please enter a valid email address', value => {
      if (!value) return true;
      return isEmail(value.trim());
    }),
    password: yup.string().trim()
      .required('Password is required')
      .min(8, 'Password must be at least 8 characters')
      .matches(/[A-Z]/, 'Password must contain at least 1 capital letter')
      .matches(/[0-9!@#$%^&*(),.?":{}|<>]/, 'Password must contain at least 1 number or symbol'),
    confirmPassword: yup.string().test("isRequired", "Password does not match!", function (value:string | undefined ) {
      const password = this.parent.password;
      if (password.trim() === value?.trim()) return true;
      return false;
   }),
    reEnterEmailAddress: yup.string().trim()
    // .required('Email does not matchhhh!')
    .email('Email is not valid')
    .test("isRequired", "Email does not match!", function(value) {
      const { emailAddress } = this.parent;
      if (!value) return false; // required
      if (!isEmail(value.trim())) return false; // must be valid email
      if (emailAddress?.trim() !== value.trim()) return false; // must match original
      return true;
      }),
})