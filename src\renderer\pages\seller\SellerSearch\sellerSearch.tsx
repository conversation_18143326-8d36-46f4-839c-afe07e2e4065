import React from 'react';
import styles from './sellerSearch.module.scss';
import clsx from 'clsx';
import { ReactComponent as SearchIcon } from '../../../assets/images/Search.svg';
import { useSellerOrderStore } from '@bryzos/giss-ui-library';

const SellerSearch: React.FC = () => {
  const {searchByProductOrZipValue, setSearchByProductOrZipValue} = useSellerOrderStore();

  return (
   <div className={styles.searchSection}>
                {/* <div className={styles.searchBox}>
                    <SearchIcon />
                    <input 
                      placeholder='Search by any Product or Location'
                      value={searchByProductOrZipValue} 
                      onChange={ e => setSearchByProductOrZipValue(e.target.value)}
                    />
                </div> */}
            </div>
  );
};

export default SellerSearch;
