// @ts-nocheck
import { useMutation, useQuery } from "@tanstack/react-query";
import axios from "axios";
import { reactQueryKeys } from "src/renderer/common";

const useGetAvailableCreditLimit = () => {
  return useMutation({
    mutationFn: async () => {
      try {
        const response = axios.get(import.meta.env.VITE_API_SERVICE + '/user/bryzos-available-credit-limit');
        return await response;
      } catch (error:any) {
        throw new Error(error?.message ?? error);
      }
  }
  });
};

export default useGetAvailableCreditLimit;
