.sellerListItemCard {
  padding: 12px 12px 29px 12px;
  cursor: pointer;
  margin-bottom: 2px;
  &:hover{
    background-color: #c3c4ca;
    .itemDescription{
      color: #0f0f14;
    }
    .price{
      color: #0f0f14;
    }
    .detailsContainer{
      color: #0f0f14;
    }
    .detailRow1{
      color: #0f0f14;
    }
    .iconButton{
      .deleteIcon{
        path{
          fill: #0f0f14;
        }
      }
      &:hover{
        path{
          fill: #0f0f14;
        }
      }
    }
    .shareIcon{
      path{
        fill: #0f0f14;
      }
      &:hover{
        path{
          fill: #0f0f14;
        }
      }
    }
    .resetIcon{
      path{
        fill: #0f0f14;
      }
      &:hover{
        path{
          fill: #0f0f14;
        }
      }
    }
  }
}

.headerRow {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  position: relative;
}

.itemDescription {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: 0.98px;
  text-align: left;
  color: #fff;
}

.price {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: 0.98px;
  text-align: left;
  color: #fff;
  display: inline-flex;
  gap: 8px;
  align-items: center;

  .orderTypeLetter {
    height: 16px;
    width: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-family: Syncopate;
    color: #fff;
    border-radius: 2px;

    &[data-letter="p"] {
      background-color: #32ff6c;
    }
    &[data-letter="c"] {
      background-color: #32ccff;
    }
    &[data-letter="o"] {
      background-color: #ff8c4c;
    }
  }
}

.actionIcons {
  display: flex;
  gap: 8px;
  align-items: center;
  position: absolute;
  top: 24px;
  right: 0;
}

.iconButton {
  &:hover {
    .deleteIcon {
      path{
        fill: #fff;
      }
  }
  .shareIcon {
    path{
      fill: #fff;
    }
  }
  .resetIcon {
    path{
      fill: #fff;
    }
  }
  }
}

.detailsContainer {
  display: flex;
  flex-direction: column;
  gap: 1px;
  font-family: Inter;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: 0.84px;
  text-align: left;
  color: #9b9eac;
}

.detailRow {
  display: flex;
  align-items: center;
}
.detailRow1 {
  display: flex;
  align-items: center;
  position: absolute;
  top: 21px;
  font-family: Inter;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: 0.84px;
  text-align: left;
  color: #9b9eac;
  .countdownTimer {
    color: rgba(50, 255, 108, 0.61);
  }
}

.location,
.weight,
.deliveryDate {
  font-size: 13px;
  color: #b0b0b0;
  line-height: 1.3;
  font-weight: 400;
}

.selectedOrder {
  background-color: #434449;
  &:hover{
    background-color: #434449;
    .itemDescription{
      color: #fff;
    }
    .price{
      color: #fff;
    }
    .detailsContainer{
      color: #fff;
    }
    .detailRow1{
      color: #fff;
    }
    .iconButton{
      .deleteIcon{
        path{
          fill: #fff;
        }
      }
      &:hover{
        path{
          fill: #fff;
        }
      }
    }
    .shareIcon{
      path{
        fill: #fff;
      }
      &:hover{
        path{
          fill: #fff;
        }
      }
    }
    .resetIcon{
      path{
        fill: #fff;
      }
      &:hover{
        path{
          fill: #fff;
        }
      }
    }
  }

  .detailsContainer {
    color: #fff;
  }

  .iconButton {
    .deleteIcon {
      path {
        fill: #fff;
      }
    }

    .shareIcon {
      path {
        fill: #fff;
      }
    }
    .resetIcon {
      path {
        fill: #fff;
      }
    }
  }
  .detailRow1 {
    color: #fff;
  }
}