.subscribePage {
  width: 800px;
  height: 1000px;
  background-image: linear-gradient(166deg, #0e0e11 -52%, #393e47 146%);
  background: url(../../assets/images/AppBG.svg) no-repeat;
  background-position: bottom;

  .subscriptionContainer {
    width: 704px;
    height: 666px;
    padding: 20px;
    border-radius: 16px;
    background: url(../../assets/images/Subscription-Content-BG.svg) no-repeat transparent;
    margin: 48px;
    position: relative;

    .countdownBanner {
      width: 664px;
      height: 36px;
      padding: 8px 16px;
      background-color: rgba(142, 164, 191, 0.34);
      border-radius: 16px 16px 0px 0px;
      font-family: Inter;
      font-size: 14.1px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: normal;
      letter-spacing: -0.28px;
      text-align: left;
      color: #d8d8d8;
    }

    .loadingContainer{
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      padding: 10px 8px;
      background-color: #000000;
      color: green;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1;
      font-weight: bold;
      font-size: 20px;
    }
  }
}


.subscriptionCard {
  width: 664px;
  height: 205px;
  background:url(../../assets/images/Subscribe_Bg.svg) no-repeat;

  .subscriptionHeader {
    padding: 16px;
    display: flex;
    gap: 48px;
    &.showUpdateAccountHeader{
      gap:30px;
    }

    .subscriptionHeaderTextContainer {
      display: flex;
      flex-direction: column;
      gap: 20px;
      width: 100%;

      .subscriptionHeaderText {
        font-family: Inter;
        font-size: 32px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: -1.6px;
        text-align: left;
        color: #8dffae;
      }

      .pricePerUser {
        opacity: 0.67;
        font-family: Syncopate;
        font-size: 18px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #fff;
        text-transform: uppercase;
      }

    }

    .userCalculator {
      display: flex;
      gap: 16px;

      .arrowIndicator {
        display: flex;
        align-items: center;
        height: 61px;
      }

      .userCount {
        width: 104px;
        display: flex;
        flex-direction: column;
        gap: 8px;
        .pricePerMonth {
          opacity: 0.67;
          font-family: Inter;
          font-stretch: normal;
          font-style: normal;
          text-align: right;
          color: #fff;
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          .price {
            font-size: 20px;
            font-weight: bold;
            letter-spacing: 1.4px;
          }
          .perMonth {
            font-size: 16px;
            font-weight: normal;
            letter-spacing: normal;
          }
        }
        .numberOfUsersInput {
          height: 62px;
          align-self: stretch;
          flex-grow: 0;
          display: flex;
          flex-direction: row;
          justify-content: flex-end;
          align-items: center;
          gap: 8px;
          padding: 0 12px;
          border-radius: 4px;
          background-color: #3f4753;
          width: 100%;
          font-family: Inter;
          font-size: 44px;
          font-weight: bold;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.2;
          letter-spacing: -0.88px;
          text-align: right;
          color: #fff;
          caret-color: #fff;

          &::-webkit-inner-spin-button,
          &::-webkit-outer-spin-button {
            -webkit-appearance: none;
            margin: 0;
          }

        }
      }
    }
  }
}

.updateAccount{
  height: 100%;
  display: flex;
  flex-direction: column;
  .updateAccountText {
    margin-bottom: 32px;
    .updateAccountPara{
      display: flex;
      align-items: center;
      span{
        opacity: 0.67;
        font-family: Inter;
        font-size: 16px;
        font-weight: normal;
        line-height: normal;
        text-align: left;
        color: #fff;
      }

      margin-bottom: 4px;
      display: inline-flex;
      &:last-child{
        margin-bottom: 0px;
      }
    }
    button{
      display: inline-flex;
      font-family: Inter;
      font-size: 12px;
      font-weight: 300;
      line-height: 1.41;
      letter-spacing: -0.24px;
      text-align: left;
      color: #8dffae;
      margin-left: 8px;
      svg{
        margin-right: 3px;
      }
    }
  }
 .updateAccountInputs{
  padding-right: 11px;
  overflow: auto;
  height: 100%;

  &::-webkit-scrollbar {
    width: 8px;
    height: 6px;
  }
  .inputWrapper{
    position: relative;
    &:last-child{
      input{
        margin-bottom: 0px;
      }
    }

  }
  input, .pendingEmailAddress {
    width: 100%;
    height: 36px;
    margin: 0 0 16px;
    padding: 9px 16px 8px 16px;
    border-radius: 5px;
    background-color: #3f4753;
    font-family: Inter;
    font-size: 16px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #fff;
    &::placeholder{
      opacity: 0.4;
      color: #fff;
    }
  }
  .pendingEmailAddress{
    font-style: italic;
    display: flex;
    flex-direction: row;
    cursor: pointer;
    span{
      opacity: 0.6;
    }
    .pendingEmailAddressSection{
      flex: 1;
      .pendingEmailAddressText{
        font-size: 12px;
        margin-left: 16px;
      }
    }
    .resendInviteButton{
      font-size: 12px;
      color: #8dffae;
      font-weight: 300;
      line-height: 1.41;
      letter-spacing: -0.24px;
      text-align: left;
      background-color: transparent;
      outline: none;
      border: none;
      cursor: not-allowed;
    }
  }


 }
}

.paymentInfoContainer {
  width: 100%;
  height: 54px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  row-gap: 2px;
  padding: 8px 16px;
  background-color: rgba(255, 255, 255, 0.04);
  p{
    font-family: Inter;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: -0.24px;
  text-align: left;
  color: #b3b3b3;
  }
}

.bottomContainer {
  width: 664px;
  height: 385px;
  padding: 24px 16px 30px;
  background-image: linear-gradient(to right, #393d44 -3%, #17171b 163%);
  border-radius: 0px 0px 16px 16px;
  overflow: hidden;

  .steps {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .stepItem {
      display: flex;
      gap: 12px;
      align-items: center;

      .stepHeader {
        flex: 0 auto;

        .stepNumber {
          font-family: Inter;
          font-size: 16px;
          font-weight: bold;
          font-stretch: normal;
          font-style: normal;
          line-height: normal;
          letter-spacing: -0.8px;
          text-align: left;
          color: #fff;
        }
      }

      .stepContent {
        flex: 1;

        .paymentDetailsContainer {
          display: flex;
          flex-direction: column;
          gap: 16px;

          .paymentDetails {
            display: flex;
            gap: 16px;

            input {
              border-radius: 5px;
              background-color: #3f4753;
              width: 276px;
              height: 36px;
              font-family: Inter;
              font-size: 16px;
              font-weight: normal;
              line-height: 1.2;
              letter-spacing: normal;
              text-align: left;
              color: #fff;
              border: none;
              padding: 0px 16px;
              caret-color: #fff;
              &::placeholder{
                color: #fff;
                opacity: 0.67;
              }
            }

            .emptyBlock {
              border-radius: 5px;
              background-color: #3f4753;
              width: 276px;
              height: 36px;
              font-family: Inter;
              font-size: 16px;
              font-weight: normal;
              font-stretch: normal;
              font-style: normal;
              line-height: 1.2;
              letter-spacing: normal;
              text-align: left;
              color: #fff;
              border: none;
              padding: 0px 16px;
            }
          }
        }
      }
    }

    .stepItem1 {
      align-items: flex-start;
      margin-top: 9px;
    }

    /* Style for Stripe Elements */
    & > div {
      flex: 1;
      min-height: 36px;
      display: flex;
      align-items: center;

      & > iframe {
        width: 100% !important;
        min-height: 40px !important;
      }
    }
  }

  .stripeElement {
    flex: 1;
    width: 276px;
    height: 36px;
    padding: 9px 16px 8px 16px;
    border-radius: 5px;
    background-color: #3f4753;
    display: flex;
    align-items: center;

    & > div {
      width: 100%;
    }

    /* Extra styling to ensure the iframe is visible */
    iframe {
      opacity: 1 !important;
      height: 24px !important;
      min-height: 24px !important;
      width: 100% !important;
    }
  }
}

.stepHeaderText {
  font-family: Inter;
  font-size: 16px;
  font-weight: normal;
  line-height: 1.2;
  letter-spacing: normal;
  text-align: left;
  color: #fff;
  margin-bottom: 24px;
  padding-left: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  span{
    opacity: 0.67;
    padding-top: 1px;
  }
  &.agreementTitleMain{
    opacity: unset;
  }
}

.agreementLink.agreementLink {
  font-family: Inter;
  font-size: 12px;
  font-weight: normal;
  font-style: italic;
  line-height: 1.41;
  letter-spacing: -0.24px;
  text-align: left;
  color: #ffa341;
  cursor: pointer;
  opacity: unset;
}

.authorization {
  display: flex;

  .checkboxContainer {
    width: 16px;
    display: block;
    position: relative;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    &:focus{
      outline: 1px solid #ffa341;
    }
  }

  input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;

    &:checked ~ .checkmark {
      &:after {
        display: block;
      }
    }
  }

  .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 16px;
    width: 16px;
    border-radius: 0.7px;
    border: 3px solid #4d5664;
    background-color: rgba(255, 255, 255, 0.76);
    &:focus{
      outline: 1px solid #ffa341;
    }

    &:after {
      content: "";
      position: absolute;
      display: none;
      left: 3px;
      top: 0px;
      width: 3px;
      height: 7px;
      border: solid #0f0f14;
      border-width: 0 1.6px 1.6px 0;
      -webkit-transform: rotate(45deg);
      -ms-transform: rotate(45deg);
      transform: rotate(45deg);
    }
  }

  .lblChk {
    font-family: Inter;
    font-size: 12px;
    font-weight: normal;
    line-height: 1.41;
    letter-spacing: 0.84px;
    text-align: left;
    color: #d8d8d8;
    padding-left: 10px;
  }
}

/* Verify Bank Button Styles */
.verifyBankButtonContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 16px;
  width: 100%;

  .verifyBankButton {
    padding: 10px 20px;
    background-color: #4a90e2;
    color: white;
    border: none;
    border-radius: 4px;
    font-family: Inter, sans-serif;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #3a7bc8;
    }

    &:focus {
      outline: 2px solid #8dffae;
    }

    &.disabled, &:disabled {
      background-color: #a0a0a0;
      cursor: not-allowed;
      opacity: 0.7;

      &:hover {
        background-color: #a0a0a0;
      }
    }
  }

  .verifyBankInfo {
    margin-top: 8px;
    font-family: Inter, sans-serif;
    font-size: 12px;
    color: #b3b3b3;
    font-style: italic;
  }
}

.paymentMethodSelect.paymentMethodSelect {
  // height: 100%;
  width: 100%;
  padding: 6.5px 12px 6.5px 16px;
  border-radius: 5px;
  background-color: #3f4753;

  div:nth-child(1) {
    padding: 0px;
    font-family: Inter;
    font-size: 16px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.2;
    letter-spacing: normal;
    text-align: left;
    color: #fff;
    display: flex;
    align-items: center;

    span {
      opacity: 0.67;
    }
  }

  .MuiSelect-icon {
    right: 1px;
    top: unset;
    transform: unset;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
  }

  svg {
    color: #fff;
    right: 13px;
    transform: rotate(180deg);
    top:unset
  }

  fieldset {
    border: 0px;
  }
}

.menuSlideUp {
  animation: slideUp 250ms ease-out forwards;
  transform-origin: top center;
}

.dropdownList {
  animation: slideUp 250ms ease-out forwards;
  transform-origin: top center;
}

.dropdown{
  &.dropdownOpen{
    border-radius: 5px;
    box-shadow: 0 0 16px 4px rgba(0, 0, 0, 0.4);
    background-color: #3f4753;
    height: 192px;
    position: absolute;
    width: 574px;
    top: -18px;
    z-index: 999;
  }
}

.dropdownContent{
  position: relative;
}

.dropdownList.dropdownList {
  padding: 4px;
  border-radius: 0px;
  background-color: #3f4753;
  max-width: 100%;
  box-shadow: none;

  .muiMenuList {
    padding: 16px;
    z-index: 2;

    li {
      margin-bottom: 15px;
      padding: 9px 24px;
      height: 38px;
      width: 100%;
      font-family: Inter;
      font-size: 16px;
      font-weight: normal;
      line-height: normal;
      text-align: left;
      color: #fff;
      background-color: transparent;
      &:last-child{
        margin-bottom: 0px;
      }

      &:hover {
        font-weight: 600;
        border-radius: 5px;
        background-color: #6b7581;
      }

      &:focus {
        font-weight: 600;
        border-radius: 5px;
        background-color: #6b7581;
      }
    }
  }
}


.accountTypeDropdown{
  position: relative;
  width: 100%;
  flex: 1;
  .dropAccountTypeOpen{
    border-radius: 5px;
    box-shadow: 0 0 16px 4px rgba(0, 0, 0, 0.4);
    background-color: #3f4753;
    height: 150px;
    position: absolute;
    width: 100%;
    top: 0px;
    z-index: 999;
  }
}
.dropdownList.dropdownList {
  &.dropListAccountType{
    .muiMenuList {
      padding: 12px;

      li {
        margin-bottom: 8px;
        padding: 5px 16px;
        height: 30px;
        font-size: 14px;
      }
    }
  }

}

.subscribeButton {
  width: 704px;
  height: 70px;
  border-radius: 10.8px;
  background: url(../../assets/images/Subscription-Button-BG.svg) no-repeat transparent;
  margin: 0px 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  &.disabled {
    cursor: not-allowed;
    span {
      opacity: 0.2;
    }
  }
  span {
    font-family: Syncopate;
    font-size: 32px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.2;
    letter-spacing: 1.28px;
    text-align: left;
    color: #fff;
  }
}

.agreementTitleTooltip.agreementTitleTooltip {
  width: 100%;
  max-width: 660px;
  height: 139px;
  padding:0;
  border-radius: 5px;
  border-style: solid;
  border-width: 0.9px;
  border-color: #707b89 ;
  background-image: linear-gradient(to bottom, #707b89, #707b89), linear-gradient(343deg, #fff 114%, #17171b 42%);
  background-origin: border-box;
  background-color: transparent;
  left:13.5px;

  .tooltipArrow.tooltipArrow{
    color: #707b89;
    width: 26px;
    height: 15px;
    margin-bottom: -16px;
    left: 37px !important;
  }
  .debitAuthorizationAgreement {
    padding: 20px 14px 8px 26px;

    .debitAuthAgreementTitle {
      font-family: Inter;
      font-size: 16px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.41;
      letter-spacing: -0.32px;
      text-align: left;
      color: #ffa341;
      margin-bottom: 8px;
    }

    p {
      font-family: Inter;
      font-size: 12px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.41;
      letter-spacing: 0.84px;
      text-align: left;
      color: #fff;
    }


  }
}

.errorEmail{
  input{
    background-color: red !important;
    color: #ffff !important;
  }
}
.errorMessageDomain{
  font-family: Inter;
  font-size: 12px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.41;
  letter-spacing: -0.24px;
  text-align: left;
  color: #fff;
  position: absolute;
  right: 10px;
  top: 11px;
}


.pastDueText{
  p{
    font-family: Inter;
    color: red;
    font-family: Inter;
    font-size: 16px;
    font-weight: normal;
    line-height: normal;
    text-align: left;
  }
}