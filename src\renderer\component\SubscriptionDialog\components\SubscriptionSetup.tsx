import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { useStripe, useElements, CardNumberElement } from '@stripe/react-stripe-js';
import PaymentForm from './PaymentForm';
import { getPaymentSchema } from '../schemas/paymentSchemas';
import styles from '../SubscriptionDialog.module.scss';
import { useBuyerSettingStore, useGlobalStore, useSubscriptionStore } from '@bryzos/giss-ui-library';
import { commomKeys, PAYMENT_METHODS, reactQueryKeys, routes, SUBSCRIPTION_STATUS } from 'src/renderer/common';
import useDialogStore from '../../DialogPopup/DialogStore';
import usePostUserSubscription from 'src/renderer/hooks/usePostUserSubscription';
import usePostBuyerSettingsPayment from 'src/renderer/hooks/usePostBuyerSettingsPayment';
import { useQueryClient } from '@tanstack/react-query';
import usePostUpdateSubscribePayment from 'src/renderer/hooks/usePostUpdateSubscribePayment';
import usePostUpdateSubscribeAccount from 'src/renderer/hooks/usePostUpdateSuscribeAccount';
import { calculateSubscriptionAmount, formatDateForDisplay, validateAccountNumber, validateRoutingNumber } from 'src/renderer/helper';
import InputWrapper from '../../InputWrapper';
import CustomTextField from '../../CustomTextField';
import usePutUpdateUserSubscription from 'src/renderer/hooks/usePutUpdateUserSubscription';
import { ReactComponent as SubscriptionArrowIcon } from '../../../assets/images/subscriptionArrow.svg';

import { useNavigate } from 'react-router-dom';
import useMutateGetCassAccessToken from 'src/renderer/hooks/useMutateGetCassAccessToken';
import useCreateCassSupplier from 'src/renderer/hooks/useCreateCassSupplier';
import usePostCassResponse from 'src/renderer/hooks/usePostCassResponse';
import TrueVaultClient from 'truevault';
import axios from 'axios';
import clsx from 'clsx';

const SubscriptionSetup = ({ currentMode, onSuccess }: { currentMode: string | undefined, onSuccess: (content: React.ReactNode) => void }) => {
  const stripe = useStripe();
  const elements = useElements();
  const { userSubscription, isFromSetting , subscriptionsPricing , closeSubscriptionDialog } = useSubscriptionStore();
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
console.log('isFromSetting', isFromSetting)
  const {
    mutateAsync: updateUserPayment
  } = usePostUpdateSubscribePayment();
  const {
    mutateAsync: updateUserAccount
  } = usePostUpdateSubscribeAccount();

  // Card completion state
  const [cardComplete, setCardComplete] = useState({
    cardNumber: false,
    cardExpiry: false,
    cardCvc: false
  });

  // Card error state
  const [cardError, setCardError] = useState<{
    cardNumber: string | null;
    cardExpiry: string | null;
    cardCvc: string | null;
  }>({
    cardNumber: null,
    cardExpiry: null,
    cardCvc: null
  });

  const [stripeError, setStripeError] = useState<string | null>(null);
  const [isCardDetailsRequired, setIsCardDetailsRequired] = useState(false);
  const { userData, setShowLoader , referenceData} = useGlobalStore();
  const { showCommonDialog, resetDialogStore } = useDialogStore();
  const {buyerSetting}: any = useBuyerSettingStore();
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  const isEditLicenseModule = currentMode === 'EDIT_LICENSES';
  const isEditPaymentModule = currentMode === 'EDIT_PAYMENT';

  // Create form with dynamic schema
  const methods = useForm<any>({
    resolver: yupResolver(getPaymentSchema(selectedPaymentMethod)),
    defaultValues: {
      numberOfLicenses: 1,
      paymentMethod: '',
    },
    mode: 'onSubmit',
  });

  const { handleSubmit, watch, setValue, setError, register, formState: { errors, isValid, isDirty, dirtyFields } } = methods;
  const numberOfLicenses = watch('numberOfLicenses');
  const [monthlyPrice, setMonthlyPrice] = useState(0);
  const {setSubscriptionDialogOpen , subscriptionDialogOpen} = useSubscriptionStore();
  const {
    mutateAsync: postSaveUserSubscription
  } = usePostUserSubscription();

  const {
    mutateAsync: buyerSettingsPayment
  } = usePostBuyerSettingsPayment();

  const {
    mutateAsync: putUpdateUserSubscription
  } = usePutUpdateUserSubscription();


  const {
    mutateAsync: getCassAccessToken
  } = useMutateGetCassAccessToken();

  const {
    mutateAsync: createCassSupplier,
    data: createCassSupplierData,
    error: createCassSupplierError,
    isLoading: isCreateCassSupplierLoading,
  } = useCreateCassSupplier();

  
  const {
    mutateAsync: postMutateCassResponse,
    data: postCassResponseData,
    error: postCassResponseError,
    isLoading: isPostCassResponseLoading,
  } = usePostCassResponse();



  useEffect(() => {
    if (userSubscription?.error_message) {
      // setShowUpdateAccount(false);
    } else if (userSubscription && subscriptionsPricing) {
      // const pricingData = calculateSubscriptionAmount(Number(userSubscription?.total_license), subscriptionsPricing);
      const pricingData = subscriptionsPricing;
      const nextPaymentCyclePrice = Number(pricingData?.price_amount)* Number(userSubscription?.licenses?.current_total);
      // const isAccountPending = (userSubscription?.status !== SUBSCRIPTION_STATUS.BANK_VERIFICATION_PENDING);
      if (userSubscription?.subscription_id) {
        // setShowUpdateAccount(isAccountPending && userSubscription?.status !== SUBSCRIPTION_STATUS.CANCELLED);
        // setIsUpdatePaymentModule(!isAccountPending);
        setValue('numberOfLicenses', userSubscription?.licenses?.current_total);
        setValue('existingNumberOfLicenses', userSubscription?.licenses?.current_total);
        setValue('nextPaymentCyclePrice', nextPaymentCyclePrice);
        if (userSubscription?.subscribed_emails?.length > 0) {
          setValue('emailAddress', userSubscription?.subscribed_emails);
        }
        setValue('agreeToTerms', !!userSubscription?.subscription_id);
        setValue('nextPaymentCycleDay', userSubscription?.billing?.next_billing_date);
        setValue('bankNameOrCardBrand', buyerSetting?.card?.card_display_brand ?? buyerSetting?.card?.bank_name);
      } else {
        // setShowUpdateAccount(false);
        // setIsUpdatePaymentModule(false);
        setValue('paymentMethod', '');
        setValue('numberOfLicenses', 1);
        setValue('existingNumberOfLicenses', 1);
        setValue('nextPaymentCyclePrice', 0);
        setValue('nextPaymentCycleDay', 0);
      }
      // setSubscriptionStatus(null);
    }
  }, [userSubscription, subscriptionsPricing]);

  useEffect(() => {
    if (subscriptionsPricing) {
      // const pricingData = calculateSubscriptionAmount(Number(numberOfLicenses), subscriptionsPricing);
      const pricingData = subscriptionsPricing;
      if (pricingData) {
        setMonthlyPrice(Number(pricingData.price_amount));
      }
    }
  }, [numberOfLicenses, subscriptionsPricing]);

  useEffect(() => {
    const price = (numberOfLicenses * monthlyPrice).toFixed(2);
    setValue('perUserPrice', price ?? '0.00');
  }, [numberOfLicenses, monthlyPrice])


  useEffect(() => {
    if(watch('paymentMethod') === PAYMENT_METHODS.ACH){
      if(!buyerSetting?.company_address?.line1 || !buyerSetting?.phone){
        let message = 'Please update your company address and phone number';
        let redirectTo = 'COMPANY'
        if(buyerSetting?.company_address?.line1){
          message = 'Please update your phone number';
          redirectTo = 'USER';
        }
        showCommonDialog(null, message, null, handleUpdateAddressCancel, [{ name: 'Go to Settings', action: () => handleGoToSettings(redirectTo) } ,{ name: 'Cancel', action: handleUpdateAddressCancel }]);
      }
    }
  }, [watch('paymentMethod'), buyerSetting])

  const handleUpdateAddressCancel = () => {
    setValue('paymentMethod', '');
    setSelectedPaymentMethod('');
    resetDialogStore();
  }

  const handleGoToSettings = (redirectTo: string) => {
    navigate(routes.buyerSettingPage, { state: { tab: redirectTo } })
    resetDialogStore();
    setSubscriptionDialogOpen(false);
  }



  const isCardFormComplete = useMemo(() => {
    if (watch('paymentMethod') === PAYMENT_METHODS.CARD) {
      let commonValidation = false
      const isValidCardDetails = !cardError.cardNumber && !cardError.cardExpiry && !cardError.cardCvc && cardComplete.cardNumber && cardComplete.cardExpiry && cardComplete.cardCvc
      if (!cardComplete.cardNumber &&
        !cardComplete.cardExpiry &&
        !cardComplete.cardCvc && !cardError.cardNumber && !cardError.cardExpiry && !cardError.cardCvc && ((!userSubscription?.subscription_id && !!buyerSetting?.card) || (userSubscription?.status === SUBSCRIPTION_STATUS.CANCELLED))) {
        return true;
      }
      if (isEditLicenseModule || isEditPaymentModule) {
        const isUserProfileUpdated = (
          buyerSetting?.card?.first_name !== watch('cardholderFirstName') ||
          buyerSetting?.card?.last_name !== watch('cardholderLastName') ||
          buyerSetting?.card?.zipcode !== watch('billingZipCode') || userSubscription?.licenses?.current_total !== watch('numberOfLicenses') || watch('paymentMethod') !== userSubscription.billing.payment_method
        )
        commonValidation = (isCardDetailsRequired ?
          (!stripeError && !cardError.cardNumber && !cardError.cardExpiry && !cardError.cardCvc && ((!cardComplete.cardCvc && !cardComplete.cardExpiry && !cardComplete.cardNumber && isUserProfileUpdated) || (cardComplete.cardCvc && cardComplete.cardExpiry && cardComplete.cardNumber)))
          :
          isUserProfileUpdated
        )
      }
      else {
        commonValidation = (isValidCardDetails &&
          !stripeError)
      }
      return (commonValidation &&
        !!watch('cardholderFirstName') &&
        !!watch('cardholderLastName') &&
        !!watch('billingZipCode') &&
        /^\d{5}$/.test(watch('billingZipCode') || '')
      )
    }
  }, [
    watch('paymentMethod'),
    cardComplete.cardNumber,
    cardComplete.cardExpiry,
    cardComplete.cardCvc,
    stripeError,
    watch('cardholderFirstName'),
    watch('cardholderLastName'),
    watch('billingZipCode'),
    isCardDetailsRequired,
    userSubscription,
    cardError,
    cardError.cardNumber,
    cardError.cardExpiry,
    cardError.cardCvc,
    cardComplete.cardNumber,
    cardComplete.cardExpiry,
    cardComplete.cardCvc,
    isDirty,
    stripeError
  ]);

  const isAchFormValid = useMemo(() => {
    if (watch('paymentMethod') !== PAYMENT_METHODS.ACH) return false;

    const routingNumber = watch('routingNumber');
    const accountNumber = watch('accountNumber');
    const reEnterAccountNumber = watch('reEnterAccountNumber');
    const accountName = watch('accountName');
    const bankName = watch('bankName');
    const accountType = watch('accountType');

    // Check if all required fields are filled and valid
    if(isEditLicenseModule || isEditPaymentModule){
        const isNewAccount = ((!!watch('accountNumber') && watch('accountNumber')?.slice(-4) !== userSubscription?.account_number) 
            || 
            userSubscription?.routing_number !== watch('routingNumber'));
        if(!isNewAccount && userSubscription?.status === SUBSCRIPTION_STATUS.BANK_VERIFICATION_PENDING){
            return true;
        }
        if(isNewAccount){
            return (
                routingNumber &&
                validateRoutingNumber(routingNumber) &&
                accountNumber &&
                validateAccountNumber(accountNumber) &&
                reEnterAccountNumber &&
                accountNumber === reEnterAccountNumber &&
                accountName &&
                bankName &&
                accountType
            );
        }
        else if(watch('accountName') !== userSubscription?.account_name || watch('bankName') !== userSubscription?.bank_name || watch('accountType') !== userSubscription?.account_type.toLowerCase()){
            return (
                accountName &&
                bankName &&
                accountType
            );
        }
        return false;
    }
    else{
        return (
            routingNumber &&
            validateRoutingNumber(routingNumber) &&
            accountNumber &&
            validateAccountNumber(accountNumber) &&
            reEnterAccountNumber &&
            accountNumber === reEnterAccountNumber &&
            accountName &&
            bankName &&
            accountType
        );
    }
    
}, [watch, validateRoutingNumber, validateAccountNumber, userSubscription]);


  const isSubmitEnabled = useMemo(() => {
    if (watch('paymentMethod') === PAYMENT_METHODS.CARD) {
      return isCardFormComplete;
    }else if (watch('paymentMethod') === PAYMENT_METHODS.ACH) {
      return isDirty || userSubscription?.billing?.payment_method !== watch('paymentMethod')
    }else if (currentMode === 'EDIT_LICENSES') {
      return userSubscription?.licenses?.current_total !== watch('numberOfLicenses')
    } 
  }, [isCardFormComplete, watch('paymentMethod'), userSubscription?.total_license, watch('numberOfLicenses') , isDirty]);

  const handlePaymentMethodChange = (method: string) => {
    setSelectedPaymentMethod(method);
    setValue('paymentMethod', method);
    // Reset form when payment method changes

    if (method === PAYMENT_METHODS.CARD) {
      initializeCardForm();
    } else {
      initializeAchForm();
    }
    // Reset card states when payment method changes
    setCardComplete({
      cardNumber: false,
      cardExpiry: false,
      cardCvc: false
    });
    setCardError({
      cardNumber: null,
      cardExpiry: null,
      cardCvc: null
    });
    setStripeError(null);
    setIsCardDetailsRequired(false);
  };

  const handleCardChange = useCallback((event: any, fieldName: keyof typeof cardComplete) => {
    setIsCardDetailsRequired(true);
    setCardComplete(prev => ({
      ...prev,
      [fieldName]: event.complete
    }));

    // Set or clear error based on the event
    if (event.error || (!event.empty && !event.complete)) {
      setCardError(prev => ({
        ...prev,
        [fieldName]: event.error?.message ?? 'Incomplete card details'
      }));
    } else {
      setCardError(prev => ({
        ...prev,
        [fieldName]: null
      }));
      setStripeError(null);
    }
  }, []);

  const initializeCardForm = () => {
    setValue('cardholderFirstName', buyerSetting?.card?.first_name);
    setValue('cardholderLastName', buyerSetting?.card?.last_name);
    setValue('billingZipCode', buyerSetting?.card?.zipcode);
    setValue('cardNumberLast4Digits', buyerSetting?.card?.card_number_last_four_digits);
    setValue('cardExpiry', buyerSetting?.card?.expiration_date);
    setValue('email_id', buyerSetting?.card?.email_id || userData?.data?.email_id);
  }

  const initializeAchForm = () => {
    setValue('accountName', buyerSetting?.ach_debit?.account_name);
    setValue('routingNumber', buyerSetting?.ach_debit?.routing_number);
    setValue('accountNumber', buyerSetting?.ach_debit?.account_number);
    setValue('accountType', buyerSetting?.ach_debit?.account_type?.toLowerCase());
    setValue('bankName', buyerSetting?.ach_debit?.bank_name);
    setValue('last4AccountNumber', buyerSetting?.ach_debit?.account_number);
    setValue('reEnterAccountNumber', buyerSetting?.ach_debit?.account_number);
  }

  const achSubmit = async (data: any) => {
    try {
      setShowLoader(true);
      console.log("data", data)
      const cassSubmitResponse = await cassSubmit(data);
      if(cassSubmitResponse){
        const truevaultData = await getTruevaultData(buyerSetting?.company_name, buyerSetting?.user_id, data.bankName, data.routingNumber, data.accountNumber, data.accountType);
        if(truevaultData){
        let referenceCassSubscribePrefix = null;
        const obj = referenceData.ref_general_settings.find(
          (obj: any) => obj.name === "CASS_USER_SUBSCRIPTION_UNIQUE_IDENTIFIER_PREFIX"
        );
        referenceCassSubscribePrefix = obj?.value ?? "";
        const uniqueIdentifier = referenceCassSubscribePrefix + (buyerSetting?.company_id);
        const convertedRoutingNo = data.routingNumber.slice(-4).padStart(data.routingNumber.length, 'x');
        const convertedAccountNO = data.accountNumber.slice(-4).padStart(data.accountNumber.length, 'x');
        const buyerSettingPaymetPayload = {
          "data": {
            "payment_method": PAYMENT_METHODS.ACH,
            "payment_details": {
              "bank_name": data.bankName,
              "account_name": data.accountName,
              "routing_number": convertedRoutingNo,
              "account_number": convertedAccountNO,
              "account_type": data.accountType,
              "cass_unique_id": uniqueIdentifier,
              "truevault_document_id": truevaultData
            }
            }
          }
          const buyerSettingsPaymentResponse = await buyerSettingsPayment(buyerSettingPaymetPayload);
          console.log("buyerSettingsPaymentResponse", buyerSettingsPaymentResponse)

          if(buyerSettingsPaymentResponse){
            const subscriptionPayload = {
              "data": {
                "total_licenses": Number(data.numberOfLicenses) || undefined,
                "payment_method": PAYMENT_METHODS.ACH,
                "plan_id": subscriptionsPricing?.id,
              }
            }
            if(!(isEditLicenseModule || isEditPaymentModule)){
              const response = await postSaveUserSubscription(subscriptionPayload);
              console.log("response", response)
            }
          }
        }

      }
    } catch (error) {      
      if(error?.message){
        try {
          // Parse the error message if it's a JSON string
          let errorMessages = error.message;
          if (typeof error.message === 'string' && error.message.startsWith('[')) {
            const parsedErrors = JSON.parse(error.message);
            if (Array.isArray(parsedErrors)) {
              // Extract error messages and join with line breaks
              errorMessages = parsedErrors
                .map(err => err.errorMessage)
                .join('\n');
            }
          }
          showCommonDialog(null, errorMessages, null, resetDialogStore, [{ name: 'OK', action: resetDialogStore }]);
        } catch (parseError) {
          // Fallback to original error message if parsing fails
          showCommonDialog(null, error.message, null, resetDialogStore, [{ name: 'OK', action: resetDialogStore }]);
        }
      }
      else{
        showCommonDialog(null,error?.message ?? 'Something went wrong', null, resetDialogStore, [{ name: 'OK', action: resetDialogStore }]);
      }
      throw error;
      setShowLoader(false);
    } finally {
      setShowLoader(false);
    }
  }

  const cassSubmit = async (data: any) => {
    try {
      if (referenceData) {
        let referenceCassSubscribePrefix = null;
        const obj = referenceData.ref_general_settings.find(
          (obj: any) => obj.name === "CASS_USER_SUBSCRIPTION_UNIQUE_IDENTIFIER_PREFIX"
        );
        referenceCassSubscribePrefix = obj?.value ?? "";
        const uniqueIdentifier = referenceCassSubscribePrefix + (buyerSetting?.company_id);

        let payload = {
          clientKey: import.meta.env.VITE_CASS_CLIENT_KEY, // from env
          uniqueIdentifier: uniqueIdentifier, // max length=50, min=3
          uniqueGroupIdentifier: buyerSetting?.user_id, // max length=50, min=0
          masterDataType: 'Buyer', // max length=8, min=0
          masterDataName: buyerSetting?.company_name?.substring(0, 40), // max length=40, min=0
          phoneNumber: buyerSetting?.phone, // max length=20, min=0
          primaryContactName: buyerSetting?.first_name + ' ' + buyerSetting?.last_name, // max length=40, min=0
          primaryContactEmailAddress: buyerSetting?.email_id, // max length=50, min=0
          remitEmailAddresses: "<EMAIL>", // max length=200, min=0
          address1: buyerSetting?.company_address?.line1?.substring(0, 40), // max length=40, min=0
          address2: buyerSetting?.company_address?.line2 ? buyerSetting?.company_address?.line2?.substring(0, 40) : null, // max length=40, min=0
          city: buyerSetting?.company_address?.city?.substring(0, 40), // max length=40, min=0
          stateOrProvince: buyerSetting?.company_address?.state_code, // max length=2, min=0
          zipOrPostalCode: buyerSetting?.company_address?.zip, // max length=10, min=0
          drawdownMethod: "04", //= 90 (Internal transfer) 03 (ACH Credit) and 04 (ACH Debit)
          drawdownTransactionCode: data.accountType === 'checking' ? "27" : "37", // “27” for crediting a checking account or “37” for crediting a saving account.
          drawdownRoutingNumber: data.routingNumber, // max=9 min=0
          drawdownAccountNumber: data.accountNumber, // max=17 min=0
          refundMethod: "" // max=9 min=0
        };
        console.log("payload", payload)
        const cassAccessToken = await getCassAccessToken();
        console.log("cassAccessToken", cassAccessToken)
        const createCassSupplierResponse = await createCassSupplier({ data: payload, accessToken: cassAccessToken });
        console.log("createCassSupplierResponse", createCassSupplierResponse)
        if(createCassSupplierResponse){
          const postCassResponse = await postMutateCassResponse({ data: { cass_unique_id: createCassSupplierResponse } });
          console.log("postCassResponse", postCassResponse)
        }
        return true;
      }
    } catch (error) {
      throw error;
    }
  }


  const getTruevaultData = async (companyName, userData, bankName, routingNo, accountNo, accountType) => {
    try {
        const res = await axios.get(import.meta.env.VITE_API_SERVICE + '/user/getAccessToken');
        const accessToken = res.data.data;
        const sellerPaymentData = {
            "document": {
                "company_name": companyName,
                "user_id": userData,
                "bank_name": bankName,
                "routing_number": routingNo,
                "account_number": accountNo,
                "account_type": accountType
            }
        }

        const client = new TrueVaultClient({ accessToken });

        try {
            const response = await client.createDocument(import.meta.env.VITE_TRUE_VAULT_ID_SELLER_VAULT_ID, null, sellerPaymentData)
            const documentIdFromTruevault = response.id;
            return documentIdFromTruevault;

        } catch(error) {
            console.error(error);
            setShowLoader(false);
        }
    }  catch(error) {
        console.error(error);
        setShowLoader(false);
    }
  }


  const handleUpdateAccount = async (data: any) => {
    try {
      const payload = {
        "data": {
          "new_total_licenses": Number(data.numberOfLicenses) || undefined,
          "payment_method":data.paymentMethod || undefined,
        }
      }
      const response = await putUpdateUserSubscription(payload);
      return response;
    } catch (err) {
      showCommonDialog(null, 'Something went wrong', null, resetDialogStore, [{ name: 'OK', action: resetDialogStore }]);
      setShowLoader(false);
    } finally {
      setShowLoader(false);
    }
  }


  const isAchChanged  = () => {
    let accountName = watch('accountName');
    let routingNumber = watch('routingNumber');
    let bankName = watch('bankName');
    let accountNumber = watch('accountNumber');
    let reEnterAccountNumber = watch('reEnterAccountNumber');
    let accountType = watch('accountType');

    let savedAccountDetails = buyerSetting?.ach_debit;
    if(savedAccountDetails){
      if(accountName !== savedAccountDetails?.account_name ||
         routingNumber !== savedAccountDetails?.routing_number
          || bankName !== savedAccountDetails?.bank_name || 
          accountNumber !== savedAccountDetails?.account_number
           || reEnterAccountNumber !== savedAccountDetails?.account_number
            || accountType !== savedAccountDetails?.account_type.toLowerCase()){
        return true;
      }
    }else{
      return true;
    }
    return false;
  }

  const onSubmit = async (data: any) => {
    setShowLoader(true);
    const { current_total: currentTotalLicenses } = userSubscription?.licenses || {};
    const { payment_method: currentPaymentMethod } = userSubscription?.billing || {};
    let updatedPaymentDetails = false;

    if (data.paymentMethod === PAYMENT_METHODS.CARD && isCardFormComplete) {
      if (!stripe || !elements) {
        setStripeError("Stripe hasn't loaded yet. Please try again.");
        setShowLoader(false);
        return;
      }

      // Check if card details are complete
      if ((!buyerSetting?.card?.card_number_last_four_digits) && (!cardComplete.cardNumber || !cardComplete.cardExpiry || !cardComplete.cardCvc)) {
        setStripeError("Please complete all card details.");
        setShowLoader(false);
        return;
      }

      try {
        let _paymentMethod;
        const cardElement = elements.getElement(CardNumberElement);
        if (cardComplete.cardNumber && cardComplete.cardExpiry && cardComplete.cardCvc) {
          const { paymentMethod, error: paymentMethodError } = await stripe.createPaymentMethod({
            type: 'card',
            card: cardElement,
            billing_details: {
              email: data.email_id,
              name: `${data.cardholderFirstName} ${data.cardholderLastName}`,
              address: {
                country: 'US',
                postal_code: data.billingZipCode,
              }
            }
          });
          if (paymentMethodError) {
            setStripeError(paymentMethodError.message || 'An error occurred during payment processing');
            setShowLoader(false);
            return;
          }
          else {
            _paymentMethod = paymentMethod;
          }
        }

        const payload = {
          "data": {
            "total_licenses": Number(data.numberOfLicenses) || undefined,
            "payment_method": PAYMENT_METHODS.CARD,
            "plan_id": subscriptionsPricing?.id,
          }
        }
        const paymentSettingPayload = {
          "data": {
            "payment_method": PAYMENT_METHODS.CARD,
            "payment_details": {
              "zipcode": data.billingZipCode,
              "payment_method_id": _paymentMethod ? _paymentMethod.id : undefined,
              "first_name": data.cardholderFirstName,
              "last_name": data.cardholderLastName
            }
          }
        }

        const buyerSettingsPaymentResponse = await buyerSettingsPayment(paymentSettingPayload);
        if (buyerSettingsPaymentResponse.error_message) {
          setStripeError(buyerSettingsPaymentResponse.error_message);
          showCommonDialog(null, buyerSettingsPaymentResponse.error_message, null, resetDialogStore, [
            { name: commomKeys.errorBtnTitle, action: resetDialogStore }
          ]);
          setShowLoader(false);
          return;
        }

        if ( !(isEditLicenseModule || isEditPaymentModule) &&  userSubscription?.status !== SUBSCRIPTION_STATUS.CANCELLED) {
          const response = await postSaveUserSubscription(payload);
          if (!response || !response?.client_secret) {
            setStripeError('No client secret received from server');

            setShowLoader(false);
            // return;
          }
        }
        onSuccess(
          <div>
            <p>You have successfully updated your payment method.</p>
            <p>Your next monthly payment on <b>{formatDateForDisplay(userSubscription?.billing?.next_billing_date)}</b> will automatically debit using this new payment method.</p>
          </div>
        )
      } catch (err) {
        showCommonDialog(
          null,
          err?.message || 'An error occurred during payment processing',
          commomKeys.actionStatus.error,
          resetDialogStore,
          [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
        );
        setStripeError(err?.message || 'An error occurred during payment processing');
        setShowLoader(false);
        return
      }
    } else if (data.paymentMethod === PAYMENT_METHODS.ACH && isAchChanged()){
      const isFormValid = await methods.trigger();
      if(isFormValid){
        try{
          const achFormResponse = await achSubmit(data);
          onSuccess(
            <div>
              <p>You have successfully updated your payment method.</p>
              <p>Your next monthly payment on <b>{formatDateForDisplay(userSubscription?.billing?.next_billing_date)}</b> will automatically debit using this new payment method.</p>
            </div>
          )        }catch(err){
          setShowLoader(false);
          return;
        }
      }else{
        setShowLoader(false);
        return;
      }
    }
    
    if (isFromSetting) {
      closeSubscriptionDialog();
    }
    console.log("errors", errors)
    if((isEditLicenseModule || isEditPaymentModule) && (Number(data.numberOfLicenses) !== currentTotalLicenses || data.paymentMethod !== userSubscription?.billing?.payment_method) && Object.keys(errors).length < 1){
      const response = await handleUpdateAccount(data);
      if(response){
      onSuccess(
      <div>
        {
          currentTotalLicenses !== Number(data.numberOfLicenses) && (
            <>
            <p> You have successfully purchased {data.numberOfLicenses} additional licenses. </p>
            <p>Don’t forget to assign your licenses to users.</p>
            </>
          )
        } 
        
         {

            (data.paymentMethod && currentPaymentMethod !== data.paymentMethod ) && (
             <>
            <p>You have successfully updated your payment method.</p>
            <p>Your next monthly payment on <b>{formatDateForDisplay(userSubscription?.billing?.next_billing_date)}</b> will automatically debit using this new payment method.</p>
            </>
          )
          }
        </div>)
      }
    }
    setShowLoader(false);
  };

  return (
    <div className={clsx(isEditPaymentModule ? styles.editPaymentContainer : styles.subscriptionSetup, currentMode === 'EDIT_LICENSES' && styles.editLicenseContainer)}>
      {
        (!isEditPaymentModule && !isEditLicenseModule) && (
      <div className={styles.header}>
        <div className={styles.stepTitle}>STEP 1: BUY LICENSES</div>
        <div className={styles.licenseCount}><span>YOU HAVE</span> 0 PAID LICENSES</div>
      </div>

        )
      }
      {(isEditPaymentModule && isEditLicenseModule) && ( <div className={styles.editPaymentNote}>To update or change your method of payment, please enter your new payment details here.</div>)}
      {/* Main Content */}
      <div className={clsx(isEditPaymentModule ? styles.editPaymentMain : styles.content)}>
        {/* Left Panel - License Quantity */}
        {
          !isEditPaymentModule && (  
        <div className={styles.panel}>
          <div className={styles.panelContent1}>
                <div className={styles.panelTitle}>
                <div> Enter the number of licenses you would like to purchase.</div>
                 <div className={styles.priceInfo}>
                  Each license is $50 per month
                 </div>
              </div>
              <div className={styles.quantityInput}>
                    <InputWrapper>
                      <CustomTextField
                        className={styles.numberOfUsersInput}
                        type='text'
                        mode='wholeNumber'
                        register={register("numberOfLicenses")}
                        placeholder='No. of Users'
                      />
                    </InputWrapper>
                {errors.numberOfLicenses && (
                  <span className="error">{String(errors.numberOfLicenses.message)}</span>
                )}
              </div>
          </div>
         
         
        </div>

          )
        }
        {/* Middle Panel - Payment Method */}

        <div className={clsx(currentMode === 'EDIT_LICENSES' && styles.editLicenseForm ,styles.panel)}>
        {
          (isEditLicenseModule || isEditPaymentModule) && (
        <div>
          <p className={styles.cardInfo}>
            {
              userSubscription?.billing?.payment_method === PAYMENT_METHODS.CARD ? (
                <p>
                  Auto-Debit from {buyerSetting?.card?.card_display_brand} ending in {buyerSetting?.card?.card_number_last_four_digits}
                </p>
              ) : (
                <p>
                  ACH Debit from {buyerSetting?.ach_debit?.bank_name} account ending in {buyerSetting?.ach_debit?.account_number?.slice(-4)}
                </p>
              )
            }
          </p>
          <p className={styles.editPaymentNote}>To update or change your method of payment, please enter your new payment details here.</p>
        </div>
          )
        }
          <FormProvider {...methods}>
            <PaymentForm
              onPaymentMethodChange={handlePaymentMethodChange}
              selectedPaymentMethod={selectedPaymentMethod}
              cardComplete={cardComplete}
              cardError={cardError}
              stripeError={stripeError}
              onCardChange={handleCardChange}
              currentMode={currentMode}
            />
          </FormProvider>
        </div>

        {/* Right Panel - Order Summary */}
        
        <div className={clsx(isEditPaymentModule ? styles.editPaymentBtnMain : styles.panel)}>
        {
          (!isEditPaymentModule) && ( 
              <>
              {
                !subscriptionDialogOpen && (
                <div className={styles.orderSummaryHeader}>
                   <div></div>
                   <div>CURRENT</div>
                   <div></div>
                   <div>UPDATED</div>
                </div>
                )
              }
                <div className={styles.orderSummary}>
   
                  <div className={styles.summaryRow}>
                    <span>Number of Licenses</span>
                    {
                      !subscriptionDialogOpen ? (
                        <>
                        <span>{userSubscription?.licenses?.current_total || 0}</span>
                        <span><SubscriptionArrowIcon/></span>
                        </>
                       )
                      : (
                        <>
                        <span></span>
                        <span></span>
                        </>
                      )
                    }
                    <span>{numberOfLicenses}</span>
                  </div>
                  <div className={styles.summaryRow}>
                    <span>Price per License</span>
                    { !subscriptionDialogOpen ? (
                      <>
                      <span>$ {monthlyPrice}</span>
                      <span><SubscriptionArrowIcon/></span>
                      </>
                    ) : (
                      <>
                      <span></span>
                      <span></span>
                      </>
                    )}
                     <span>$ {monthlyPrice}</span>
                  </div>
                </div>
                <div className={styles.summaryTotalRow}>
                  <span>Monthly Total</span>
                  { !subscriptionDialogOpen ? (
                    <>
                    <span >$ { Number(monthlyPrice)* Number(userSubscription?.licenses?.current_total || 0)}</span>
                    <span><SubscriptionArrowIcon/></span>
                    </>
                  ) : (
                    <>
                    <span></span>
                    <span></span>
                    </>
                  )}
                     <span>$ { Number(monthlyPrice)* Number(numberOfLicenses)}</span>
                </div>
              </>
          )
        }
        {
          !isEditPaymentModule && (
                <div className={styles.disclaimer}>
                  By clicking "Purchase," you agree to the terms of the{' '}
                  <span className={styles.link}>Debit Authorization Agreement</span>.
                </div>
            
          )
        }

          <button
            className={clsx(isEditPaymentModule ? styles.editPaymentBtn : styles.purchaseButton)}
            onClick={() => onSubmit(watch())}
            disabled={!stripe || isProcessing || !isSubmitEnabled}
          >
            {isProcessing ? 'PROCESSING...' : 
            isEditPaymentModule ? 'Save Method of Payment' : 'PURCHASE'
            }
          </button>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionSetup;
