import { MenuItem, Select } from '@mui/material';
import React from 'react';
import { useFormContext } from 'react-hook-form';
import { PAYMENT_METHODS } from 'src/renderer/common';
import styles from './SubscriptionSetup.module.scss'
import { ReactComponent as DropdownIcon } from '../../../assets/images/StateIconDropDpown.svg';

interface PaymentMethodDropdownProps {
  onPaymentMethodChange: (method: string) => void;
}

const PaymentMethodDropdown: React.FC<PaymentMethodDropdownProps> = ({ onPaymentMethodChange }) => {
  const { register, watch } = useFormContext();
  const selectedPaymentMethod = watch('paymentMethod');

  // Mapping object to convert payment method values to display text
  const paymentMethodDisplayMap = {
    [PAYMENT_METHODS.CARD]: 'CREDIT / DEBIT CARD',
    [PAYMENT_METHODS.ACH]: 'ACH Debit'
  };

  const handlePaymentMethodChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const method = e.target.value;
    onPaymentMethodChange(method);
  };

  return (
    <div>
      <label>CHOOSE METHOD OF PAYMENT</label>
      <Select
        displayEmpty
        {...register('paymentMethod')}
        onChange={handlePaymentMethodChange}
        className={styles.selectDropdown}
        MenuProps={{
          classes: {
            paper: styles.Dropdownpaper,
            list: styles.muiMenuList,
          },
        }}
        IconComponent={DropdownIcon}
        renderValue={(selected) => {
          if (!selected) {
            return <span style={{ color: '#71737f', textTransform:'uppercase'}}>Method of payment</span>; 
          }
          // Use the mapping to get the display text for the selected value
          return paymentMethodDisplayMap[selected as keyof typeof paymentMethodDisplayMap] || selected;
        }}
      >
        <MenuItem value={PAYMENT_METHODS.CARD}>CREDIT / DEBIT CARD</MenuItem>
        <MenuItem value={PAYMENT_METHODS.ACH}>ACH Debit</MenuItem>
      </Select>
    </div>
  );
};

export default PaymentMethodDropdown; 