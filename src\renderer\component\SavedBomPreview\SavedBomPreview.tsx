import React from 'react'
import styles from './SavedBomPreview.module.scss'
import clsx from 'clsx'
import dayjs from 'dayjs'
import SavedBomPreviewTile from './SavedBomPreviewTile'
import { dateTimeFormat } from '@bryzos/giss-ui-library'

const SavedBomPreview = ({ watch, fields, getValues }: any) => {
    return (
        <div className={styles.savedBomPreviewContainer}>
            <div className={clsx(styles.headerContainer)}>
                <div className={styles.headerItem}>
                    {watch('internal_po_number')?.toUpperCase() || '-'}
                </div>
                <div className={styles.headerItem}>
                    {watch('delivery_date') ?
                        `${dayjs(watch('delivery_date')).format('ddd').toUpperCase()}, ${dayjs(watch('delivery_date')).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit)}`
                        : '-'
                    }
                </div>
                <div className={styles.headerItem}>
                    {watch('order_type')?.toUpperCase() || '-'}
                </div>
                <div className={styles.headerItem}>
                    {<><span>{(watch('shipping_details.line1')?.toUpperCase() || '-')}</span> <span>{(watch('shipping_details.line2')?.toUpperCase() || '')}</span></>}
                </div>
            </div>
            <div className={clsx(styles.createPOContainer)}>
                <div className={styles.addPoLineTableContainer}>
                    <table className={styles.addPoLineTable}>
                        <thead>
                            <tr>
                                <th><span>LN</span></th>
                                <th><span>DESCRIPTION</span></th>
                                <th><span>QTY</span></th>
                                <th><span> $/UNIT</span></th>
                                <th><span>EXT ($)</span></th>
                            </tr>
                        </thead>
                        <tbody>
                            {
                                fields.map((item: any, index: any) => (
                                    <SavedBomPreviewTile index={index} watch={watch} getValues={getValues} />
                                ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    )
}

export default SavedBomPreview
