import * as yup from 'yup';

export const poHeaderSchema = yup.object().shape({
    isEdit: yup.boolean().default(true),
    buyer_internal_po: yup.string().trim().required('Po Job Name is not valid'),
    order_type: yup.string(),
    delivery_date: yup.string(),
    shipping_details: yup.object().shape({
        line1: yup.string().trim().required('Address line 1 is required').trim(),
        line2: yup.string().trim().optional().trim(),
        city: yup.string().trim().required('City is required').trim(),
        state_id: yup.number().required('State is required'),
        zip: yup.string().trim().required('ZIP code is required').matches(/^\d{5}$/, 'ZIP code must be 5 digits'),
        validating_state_id_zip: yup.boolean().default(false).defined()
    })
});
