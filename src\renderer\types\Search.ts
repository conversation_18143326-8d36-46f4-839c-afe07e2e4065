export type ProductQtyType = 'cwt'| 'ft' | 'lb' | 'cwt,ft'

export type ProductPricingModel = {
    [key: string]: string | number | boolean | undefined;
    id: number,
    UI_Description: string,
    cwt_price: string,
    ft_price: string,
    lb_price: string,
    product_type_pipe: boolean,
    line_session_id: string,
    "cwt,ft_price"?: string,
    is_safe_product_code: boolean,
    domestic_material_only: boolean,
    pc_price: string,
    net_ton_price: string,   
}

export type SearchAnalyticDataModel = {
    session_id: string,
    line_session_id: string,
    product_id: number,
    description: string,
    price_shared: boolean,
    price_share_unit?:  string,
    search_price_unit?: string,
    zip_code?: string,
    order_size?: number
}

export type HttpRequestPayload<T> = {
    data: T
}