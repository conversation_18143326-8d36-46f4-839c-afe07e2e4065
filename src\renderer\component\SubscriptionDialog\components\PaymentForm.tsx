import React, { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import PaymentMethodDropdown from './PaymentMethodDropdown';
import CreditCardForm from './CreditCardForm';
import BankTransferForm from './AchPaymentForm';
import { PAYMENT_METHODS } from 'src/renderer/common';
import AchPaymentForm from './AchPaymentForm';
import styles from './SubscriptionSetup.module.scss'
import clsx from 'clsx';
interface PaymentFormProps {
  onPaymentMethodChange: (method: string) => void;
  currentMode: string;
  selectedPaymentMethod: string;
  cardComplete: {
    cardNumber: boolean;
    cardExpiry: boolean;
    cardCvc: boolean;
  };
  cardError: {
    cardNumber: string | null;
    cardExpiry: string | null;
    cardCvc: string | null;
  };
  stripeError: string | null;
  onCardChange: (event: any, fieldName: 'cardNumber' | 'cardExpiry' | 'cardCvc') => void;
}

const PaymentForm: React.FC<PaymentFormProps> = ({ 
  onPaymentMethodChange, 
  selectedPaymentMethod,
  cardComplete,
  cardError,
  stripeError,
  onCardChange,
  currentMode
}) => {
  console.log("currentMode", currentMode);
  console.log(">>>>>>>>>>>> payment")
  return (
    <div className={clsx(styles.paymentForm ,currentMode  === 'EDIT_LICENSES' && styles.isEditLicenseModule)}>
      <PaymentMethodDropdown onPaymentMethodChange={onPaymentMethodChange} />
      
      {selectedPaymentMethod === PAYMENT_METHODS.CARD && (
        <CreditCardForm 
          cardComplete={cardComplete}
          cardError={cardError}
          stripeError={stripeError}
          onCardChange={onCardChange}
        />
      )}
      
      {selectedPaymentMethod === PAYMENT_METHODS.ACH && (
        <AchPaymentForm />
      )}
    </div>
  );
};

export default PaymentForm; 