.orientation-slider-container {
  display: flex;
  width: 100%;
  font-family: 'Syncopate', sans-serif;
  justify-content: center;
  align-items: center;
  gap: 12px;
}

.orientation-label {
 font-family: Syncopate;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: -0.56px;
  text-align: center;
  color: #0f0f14;
  white-space: nowrap;
}

.slider-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.slider-track {
  position: relative;
  flex: 1;
  height: 4px;
  width: 104px;
  background-color: #0f0f14;
  border: none;
}

.slider-input {
  position: relative;
  top: -11px;
  width: 100%;
  height: 100%;
  background: transparent;
  outline: none;
  border: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;

  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background-color: #fff;
    cursor: pointer;
    position: relative;
    z-index: 999;
    // top:-11px;

    &:hover {
      background-color: #f0f0f0;
      border-color: #888;
    }
  }

  &::-moz-range-thumb {
    width: 14px;
    height: 14px;
    background-color: #fff;
    cursor: pointer;
    position: relative;
    z-index: 2;

    &:hover {
      background-color: #f0f0f0;
      border-color: #888;
    }
  }
}

.marks-container {
  position: absolute;
  top: -2px;
  left: 0;
  width: 100%;
  height: calc(100% + 4px);
  pointer-events: none;
}

.mark {
  position: absolute;
  top: -5px;
  height: 100%;
  transform: translateX(-50%);
  cursor: pointer;
  z-index: 99;
}

.mark-line {
  width: .5px;
  height: 18px;
  background-color: #0f0f14;
  margin: 0 auto;
  cursor: pointer;
}


.value-display {
  width:30px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  position: relative;
   font-family: Syncopate;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: -0.56px;
  text-align: center;
  color: #0f0f14;
}

.value-number {
   font-family: Syncopate;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: -0.56px;
  text-align: center;
  color: #0f0f14;
  line-height: 1;
    opacity: 0.5;
}

.value-unit {
   font-family: Syncopate;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: -0.56px;
  text-align: center;
  color: #0f0f14;
  white-space: nowrap;
  opacity: 0.5;
}