.OrderActionsMain {
    width: 322px;
    padding: 20px;
    border-radius: 20px;
    background-origin: border-box;
    position: relative;
    overflow: hidden;
    background: url(../../assets/images/Create-PO-Order-Ledger.svg) no-repeat transparent;

    .actionTitle {
        font-family: Syncopate;
        font-size: 20px;
        font-weight: bold;
        font-style: normal;
        line-height: 1.2;
        letter-spacing: 0.8px;
        text-align: center;
        color: #fff;
        margin-bottom: 20px;
    }

    button {
        width: 100%;
        height: 50px;
        flex-grow: 0;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        padding: 14px 0 8px;
        border-radius: 10px;
        background-color: #222329;
        font-family: Syncopate;
        font-size: 18px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: -0.72px;
        text-align: center;
        color: rgba(255, 255, 255, 0.4);
        margin-bottom: 12px;
        &:last-child{
            margin-bottom: 0px;
        }
        &[disabled]{
              background-color: #222329;
                color: rgba(255, 255, 255, 0.1);
        }
        &:not([disabled]):not(.activeBtn):hover {
            background-color: #fff;
            color: #191a20;
        }

        &.activeBtn{
            background:url(../../assets/images/Order-Button-Active-Bg.svg) no-repeat transparent;
            background-position: bottom right;
            background-size: cover;
          
        }
    }
}
