import { create } from 'zustand';

interface DeletedItems {
  INSTANT_PRICING: any[];
  PO: any[];
  QUOTE: any[];
}

interface DeletedItemsStore {
  deletedItems: DeletedItems;
  setDeletedItems: (deletedItems: DeletedItems) => void;
  staticDeletedItems: any[];
  setStaticDeletedItems: (staticDeletedItems: any[]) => void;
  removeFromDeletedItems: (itemIds: string[], orderType: 'INSTANT_PRICING' | 'QUOTE' | 'PO') => void;
  addToDeletedItems: (items: any[], orderType: 'INSTANT_PRICING' | 'QUOTE' | 'PO') => void; // Add this line
}

export const useDeletedItemsStore = create<DeletedItemsStore>((set) => ({
  deletedItems: {
    INSTANT_PRICING: [],
    PO: [],
    QUOTE: [],
  },
  setDeletedItems: (deletedItems) => set({ deletedItems }),
  staticDeletedItems: [],
  setStaticDeletedItems: (staticDeletedItems) => set({ staticDeletedItems }),
  removeFromDeletedItems: (itemIds: string[], orderType: 'INSTANT_PRICING' | 'QUOTE' | 'PO') => {
    set((state) => {
      const updatedDeletedItems = { ...state.deletedItems };
      const key = orderType === 'QUOTE' ? 'QUOTE' : orderType === 'PO' ? 'PO' : 'INSTANT_PRICING';
      
      updatedDeletedItems[key] = updatedDeletedItems[key].filter((item) => 
        !itemIds.includes(item.id)
      );
      
      return { deletedItems: updatedDeletedItems };
    });
  },
  addToDeletedItems: (items: any[], orderType: 'INSTANT_PRICING' | 'QUOTE' | 'PO') => {
    set((state) => {
      const updatedDeletedItems = { ...state.deletedItems };
      const key = orderType === 'QUOTE' ? 'QUOTE' : orderType === 'PO' ? 'PO' : 'INSTANT_PRICING';
      
      // Add items that don't already exist in the deleted items list
      const newItems = items.filter(item => 
        !updatedDeletedItems[key].some(existingItem => existingItem.id === item.id)
      );
      
      updatedDeletedItems[key] = [...updatedDeletedItems[key], ...newItems];
      
      return { deletedItems: updatedDeletedItems };
    });
  },
}));
