import { ClickAwayListener, Dialog } from '@mui/material'
import React from 'react'
import styles from './NoBuyerSettingPopup.module.scss'
import { ReactComponent as CloseIcon } from '../../../../../assets/images/no-buyersetting-close.svg'

const NoBuyerSettingPopup = ({ openNoBuyerSettingPopup, setOpenNoBuyerSettingPopup, handleGoToSettings }: { openNoBuyerSettingPopup: boolean, setOpenNoBuyerSettingPopup: (open: boolean) => void, handleGoToSettings: () => void }) => {
    return (
        <Dialog
            open={openNoBuyerSettingPopup}
            onClose={() => setOpenNoBuyerSettingPopup(false)}
            transitionDuration={100}
            disableScrollLock={true}
            
            style={{
                position: 'absolute',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                border: '1px solid transparent',
                zIndex: 999999
            }}
            PaperProps={{
                className: styles.modal,
            }}
            hideBackdrop
        >
            <ClickAwayListener onClickAway={() => setOpenNoBuyerSettingPopup(false)}>

                <div>
                    <span className={styles.closeIcon} onClick={() => setOpenNoBuyerSettingPopup(false)}><CloseIcon /></span>
                    <h1>almost there!</h1>
                    <p>
                        Before completing checkout, please finish filling out your Settings. There is information required to process this order. 
                    </p>
                    <p>
                        This order is saved here in Purchasing so you can checkout immediately upon completion.
                    </p>
                    <div className={styles.buttonContainer}>
                        <button onClick={handleGoToSettings}>Go to Settings</button>
                    </div>
                </div>

            </ClickAwayListener>
        </Dialog>
    )
}

export default NoBuyerSettingPopup
