.reactionContainer {
  display: flex;
  align-items: center;
  width: fit-content;
}
.reaction {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-right: 5px;
  padding: 2px 4px;
  font-size: 16px;
  cursor: pointer;
  position: relative;

  &:last-child{
    margin-right: 0px;
  }

  &:hover {
    background-color: #e0e0e0; 
    transition: background-color 0.3s ease;
  }

  .countReaction {
    font-size: 11px;
  }
}


.reactionMenu {
  z-index: 1000;
  display: flex;
  flex-direction: row;
  background-color: white;
  border: 1px solid #ccc;
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.selectedReaction{
  background-color: #f0f0f0;
  &:hover {
    background-color: #ccc;
  }
}
.reactionButton {
  margin: 4px;
  cursor: pointer;
  background: none;
  border: none;
  font-size: 20px;

  &:hover {
    background-color: #f0f0f0;
  }
}