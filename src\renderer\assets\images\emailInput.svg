<svg width="482" height="52" viewBox="0 0 482 52" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#yvaqq9um6a)">
        <rect x="1" y="1" width="480" height="50" rx="13" fill="#23242A" fill-opacity=".2"/>
        <rect x=".5" y=".5" width="481" height="51" rx="13.5" stroke="url(#e34g15irfb)" stroke-opacity=".33"/>
    </g>
    <defs>
        <linearGradient id="e34g15irfb" x1="546.332" y1="82.347" x2="516.5" y2="-63.621" gradientUnits="userSpaceOnUse">
            <stop stop-color="#fff"/>
            <stop offset="1" stop-color="#1A1B20"/>
        </linearGradient>
        <filter id="yvaqq9um6a" x="0" y="0" width="485" height="55" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dx="3" dy="3"/>
            <feGaussianBlur stdDeviation="3.7"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
            <feBlend in2="shape" result="effect1_innerShadow_3_247"/>
        </filter>
    </defs>
</svg>
