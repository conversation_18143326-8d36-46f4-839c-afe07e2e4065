<svg width="70" height="30" viewBox="0 0 70 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_26_303)">
<g filter="url(#filter0_df_26_303)">
<rect width="70" height="30" rx="5" fill="url(#paint0_linear_26_303)" fill-opacity="0.18" shape-rendering="crispEdges"/>
<rect x="0.25" y="0.25" width="69.5" height="29.5" rx="4.75" stroke="url(#paint1_linear_26_303)" stroke-width="0.5" shape-rendering="crispEdges"/>
<rect x="0.25" y="0.25" width="69.5" height="29.5" rx="4.75" stroke="url(#paint2_linear_26_303)" stroke-opacity="0.6" stroke-width="0.5" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter1_b_26_303)">
<rect x="0.999969" y="1" width="68" height="28" rx="4" fill="url(#paint3_linear_26_303)" fill-opacity="0.11"/>
</g>
</g>
<defs>
<filter id="filter0_df_26_303" x="-9" y="-10" width="91" height="51" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="3" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_26_303"/>
<feOffset dx="1.5" dy="0.5"/>
<feGaussianBlur stdDeviation="3.75"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.249202 0 0 0 0 0.249202 0 0 0 0 0.249202 0 0 0 0.36 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_26_303"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_26_303" result="shape"/>
<feGaussianBlur stdDeviation="0.1" result="effect2_foregroundBlur_26_303"/>
</filter>
<filter id="filter1_b_26_303" x="-20.3" y="-20.3" width="110.6" height="70.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="10.65"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_26_303"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_26_303" result="shape"/>
</filter>
<linearGradient id="paint0_linear_26_303" x1="58.7073" y1="52.2456" x2="24.4408" y2="-43.9034" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#999999"/>
</linearGradient>
<linearGradient id="paint1_linear_26_303" x1="64.0309" y1="27.5032" x2="31.8422" y2="-15.7377" gradientUnits="userSpaceOnUse">
<stop stop-color="#1C1C21" stop-opacity="0"/>
<stop offset="1" stop-color="white" stop-opacity="0.6"/>
</linearGradient>
<linearGradient id="paint2_linear_26_303" x1="96.3331" y1="21.7622" x2="84.7903" y2="-5.27968" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_26_303" x1="31.2939" y1="20.7912" x2="24.2536" y2="3.1308" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="#1C1C21"/>
</linearGradient>
<clipPath id="clip0_26_303">
<rect width="70" height="30" fill="white"/>
</clipPath>
</defs>
</svg>
