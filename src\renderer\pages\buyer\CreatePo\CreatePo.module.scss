.createPoContent {
  position: absolute;
  inset: 0;
  z-index: 1;
  background-color: #191a20;

  &.buyerOrderManagementContainer {
      table {
        tr {
          &.disputeResolved {
            .poDescriptionDiv {
              border-left: solid 4px #2c8a4a;
  
              .partNumberFiled {
                display: none;
              }
            }
          }
  
          &.disputePending {
            .poDescriptionDiv {
              border-left: solid 4px #bc6d42;
  
              .partNumberFiled {
                display: none;
              }
            }
          }
  
          &.disputeRejected {
            .poDescriptionDiv {
              border-left: solid 4px #ff4848;
  
              .partNumberFiled {
                display: none;
              }
            }
          }
        }
      }
    }


  .formInnerContent {
    position: relative;
    height: 100%;

    .tblWrapper {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .formInputGroup1 {
      display: flex;
      gap: 20px;

      .lblInput {
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.6;
        text-align: left;
        color: #fff;
        width: 152px;
        height: 34px;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        padding: 6px 4px 6px 10px;
        border: solid 0.5px #000;
        background-color: rgba(0, 0, 0, 0.25);
        border-radius: 4px 0px 0px 4px;
        border-right: 0px;

        .questionIcon {
          display: flex;
          align-items: center;
          margin-left: auto;
          transition: all 0.1s;

          .questionIcon2 {
            display: none;
          }

          .questionIcon3 {
            display: none;
          }

          .questionIcon4 {
            display: none;
          }

          &:hover {
            .questionIcon1 {
              display: none;
            }

            .questionIcon2 {
              display: block;
            }
          }
        }
      }

      .inputSection {
        padding: 13px 16px;
        border-radius: 12px;
        background-color: rgba(255, 255, 255, 0.04);
        width: 250px;
        height: 40px;
        font-family: Syncopate;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: 0.56px;
        text-align: left;
        color: #616575;
        border: none;
        flex: 1;

        &.paddingLR0 {
          padding-left: 0px;
          padding-right: 0px;
        }

        &.cityInput {
          flex: 0 0 96px;
        }

        &.stateInput {
          flex: 0 85px;
        }

        &.zipCodeInput {
          flex: 0 0 72px;
          padding: 6px 3px 6px 6px;

        }

        &.bdrRadius0 {
          border-radius: 0px;
        }

        &.bdrRight0 {
          border-right: 0px;
        }

        .chooseOneContainer {
          width: 100%;
          display: flex;
          flex-direction: column;

          .chooseOneHeading {
            font-family: Inter;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 4px;
          }

          .radioContainer {
            display: flex;
            gap: 20px;
            align-items: center;

            .radioLabel {
              display: flex;
              align-items: center;
              gap: 8px;
              color: #fff;
              font-family: Inter;
              font-size: 14px;
              cursor: pointer;

              .radioInput {
                appearance: none;
                width: 16px;
                height: 16px;
                border: 2px solid rgba(255, 255, 255, 0.5);
                border-radius: 50%;
                margin: 0;
                position: relative;
                cursor: pointer;

                &:checked {
                  border-color: #70ff00;

                  &::after {
                    content: '';
                    position: absolute;
                    width: 8px;
                    height: 8px;
                    background-color: #70ff00;
                    border-radius: 50%;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                  }
                }

                &:hover {
                  border-color: #70ff00;
                }
              }
            }
          }
        }

        input {
          width: 100%;
          font-family: Inter;
          font-size: 14px;
          font-weight: normal;
          line-height: 1.6;
          text-align: left;
          color: #fff;
          border: 0px;
          background-color: transparent;
          padding: 0px;

          &:focus {
            outline: none;
            box-shadow: none;
          }

          &::placeholder {
            color: rgba(255, 255, 255, 0.5);
            font-weight: normal;
          }
        }
      }

      .errorInput {
        border-color: #ff3b3b !important;

        &:focus {
          border-color: #ff3b3b !important;
        }
      }

      &.FormInputGroupError {
        .lblInput {
          border: solid 0.5px #f00;
          background-color: #f00;
          cursor: pointer;
          white-space: nowrap;
        }

        .borderOfError {
          border: solid 0.5px #f00;
        }
      }

      select {
        background: transparent;
        border: 0;
        color: rgba(255, 255, 255, 0.5);
        height: 100%;
        width: 100%;
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.6;
        text-align: left;
        color: #fff;
      }

      &:focus-within {
        .lblInput {
          background-color: #70ff00;
          border: solid 0.5px #70ff00;
          color: #000;

          .questionIcon {
            display: flex;
            align-items: center;
            margin-left: auto;
            transition: all 0.1s;

            .questionIcon3 {
              display: none;
            }

            .questionIcon1 {
              display: none;
            }

            .questionIcon2 {
              display: none;
            }

            .questionIcon4 {
              display: block;
            }

            &:hover {
              .questionIcon4 {
                display: none;
              }

              .questionIcon1 {
                display: none;
              }

              .questionIcon2 {
                display: none;
              }

              .questionIcon3 {
                display: block;
              }
            }
          }
        }

        .inputSection {
          height: 34px;
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: flex-start;
          padding: 6px 8px 6px 10px;
          border: solid 0.5px #70ff00;
          background-color: rgba(0, 0, 0, 0.2);
          flex: 1;

          &.paddingLR0 {
            padding-left: 0px;
            padding-right: 0px;
          }

          &.cityInput {
            flex: 0 0 96px;
          }

          &.stateInput {
            flex: 0 85px;
          }

          &.zipCodeInput {
            flex: 0 0 72px;
            padding: 6px 3px 6px 6px;

          }

          &:last-child {
            border-left: 0px;
          }

          input {
            width: 100%;
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.6;
            text-align: left;
            // color: #70ff00;
            border: 0px;
            background-color: transparent;
            padding: 0px;

            &:focus {
              outline: none;
              box-shadow: none;
            }

            &::placeholder {
              font-weight: normal;
            }
          }
        }
      }
    }

    .formInputGroupFocus {
      .lblInput {
        background-color: #70ff00;
        border: solid 0.5px #70ff00;
        color: #000;

        .questionIcon {
          display: flex;
          align-items: center;
          margin-left: auto;
          transition: all 0.1s;

          .questionIcon3 {
            display: none;
          }

          .questionIcon1 {
            display: none;
          }

          .questionIcon2 {
            display: none;
          }

          .questionIcon4 {
            display: block;
          }

          &:hover {
            .questionIcon4 {
              display: none;
            }

            .questionIcon1 {
              display: none;
            }

            .questionIcon2 {
              display: none;
            }

            .questionIcon3 {
              display: block;
            }
          }
        }
      }

      .inputSection {
        height: 34px;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: flex-start;
        padding: 6px 8px 6px 10px;
        border: solid 0.5px #70ff00;
        background-color: rgba(0, 0, 0, 0.2);
        flex: 1;

        &.paddingLR0 {
          padding-left: 0px;
          padding-right: 0px;
        }

        &.cityInput {
          flex: 0 0 96px;
        }

        &.stateInput {
          flex: 0 85px;
        }

        &.zipCodeInput {
          flex: 0 0 72px;
          padding: 6px 3px 6px 6px;

        }

        &:last-child {
          border-left: 0px;
        }

        input {
          width: 100%;
          font-family: Inter;
          font-size: 14px;
          font-weight: normal;
          line-height: 1.6;
          text-align: left;
          border: 0px;
          background-color: transparent;
          padding: 0px;

          &:focus {
            outline: none;
            box-shadow: none;
          }

          &::placeholder {
            font-weight: normal;
          }
        }
      }
    }
  }

  .totalWeight {
    font-family: Inter;
    font-size: 14px;
    line-height: 1.4;
    text-align: left;
    color: #fff;
    padding-left: 40px;
    padding-bottom: 3px;
    border-bottom: 0.2px solid #fff;
    margin-top: -12px;
  }

  .lineWeight {
    font-family: Inter;
    font-size: 14px;
    line-height: 1.4;
    text-align: left;
    color: rgba(255, 255, 255, 0.75);
  }

  .addMoreLine {
    position: relative;
    margin-top: 16px;
    display: flex;
    align-items: center;

    button {
      background-color: transparent;
      border: 0px;
      transition: all 0.1s;
      position: relative;
      top: 1.5px;

      .addLineHover {
        display: none;
      }

      &:hover {
        .addLine {
          display: none;
        }

        .addLineHover {
          display: inline-block;
        }
      }

      &:focus-visible {
        border: 1px solid #70ff00;
      }
    }

    span {
      width: 100%;
      display: flex;
      border-top: 0.2px solid #fff;
      position: absolute;
      z-index: -1;
    }
  }

  .removeLine {
    position: relative;
    margin-top: 16px;

    button {
      z-index: 1;
      position: absolute;
      top: -13px;
      left: 0;
      background-color: transparent;
      border: 0px;
      transition: all 0.1s;

      .removeLineHoverIcon {
        display: none;
      }

      &:hover {
        .removeLineIcon {
          display: none;
        }

        .removeLineHoverIcon {
          display: inline-block;
        }
      }

      &:focus-visible {
        border: 1px solid #70ff00;
      }
    }

  }

  .totalAmt {
    margin-top: 8px;
    margin-bottom: 12px;
    display: flex;
    justify-content: space-between;

    .featureActions {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .refreshNetTerm {
        opacity: 0.7;
        font-family: Inter;
        font-size: 12px;
        line-height: 1.2;
        text-align: left;
        color: #fff;
        display: flex;
        align-items: center;
        gap: 2px;
        white-space: nowrap;

        &:hover {
          opacity: 1;
        }
      }
    }

    .saleTax {
      font-family: Inter;
      font-size: 18px;
      line-height: 1.6;
      text-align: right;
      color: #fff;

      .questionIcon {
        vertical-align: middle;
        margin-left: 6px;
        transition: all 0.1s;

        .questionIcon2 {
          display: none;
        }

        &:hover {
          .questionIcon1 {
            display: none;
          }

          .questionIcon2 {
            display: inline-block;
          }
        }
      }
    }

    .totalPurchase {
      font-family: Inter;
      font-size: 24px;
      font-weight: 600;
      line-height: 1.6;
      text-align: right;
      color: #fff;
    }

    table {
      width: 100%;
      display: flex;
      justify-content: end;
      white-space: nowrap;

      tr {
        td {
          text-align: right;
          padding: 0px 6px;

          .prodId {
            display: inline-block;
          }
        }
      }
    }
  }

  .btnOfCreatePo {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-top: 12px;

    .selectedpayment {
      font-family: Inter;
      font-size: 24px;
      font-weight: 600;
      line-height: 1.6;
      color: #fff;
      width: 100%;
      height: 54px;
      border-radius: 4px;
      border: solid 1px rgba(255, 255, 255, 0.1);
      background-color: rgba(255, 255, 255, 0.1);
      cursor: pointer;

      .selectPaymentMethod {
        padding: 0px;
        font-family: Inter;
        font-size: 18px;
        line-height: 1.6;
        text-align: left;
        color: #fff;
        height: 50px;
        width: 100%;

        .MuiSelect-select {
          display: flex;
          justify-content: center;
        }

        svg {
          right: 10px;
          top: calc(50% - 0.6em);
          transform: unset;
          color: #fff;
        }

        fieldset {
          border: 0;
        }
      }
    }

    button {
      width: 100%;
      height: 54px;
      padding: 8px 0;
      border-radius: 4px;
      border: solid 1px rgba(255, 255, 255, 0.1);
      background-color: rgba(255, 255, 255, 0.1);
      cursor: pointer;
      transition: all 0.1s;

      &:hover {
        background-color: #70ff00;

        &.placeOrder {
          color: #000;
        }
      }

      &.placeOrder {
        font-family: Inter;
        font-size: 24px;
        font-weight: normal;
        line-height: 1.6;
        color: #fff;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        border: solid 1px rgba(255, 255, 255, 0.1);
        background-color: rgba(255, 255, 255, 0.1);

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);

          &.placeOrder {
            color: #fff;
          }
        }
      }
    }
  }

  .textOfCreatePo {
    width: 100%;
    font-size: 14px;
    font-family: Inter;
    line-height: 1.6;
    font-weight: 300;
    text-align: center;
    color: #fff;
    margin-top: 12px;
  }

  .backBtnMain {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 40px;
    background-color: #000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px 0 40px;
    z-index: 100;
    border-bottom-left-radius: 21px;
    border-bottom-right-radius: 21px;

    .savePOGoBack {
      font-family: Syncopate;
      font-size: 11.8px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: -0.48px;
      text-align: center;
      color: rgba(255, 255, 255, 0.6);

      &:hover {
        color: rgba(255, 255, 255, 0.9);
      }

      &:focus-visible {
        color: rgba(255, 255, 255, 0.9);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;

        &:hover {
          color: rgba(255, 255, 255, 0.6);
        }

        &:focus-visible {
          color: rgba(255, 255, 255, 0.6);
        }
      }
    }

    .cancelPOGoBack {
      font-family: Syncopate;
      font-size: 10px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: 0.4px;
      text-align: center;
      color: rgba(255, 255, 255, 0.5);
      margin-right: 237px;

      &:hover {
        color: rgba(255, 255, 255, 0.9);
      }

      &:focus-visible {
        color: rgba(255, 255, 255, 0.9);
      }

    }
  }

}

.selectPaymentMethodPaper.selectPaymentMethodPaper {
  padding: 3px 4px 8px 12px;
  backdrop-filter: blur(24px);
  background-color: #ffffff4c;
  box-shadow: 0px 8px 30px #000000cc;
  margin-top: 9px;
  overflow: hidden;
  background: url(../../assets/images/DropDownBG.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;

  ul {
    overflow: auto;
    max-height: 260px;
    padding-right: 8px;

    &::-webkit-scrollbar {
      width: 8px;
      height: 6px;
    }

    li {
      font-family: Inter;
      font-size: 16px;
      font-weight: normal;
      line-height: 1.57;
      text-align: left;
      color: #fff;
      border-radius: 2px;
      margin-bottom: 3px;

      i {
        font-size: 12px;
      }

      &:hover {
        background-color: #fff;
        color: #000;
      }

      &[aria-selected="true"] {
        background-color: #fff;
        color: #000;
        border-radius: 2px;
      }

    }

    .Mui-selected.Mui-selected {
      background-color: transparent;
    }
  }
}

.selectUomPaper.selectUomPaper {
  padding: 4px;
  border-radius: 6px;
  -webkit-backdrop-filter: blur(22.4px);
  backdrop-filter: blur(22.4px);
  background-color: rgba(128, 130, 140, 0.28);
  box-shadow: none;

  ul {
    overflow: auto;
    max-height: 260px;
    padding: 0;

    &::-webkit-scrollbar {
      width: 6px;
    }

    li {
      text-transform: uppercase;
      margin-bottom: 1px;
      padding: 6px;
      border-radius: 5px;
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: normal;
      text-align: center;
      color: rgba(255, 255, 255, 0.6);

      &[aria-selected="true"] {
        background-color: rgba(255, 255, 255, 0.2);
        font-weight: bold;
        color: #fff;
      }

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
        font-weight: bold;
        color: #fff;
      }

    }

    .Mui-selected.Mui-selected {
      background-color: rgba(255, 255, 255, 0.2);
      font-weight: bold;
      color: #fff;
    }
  }
}


.autocompleteDescPanel {
  border-radius: 4px;
}

.autocompleteDescInnerPanel.autocompleteDescInnerPanel {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  box-shadow: inset 4px 4px 10.1px 0 #000;
  border-radius: 0px 0px 13px 13px;
  background: url(../../../assets/images/Mian-Dropdown-BG.svg) #1b1c21 no-repeat;
  background-position: bottom right;
  background-size: cover;
}

.noOptionPanel.noOptionPanel {
  display: none;
  font-family: Inter;
  font-size: 14px;
  font-weight: 300;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: left;
  color: #eaecf0;
}

.visibleNoOption.visibleNoOption {
  display: block;
}

.listAutoComletePanel.listAutoComletePanel {
  width: 100%;
  max-height: 672px;
  padding: 6px 4px 6px 10px;
  margin-top: 4px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }
  span {
    padding: 8px;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 8px;
    font-family: Inter;
    font-size: 14px;
    font-weight: 300;
    line-height: 1.4;
    letter-spacing: 0.98px;
    text-align: left;
    color: #eaecf0;
    text-transform: uppercase;

    &:hover {
      border-radius: 10px;
      background-image: linear-gradient(335deg, #eaecf0 0%, #9b9eac 100%);
      color: #0f0f14;
    }

    &[aria-selected="true"] {
      border-radius: 10px;
      background-image: linear-gradient(335deg, #eaecf0 0%, #9b9eac 100%);
      color: #0f0f14;

      &.Mui-focused {
        background-image: linear-gradient(335deg, #eaecf0 0%, #9b9eac 100%);
        background-color: transparent;
      }
    }
  }
}

.Dropdownpaper.Dropdownpaper {
  padding: 3px 4px 8px 8px;
  -webkit-backdrop-filter: blur(30px);
  backdrop-filter: blur(30px);
  box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
  background-color: rgba(0, 0, 0, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 7px;
  overflow: hidden;
  width: 66px;
  border-radius: 4px;
  background: url(../../assets/images/DropDownBG.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;

  &.DropdownDeliveryDate {
    margin-top: 8px;
  }

  ul {
    overflow: auto;
    max-height: 230px;
    padding-right: 4px;
    padding-top: 0px;
    padding-bottom: 0px;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    li {
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.4;
      text-align: left;
      color: #fff;
      border-radius: 2px;
      padding: 8px 12px;
      margin-bottom: 2px;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: #fff;
      }

      &.Mui-selected {
        background-color: rgba(112, 255, 0, 0.2);
        color: #fff;
      }
    }
  }
}


.ErrorDialog {
  .dialogContent {
    max-width: 330px;
    width: 100%;
    min-height: 230px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 30px 34px 30px 34px;
    object-fit: contain;
    border-radius: 10px;
    -webkit-backdrop-filter: blur(24px);
    backdrop-filter: blur(24px);
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
    background-color: rgba(0, 0, 0, 0.72);
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: center;
    color: #fff;

    p {
      margin-bottom: 20px;
    }


    .submitBtn {
      height: 40px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding: 10px 24px;
      border-radius: 4px;
      border: solid 0.5px #fff;
      background-color: transparent;
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.4;
      text-align: center;
      color: #fff;
      transition: all 0.1s;

      &:hover {
        background-color: #70ff00;
        border: solid 0.5px #70ff00;
        color: #000;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;

        &:hover {
          border: solid 0.5px #fff;
          background-color: transparent;
          color: #fff;
        }
      }
    }


  }

}

.w100 {
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}


.headerNoteCreatePO {
  height: 72px;
  padding: 8px;
  border-radius: 6px;
  background-color: rgba(255, 255, 255, 0.15);
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-bottom: 10px;
  position: relative;

  .headerNoteText {
    font-family: Inter;
    font-size: 12px;
    line-height: 1.4;
    text-align: center;
    color: #fff;
  }

  .marginTop8 {
    margin-top: 8px;
  }

  .leftIcon {
    position: absolute;
    left: 8px;
    top: 20px;
  }

  .rightIcon {
    position: absolute;
    right: 8px;
    top: 20px;
  }

  svg {
    width: 32px;
    height: 32px;
  }
}

.domesticMaterialCheckbox {
  margin-top: 5px;
  display: flex;
  padding-bottom: 10px;

  .lblCheckbox {
    display: flex;
    align-items: center;
    white-space: nowrap;
    padding-left: 21px;
    font-size: 12px;

    &:focus-within {
      .checkmark {
        border: solid 1px #70ff00;
      }
    }

    .domesticMaterialTex {


      font-family: Inter;
      font-size: 14px;
      line-height: 1.6;
      text-align: left;
      color: rgba(255, 255, 255, 0.75);
    }

    .checkmark {
      width: 15px;
      height: 15px;
      border-radius: 1.7px;
      border: solid 0.7px #3b4665;
      background-color: #ebedf0;
      top: 4px;

      &::after {
        left: 5px;
        top: 2px;
        width: 3px;
        height: 6px;

      }
    }

    input:checked~.checkmark {
      background-color: #70ff00;
      border: solid 0.7px #70ff00;
    }

  }

  .div50 {
    width: 280px;
  }

  .hiddenCheckbox:focus+.customNumberToggle,
  .hiddenCheckbox:focus-visible+.customNumberToggle {
    outline: 1px solid #459fff;
    box-shadow: 0 0 3px rgba(69, 159, 255, 0.5);
    opacity: 1;
  }
}

.questionIconDesc {
  vertical-align: middle;
  margin-left: 6px;
  transition: all 0.1s;
  position: absolute;
  top: 12px;
  right: 90px;
  cursor: pointer;

  .questionIcon2 {
    display: none;
  }

  &:hover {
    .questionIcon1 {
      display: none;
    }

    .questionIcon2 {
      display: inline-block;
    }
  }
}

.radioGroupContainer {
  display: flex;
  text-align: center;
  cursor: pointer;
  justify-content: center;
  position: relative;

  .chosseOneIcon {
    position: absolute;
    top: -19px;
  }

  .radioButton {
    width: 90px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    background-color: rgba(255, 255, 255, 0.04);

    &:not(.disableBidBuyNowBtn):hover {
      color: #459fff;
    }

    &.selected {
      font-family: Syncopate;
      font-size: 14px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1;
      letter-spacing: 0.56px;
      text-align: left;
      color: #459fff;
    }

    &.buySelected {
      background: url(../../../assets/images/BUYInputBG.svg);
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      border-left: none;
    }

    &.bidSelected {
      background: url(../../../assets/images/BIDInputBG.svg);
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
    }
  }

  .disableBidBuyNowBtn {
    cursor: not-allowed;
  }

  .radioButtonLeft {
    border-right: solid 1.5px #323336;
    border-top-left-radius: 13px;
    border-bottom-left-radius: 13px;

    &:focus-visible {
      outline: none;

      &.radioButton {
        color: #459fff;
      }
    }
  }

  .radioButtonRight {
    border-left: solid 2px #1c1d23;
    border-top-right-radius: 13px;
    border-bottom-right-radius: 13px;

    &:focus-visible {
      outline: none;

      &.radioButton {
        color: #459fff;
      }
    }
  }

  .hiddenRadio {
    display: none;
  }
}

.inputfiled.inputfiled {
  padding: 6px 16px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.04);
  width: 250px;
  height: 40px;
  border: none;
  flex: 1;
   transition: all 0.2s ease;
  &:focus {
    border: none;
    color: #459fff;
    caret-color: #459fff;
  }


  &.pOInput {
    border: 0px solid transparent;
    transition: all 0.2s ease;

    &:focus {
      border: 0px solid transparent;
      color: #fff;
      background-image: linear-gradient(126deg, #1c40e7 -20%, #16b9ff 114%), linear-gradient(286deg, #fff 116%, #1a1b20 30%);
      box-shadow: inset 3px 3px 7px rgba(0, 0, 0, 1);
      &::placeholder {
        color: #fff;
      }
    }

  }

    &::placeholder {
      font-family: Syncopate;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1;
      letter-spacing: 0.56px;
      text-align: left;
      color: #616575;
    }

  }

  .poInputMain {
  position: relative;

  .hasValue {
    position: relative;
    &:focus-within {
      overflow: hidden;
      z-index: 0;
      border-radius: 12px;

      &::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        padding: 1px;
        background: linear-gradient(to bottom right, #1a1b20 61%, #fff 294%);

        -webkit-mask:
          linear-gradient(#fff 0 0) content-box,
          linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;

        background-clip: border-box;
        z-index: -1;
        pointer-events: none;
      }


    }
    
     .pOInput {
      &:focus{
        border: 0px solid transparent;
        background: transparent;
      }
     }
  }

}
.formInputGroup {
  position: sticky;
  top: 0;
  z-index: 0;
  padding: 32px 40px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  font-family: Syncopate;
  font-size: 14px;
  line-height: 1.2;
  letter-spacing: 0.56px;
  color: #616575;
  // background-image: linear-gradient(79deg, #0f0f14 50%, #393e47 135%);
}

.createPOContainerWrapper {
  flex: 1;
  position: relative;
  .createPOContainerWrapperInner {
    position: absolute;
    inset: 0;
  }
}

.createPOContainer {
  // overflow-y: auto;
  height: 100%;
  max-height: calc(100% - 40px);
  position: relative;
  scroll-behavior: smooth;


  &::-webkit-scrollbar {
    width: 0px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    border-image-source: radial-gradient(circle at 31% -2%, #fff, rgba(255, 255, 255, 0) 23%);
    background-image: linear-gradient(to bottom, #191a20, #191a20), radial-gradient(circle at 31% -2%, #fff, rgba(255, 255, 255, 0) 23%);
  }
}

.removeFooter {
  max-height: 100%;
}

.deliverByButton1 {
  width: 100%;
  height: 40px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.04);
  display: flex;
  border: 1px solid transparent;
  font-family: Inter;
  font-size: 14px;
  letter-spacing: 0.56px;
  color: #fff;

  &:hover {
    border-color: #459fff;
  }

  .leftSideSpan {
    width: 72px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.04);
    border-top-left-radius: 13px;
    border-bottom-left-radius: 13px;
  }

  .rightSideSpan {
    color: #fff;
    width: calc(100% - 72px);
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40px;
  }
}

.deliverByButton2 {
  width: 100%;
  height: 40px;
  padding: 12px 20px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.04);
  font-family: Syncopate;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: 0.56px;
  text-align: left;
  color: #616575;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    border-color: #459fff;
  }

  &:focus-visible {
    outline: none;
    border-color: #459fff;
  }
}

.datePickerPopper {
  background-color: #1a1a1a;
  color: white;
  border-radius: 8px;
  border: 1px solid #30363d;
}

.headerContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-image: linear-gradient(164deg, #0f0f14 -30%, #393e47 230%);
  padding: 8px 40px;
  width: 100%;
  position: absolute;
  top: 0px;
  left: 0;
  right: 0;
  z-index: 2;
  opacity: 0;
  height: 40px;
  cursor: pointer;

  .headerItem {
    &:nth-child(1) {
      width: 194px;
    }

    &:nth-child(2) {
      width: 115px;
    }

    &:nth-child(3) {
      width: 38px;
    }

    &:nth-child(4) {
      width: 141px;
    }

    font-family: Inter;
    font-size: 15px;
    font-weight: normal;
    letter-spacing: 0.6px;
    text-align: left;
    color: rgba(69, 159, 255, 0.8);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;

    &:hover {
      color: #459fff;
      font-weight: bold;
    }
  }
}

.showHeader {
  opacity: 1;
}


.addPoLineTable {
  position: relative;
  max-height: 100%;
  overflow-y: scroll;
  z-index: 3;
  width: 100%;
   box-shadow: 0 -16px 15.1px -11px rgba(0, 0, 0, 0.6);
  border-style: solid;
  border-width: 1px;
  border-image-source: linear-gradient(182deg, #fff -75%, #1a1b21 26%);
  border-image-slice: 1;  
  background-color: #1e1e24;
  padding: 0px 8px;

  &::-webkit-scrollbar {
    width: 0px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    border-image-source: radial-gradient(circle at 31% -2%, #fff, rgba(255, 255, 255, 0) 23%);
    background-image: linear-gradient(to bottom, #191a20, #191a20), radial-gradient(circle at 31% -2%, #fff, rgba(255, 255, 255, 0) 23%);
  }

  thead {
    width: 100%;
    position: sticky;
    top: 0px;
    z-index: 100;
    background-color: #1e1e24;
  }

  thead,
  tbody tr {
    display: table;
    width: 100%;
  }

  table {
    width: 100%;
    border-spacing: 1px;
    border-collapse: collapse;

    tr {

      &.sellerUserLine{
        margin-bottom: 0px;
        .poDescriptionDiv{
          background-color: transparent;
          height: auto;
          .poDescription{
            padding: 0px;
            height: 100px;

          }
        }
        .poQtyValue.poQtyValue{
          background-color: transparent;
          width: 80px;
        }
      }


      .isCounterResolved{
        color: #2c8a4a;
        font-weight: bold;
      }
      .isCounterPending{
         color: #ff8c4c;
         font-weight: bold;
      }
      .isCounterRejected{
        color: #ff4848;
         font-weight: bold;
      }

      .pointRight{
        position: absolute;
        left:-9px;
        top: 50%;
        transform: translateY(-50%);
      }

      th {
        font-family: Syncopate;
        font-size: 16px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: normal;
        text-align: left;
        color: #9b9eac;
        padding-bottom: 16px;

        &:nth-child(1) {
          width: 13%;
          padding: 0px;
          text-align: center;
        }

        &:nth-child(2) {
           width: 25%;
           min-width: 292px;
           padding-top: 16px;
        }

        &:nth-child(3) {
          width: 20%;
          padding-top: 16px;
          text-align: center;
        }

        &:nth-child(4) {
          width: 15%;
          padding-top: 16px;
          text-align: center;
        }

        &:nth-child(5) {
          width: 17%;
          padding-right: 22px;
          padding-top: 16px;
          text-align: right;
        }
      }
    }

        tr {

          position: relative;

          .descriptionDispute{
            padding-right: 24px;
          }
    
          .disputetblCol.disputetblCol {
            width: 52%;
    
            .disputeContainer {
              margin: 0 10px 0 0px;
              padding: 2px 2px 8px;
              background-color: #18181d;
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              border-radius: 0px 20px 20px 0px;
              min-height: 165px;

              .disputeCounterHeader{
                margin-bottom: auto;
                width: 100%;
              }
    
              .disputeCounterContainer1 {
                width: 100%;
                padding: 12px;
                background-color: #27272d;
                display: flex;
                justify-content: flex-start;
                flex-direction: column;
                row-gap: 4px;
                border-radius: 0px 20px 0px 0px;
                &.disputeContainerFirst{
                   max-height: 83px;
                }
             
                .disputeCounterScroll{
                      overflow: auto;
                      padding-right: 3px;
                      display: flex;
                      flex-direction: column;
                      row-gap: 2px;
                      padding-bottom: 1px;

                  &::-webkit-scrollbar {
                      width: 4px;
                      height: 4px;
                    }
                  
                    &::-webkit-scrollbar-track {
                      background: transparent;
                    }
                  
                    &::-webkit-scrollbar-thumb {
                      background: #8b91a6;
                      border-radius: 24px;
                    }
                }
              
                

                &.disputeContainerLast{
                  border-radius: 0px;
                  margin-top: 4px;
                   .disputeCounterStatusIcon{
                            position: absolute;
                            top: 1px;
                            left: -15px;
                    }
                }
    
                .disputeCounterData {
                  font-family: Inter;
                  font-size: 12px;
                  font-weight: normal;
                  font-stretch: normal;
                  font-style: normal;
                  line-height: 1.4;
                  letter-spacing: normal;
                  text-align: left;
                  color: #fff;
                  display: grid;
                  grid-template-columns: 45% 25% 30%;
    
                  span {
                    &.disputeCounterDataFirstCol {
                      display: flex;
                      column-gap: 13px;
                      justify-content: flex-start;
                      flex: 1;
                      padding-left: 20px;
    
                      .disputeCounterStatus {
                        width: 70px;
                        position: relative;
    
                        .disputeCounterStatusLbl {
                          font-weight: 300;
                          margin-right: 3px;

                          .disputeCounterStatusIcon{
                            position: absolute;
                            top: 1px;
                            left: -15px;
                          }
                        }
                      }
    
                      .disputeCounterQtyUnit {
                        display: flex;
                        column-gap: 8px;
                        .disputeQtyUnit{
                          text-transform: uppercase;
                        }
                      }
                    }
    
                    &.disputeCounterPriceUnit {
                      text-align: left;
                      flex: 1;
                      .disputePriceUnit{
                        margin-left: 6px;
                        text-transform: uppercase;
                      }
                    }
    
                    &.disputeCounterExtended {
                      text-align: right;
                      flex: 1;
                    }
                  }
    
                }
              }
            }
    
            .disputeCounterBtnSection {
              width: 100%;
    
              .actionRequiredContainer {
                margin-top: 31px;
                font-family: Inter;
                font-size: 12px;
                font-weight: 300;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.4;
                letter-spacing: 0.84px;
                text-align: left;
                color: #dbdcde;
                display: flex;
                flex-direction: column;
                padding-left: 8px;
    
                span {
                  &:nth-child(1) {
                    font-style: italic;
                  }
                }
              }

              .rejectReasonContainer{
                  font-family: Inter;
                  font-size: 12px;
                  font-weight: 300;
                  font-stretch: normal;
                  font-style: normal;
                  line-height: 1.4;
                  letter-spacing: 0.84px;
                  text-align: left;
                  color: #ff4848;
                  padding-left: 12px;
              }

              .cancelRequestSentContainer{
                  font-family: Inter;
                  font-size: 14px;
                  font-weight: 300;
                  font-stretch: normal;
                  font-style: normal;
                  line-height: 1.4;
                  letter-spacing: 0.98px;
                  text-align: left;
                  color: #ff4848;
                   padding-left: 12px;
              }

              .restockingFeeContainerBuyer {
                .restockingFeeBuyerInner {
                  padding: 0px 12px;
                  display: flex;
                  column-gap: 6px;
                  font-family: Inter;
                  font-size: 12px;
                  font-weight: 300;
                  font-stretch: normal;
                  font-style: italic;
                  line-height: 1.3;
                  letter-spacing: 0.84px;
                  text-align: left;
                  color: #dbdcde;
                  margin: 4px 0px 16px 0px;

                  .restockingFeeAmount {
                    font-weight: 600;
                    font-style: normal;
                  }
                }
              }

              .restockingFeeContainerBuyer1{
                  width: 100%;
                padding: 12px;
                display: flex;
                justify-content: flex-start;
                flex-direction: column;
                row-gap: 4px;
                .row1{
                  display: grid;
                  grid-template-columns: 25% 30% 25%;
                   font-family: Inter;
                  font-size: 12px;
                  font-weight: 300;
                  font-stretch: normal;
                  font-style: normal;
                  line-height: 1.4;
                  letter-spacing: 0.84px;
                  text-align: left;
                  color: #fff;
                }
              }

              .disputeCounterAcceptReject{
                  display: flex;
                  flex-direction: column;
                  margin-top: 4px;
                  .disputeAcceptRejectText{
                    font-family: Inter;
                    font-size: 12px;
                    font-weight: 300;
                    line-height: 1.4;
                    letter-spacing: 0.84px;
                    text-align: left;
                    color: #dbdcde;
                    margin-bottom:7px;
                  }

                  .disputeBtnGrid{
                    width: 60%;
                    display: flex;
                    margin-left: auto;
                    column-gap: 8px;
                     button{
                      flex: 1;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      height: 28px;
                      border-radius: 500px;
                      background-color: rgba(255, 255, 255, 0.04);
                      font-size: 12px;
                      font-weight: normal;
                      line-height: 1.4;
                      letter-spacing: normal;
                      text-align: center;
                      color: rgba(255, 255, 255, 0.5);
                      transition: all 0.2s ease;
                      &:hover{
                        color: #fff;
                      }
                     }
                  }
                  
              }
    
              .disputeCounterBtnGrid {
                display: flex;
                column-gap: 10px;
                margin-top: 4px;
                width: 100%;
                // padding: 0px 10px;
    
                .exportContainer {
                  flex: 1;
    
                  button.exportButton {
                    width: 100%;
                    height: 28px;
                    border-radius: 500px;
                    font-family: Inter;
                    font-size: 12px;
                    font-weight: normal;
                    line-height: 1.4;
                    letter-spacing: normal;
                    text-align: center;
                    color: rgba(255, 255, 255, 0.5);
    
                    span {
                      &:first-child {
                        padding-left: 20px;
                      }
                    }
    
                    .exportArrow {
                      margin-left: auto;
                    }
                  }
                }
              }

              .disputeCounterBtnGrid1{
                display: flex;
                flex-direction: column;
                column-gap: 10px;
                margin-top: 4px;
                width: 100%;

                &.disableButtons{
                  button{
                   &[disabled]{
                    opacity: 0.3;
                    cursor: not-allowed;
                   }
                  }
                }

                .counterStatusQueue{
                  font-family: Inter;
                  font-size: 12px;
                  font-weight: normal;
                  font-style: italic;
                  line-height: 1.4;
                  text-align: right;
                  color: #eaecf0;
                  margin:3px 10px 8px 0px;

                }
                .counterStatusQueueRejected{
                  font-family: Inter;
                  font-size: 12px;
                  font-weight: normal;
                  font-style: italic;
                  line-height: 1.4;
                  text-align: right;
                  color: #ff4848;
                  margin:3px 10px 8px 0px;
                }
              }
    
              .btnAcceptCounter {
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 0px 20px;
                height: 28px;
                border-radius: 500px;
                background-color: rgba(255, 255, 255, 0.04);
                font-family: Inter;
                font-size: 12px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.4;
                letter-spacing: normal;
                text-align: center;
                color: rgba(255, 255, 255, 0.5);
    
                &:hover {
                  color: #fff;
                }
                &[disabled]{
                  background-color: rgba(255, 255, 255, 0.04);
                  color: rgba(255, 255, 255, 0.5);
                  opacity: unset;
                }
              }

              .btnAcceptCounter.accepted{
                color: #32ff6c;
              }
              .btnAcceptCounter.rejected{
                color: #ff4848;
              }

              .exportContainer{
                flex: 1;
              }
    
              .disputeCounterInputGrid {
                .disputeInputGridMain {
                  display: flex;
                  justify-content: center;
                  column-gap: 1px;
                  margin-top: 1px;
    
                  .disputeBox {
                    display: flex;
                    justify-content: center;
                    flex: 1;
                    padding: 6px 16px 5px 18px;
                    background-color: #27272d;
                  }
    
                  .disputeInputGrid {
                    input {
                      width: 95px;
                      height: 40px;
                      border-radius: 13px;
                      box-shadow: inset 4px 4px 10.1px 0 #000;
                      font-family: Inter;
                      font-size: 12px;
                      font-weight: 300;
                      font-stretch: normal;
                      font-style: normal;
                      line-height: 1.4;
                      letter-spacing: 0.84px;
                      text-align: center;
                      color: #fff;
                    }
                  }
    
                  .deliveryDateContainer {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    font-family: Inter;
                    font-size: 12px;
                    font-weight: 300;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.4;
                    letter-spacing: 0.84px;
                    text-align: left;
                    color: #c3c4ca;
                    .deliveryDateContainerData{
                      color: #fff;
                    }
                  }
                }
    
                .disputeCounterInputBtnGrid {
                  display: flex;
                  column-gap: 10px;
                  padding: 0px 8px;
                  margin-top: 16px;
                }
    
              }
            }

            .deliveryNewLineContainer{
               padding: 6px 18px 7px 10%;
               background-color: #27272d;
               display: flex;
               flex-direction: column;
               row-gap: 4px;
               margin-top: 1px;
               width: 100%;
               font-family: Inter;
              font-size: 12px;
              font-weight: 300;
              font-stretch: normal;
              font-style: normal;
              line-height: 1.4;
              letter-spacing: 0.84px;
              text-align: left;
              color: #fff;
              .deliveryNewLineData{
                display: grid;
                grid-template-columns: 35% 45% 20%;
                column-gap: 16px;
                justify-content: center;
                .deliveryDateLbl{
                  color: #c3c4ca;
                }
                .deliveryDateStrike{
                    color: #71737f;
                     text-decoration: line-through;
                }
              }
            }

            .disputeCounterContainer2{
              padding: 12px 10px 0px 10px;
            }
            
             // Restocking Fee 
            .disputeCounterRestockingFee{
              display: flex;
              flex-direction: column;
               padding: 12px 8px 0px 10px;
              .disputeCounterRestockingFeeInput{
                display: flex;
                align-items: center;
                justify-content: flex-end;
                column-gap: 10px;
                .restockingFeeLbl{
                  flex-grow: 0;
                  font-family: Inter;
                  font-size: 12px;
                  font-weight: 600;
                  font-stretch: normal;
                  font-style: normal;
                  line-height: 1;
                  letter-spacing: 0.48px;
                  text-align: center;
                  color: #fff;
                }

                input{
                    min-width: 107px;
                    width: 28%;
                    height: 36px;
                    padding: 10px 6.5px 8px 10px;
                    border-radius: 10px;
                    box-shadow: inset 4px 5px 2.2px 0 #000;
                    border-style: solid;
                    border-width: 1.5px;
                    background-image: linear-gradient(to bottom, #1f1f25, #1f1f25), linear-gradient(357deg, #fff 255%, #101015 32%);
                    font-family: Inter;
                    font-size: 14px;
                    font-weight: normal;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1;
                    letter-spacing: 0.56px;
                    text-align: right;
                    color: #fff;
                  
                }
              }

              .disputeCounterBtnSkip{
                display: flex;
                column-gap: 10px;
                margin-top: 16px;
                justify-content: flex-end;
                .skipBtn{
                   min-width: 107px;
                   width: 25%;
                  height: 28px;
                  padding: 5px 41px 6px;
                  border-radius: 500px;
                  background-color: rgba(255, 255, 255, 0.04);
                    font-family: Inter;
                  font-size: 12px;
                  font-weight: normal;
                  font-stretch: normal;
                  font-style: normal;
                  line-height: 1.4;
                  letter-spacing: normal;
                  text-align: center;
                  color: rgba(255, 255, 255, 0.5);
                  transition: all 0.2s ease;
                  &:hover{
                    color: #fff;
                  }
                }
                .submitBtn{
                   min-width: 107px;
                   width: 25%;
                  height: 28px;
                  flex-grow: 0;
                  padding: 5px 10px 6px 10px;
                  border-radius: 500px;
                  background-color: #fff;
                   font-family: Inter;
                  font-size: 12px;
                  font-weight: normal;
                  font-stretch: normal;
                  font-style: normal;
                  line-height: 1.4;
                  letter-spacing: normal;
                  text-align: center;
                  color: #0f0f14;
                   transition: all 0.2s ease;
                    &[disabled]{
                     background-color: rgba(255, 255, 255, 0.04);
                      color: rgba(255, 255, 255, 0.5);
                     opacity: unset;
                  }
                }
              }

            }

            .disputeLastCounterRestockingFee{
              display: flex;
              flex-direction: column;
              padding: 4px 8px 0px 8px;
              .restockingFeeLbl{
                font-family: Inter;
                font-size: 12px;
                font-weight: 300;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.3;
                letter-spacing: 0.84px;
                text-align: left;
                color: #dbdcde;
              }
              .counterStatusQueue{
                font-family: Inter;
                font-size: 12px;
                font-weight: normal;
                font-stretch: normal;
                font-style: italic;
                line-height: 1.4;
                letter-spacing: normal;
                text-align: right;
                color: #eaecf0;
              }
              .restockingFeeOriginal{
                display: flex;
                column-gap: 16px;
                justify-content:center;
                align-items: center;
                 font-family: Inter;
                font-size: 12px;
                font-weight: 300;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.4;
                letter-spacing: 0.84px;
                text-align: left;
                color: #c3c4ca;

                .editFeeBtn{
                    width: 107px;
                    height: 28px;
                    flex-grow: 0;
                    margin: 5px 6px 0 20px;
                    padding: 5px 31px 6px 30px;
                    border-radius: 500px;
                    background-color: rgba(255, 255, 255, 0.04);
                      font-family: Inter;
                    font-size: 12px;
                    font-weight: normal;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.4;
                    letter-spacing: normal;
                    text-align: center;
                    color: rgba(255, 255, 255, 0.5);
                }
              }
            }

            // Edit Counter

            .editCounterContainer{
              display: flex;
              flex-direction: column;
               font-family: Inter;
              font-size: 12px;
              font-weight: 300;
              font-stretch: normal;
              font-style: normal;
              line-height: 1.3;
              letter-spacing: normal;
              text-align: left;
              color: #dbdcde;
               .editCounterBtnContainer{
                    display: flex;
                    align-items: center;
                    column-gap: 10px;
                    margin-top: 16px;
                    justify-content: space-between;
                    .btnAcceptCounter{
                      min-width: 160px;
                      width: 40%;
                      flex:unset
                    }

                    .submitCounterdStatus{
                      font-family: Inter;
                      font-size: 12px;
                      font-weight: normal;
                      font-stretch: normal;
                      font-style: italic;
                      line-height: 1.4;
                      letter-spacing: normal;
                      text-align: right;
                      color: #ff8c4c;
                    }
                    .editCounterBtnContainerInner{
                      display: flex;
                      align-items: center;
                      flex-direction: row;
                      column-gap: 10px;
                    }
               }
            }
    
    
          }
    
          td {
            margin-bottom: 20px;
            vertical-align: top;
            padding: 8px 0px;
    
    
            &:nth-child(1) {
              width: 13%;
    
              .prodId {
                justify-content: center;
              }
            }
    
            &:nth-child(2) {
              min-width: 292px;
              width: 25%;
              position: relative;
            }
    
            &:nth-child(3) {
              width: 20%;
              text-align: center;
              .showLineCanceledDetails{
                padding-top: 49px;
              }
            }
    
            &:nth-child(4) {
              width: 15%;
              .showLineCanceledDetails{
                padding-top: 72px;
              }
            }
    
            &:nth-child(5) {
              width: 17%;
              padding-right: 22px;
              .showLineCanceledDetails{
                padding-top: 72px;
              }
            }
    
            .prodId {
              display: flex;
              align-items: center;
              position: relative;
              flex-direction: column;
    
              .lineNumberContainer {
                display: flex;
                flex-direction: column;
                gap: 5px;
                align-items: center;
                justify-content: center;
                text-align: center;
    
                .hiddenCheckbox {
                  display: none;
                }
    
                .usaOnlyText {
                  font-family: Syncopate;
                  font-size: 12px;
                  color: rgba(255, 255, 255, 0.5);
                  font-weight: normal;
    
                  &.visibilityHidden {
                    visibility: hidden;
                  }
                }
              }
    
              .domesticCheckText {
                position: absolute;
                top: 44px;
                left: 50%;
                transform: translateX(-50%);
              }
            }
    
            .poDescriptionDiv {
              width: 100%;
              min-width: 292px;
              height: 162px;
              border-radius: 13px;
              background-color: rgba(255, 255, 255, 0.04);
              transition: all 0.1s;
    
              &:focus-within {
                box-shadow: inset 4px 4px 10.1px 0 #000;
                background-color: transparent;
                background: url(../../../assets/images/POInputActive.svg) no-repeat;
                background-position: bottom right;
                background-size: cover;
    
                .partNumberFiled {
                  box-shadow: inset 4px 4px 10.1px 0 #000;
                }
    
                // .poDescription {
                //   color: #459fff;
                // }
              }
    
              &.disabled {
                cursor: not-allowed;
              }
    
              .poDescription {
                background-color: transparent;
                border: 0;
                width: 100%;
                height: 117px;
                resize: none;
                caret-color: #459fff;
                font-family: Inter;
                font-size: 14px;
                font-weight: 300;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.4;
                letter-spacing: 0.98px;
                text-align: left;
                color: #eaecf0;
                padding-left: 16px;
                padding-right: 16px;
                padding-top: 16px;
                overflow: hidden;
    
                &[readonly] {
                  text-transform: uppercase;
                }
    
    
    
                &::placeholder {
                  font-family: Syncopate;
                  font-size: 14px;
                  font-weight: normal;
                  font-stretch: normal;
                  font-style: normal;
                  line-height: normal;
                  letter-spacing: 0.56px;
                  text-align: left;
                  color: #b5b6bb;
                }
    
                &:focus-within {
                  outline: none;
                  box-shadow: none;
    
                  &::placeholder {
                    font-family: Syncopate;
                    font-size: 14px;
                    font-weight: normal;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: normal;
                    letter-spacing: 0.56px;
                    text-align: left;
                    color: #616575;
                    line-height: 22px;
    
                    &::first-line {
                      font-weight: normal;
                    }
                  }
                }
    
                &:disabled {
                  cursor: not-allowed;
    
                  &::placeholder {
                    font-family: Syncopate;
                    font-size: 14px;
                    font-weight: normal;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: normal;
                    letter-spacing: 0.56px;
                    text-align: left;
                    color: #b5b6bb;
                    line-height: 21px;
                  }
                }
              }
    
              .poDescriptionOpen {
                height: 47px;
              }
    
              .partNumberFiled {
                width: calc(100% - 1px);
                height: 40px;
                background-color: transparent;
                border: none;
                padding: 0px 16px 0px 16px;
                font-family: Syncopate;
                font-size: 14px;
                font-weight: normal;
                line-height: normal;
                letter-spacing: 0.56px;
                text-align: left;
                color: #616575;
                border-bottom-left-radius: 13px;
                border-bottom-right-radius: 13px;
                text-transform: uppercase;
    
                input {
                  font-family: Syncopate;
                  font-size: 14px;
                  font-weight: normal;
                  line-height: normal;
                  letter-spacing: 0.56px;
                  text-align: left;
                  color: #fff;
                  width: 100%;
                  height: 100%;
                  border: none;
                  background: transparent;
                  padding: 0px 0px 0px 1px;
                  text-transform: uppercase;
    
                  &:disabled {
                    cursor: not-allowed;
                  }
    
                  &::placeholder {
                    color: #616575;
                  }
    
                  &:focus-within {
                    border: none;
                    outline: none;
                    box-shadow: none;
    
                    &::placeholder {
                      color: #616575;
                      font-weight: bold;
                    }
                  }
    
                  &:focus {
                    &::placeholder {
                      color: #32ccff;
                    }
    
                  }
                }
    
                span {
                  position: absolute;
                  right: 10px;
                  top: 4px;
                }
    
              }
    
            }
    
            .poQty,
            .poPerUm {
              width: 80px;
              position: relative;
    
              .pricePerUnitContainer {
                position: relative;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 4px;
    
                &:hover {
                  .priceHistoryDropdown {
                    display: block;
                  }
                }
    
                .priceHistoryIndicator {
                  font-size: 8px;
                  color: rgba(255, 255, 255, 0.6);
                  margin-left: 2px;
                }
    
                .priceChangeArrow {
                  font-size: 10px;
                  font-weight: 600;
                  margin-right: 4px;
                  display: inline-block;
                  cursor: pointer;
                }
              }
    
              .priceHistoryDropdown {
                position: absolute;
                top: 100%;
                left: 0;
                z-index: 1000;
                background: rgba(35, 35, 35, 0.98);
                border-radius: 8px;
                padding: 16px;
                min-width: 320px;
                box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.15);
                display: none;
                margin-top: 4px;
                animation: fadeIn 0.2s ease-in-out;
    
                @keyframes fadeIn {
                  from {
                    opacity: 0;
                    transform: translateY(-10px);
                  }
    
                  to {
                    opacity: 1;
                    transform: translateY(0);
                  }
                }
    
                .priceHistoryRow {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  padding: 12px 0;
                  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    
                  &:last-child {
                    border-bottom: none;
                  }
    
                  .priceHistoryLabel {
                    font-size: 13px;
                    color: #aaa;
                    font-weight: 500;
                    min-width: 70px;
                    text-align: left;
                  }
    
                  .priceHistoryValues {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    font-size: 13px;
                    color: #aaa;
    
                    .priceHistoryQty,
                    .priceHistoryUnit,
                    .priceHistoryTotal {
                      color: #aaa;
                      font-weight: 400;
                    }
    
                    .priceHistoryPrice {
                      color: #aaa;
                      font-weight: 500;
                      display: flex;
                      align-items: center;
                      gap: 4px;
    
                      &.currentPrice {
                        color: #ff8c00;
                        font-weight: 600;
                      }
    
                      .currentPriceArrow {
                        font-size: 10px;
                        color: #ff8c00;
                        font-weight: 600;
                      }
                    }
                  }
                }
              }
    
              .errorInput {
                border: 1px solid red;
    
                &:focus-within {
                  border: solid 1px red;
                  outline: none;
                  box-shadow: none;
                }
              }
    
              .selectUom {
                .uomDrodown {
                  font-family: Inter;
                  font-size: 14px;
                  line-height: normal;
                  letter-spacing: normal;
                  color: #fff;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  vertical-align: middle;
                  height: 45px;
                  padding-left: 4px;
                  border: 0;
                  text-transform: uppercase;
    
                  &:focus-visible {
                    outline: none;
                  }
    
                  div:nth-child(1) {
                    padding: 0px 32px 0px 0px;
                  }
    
                  .selectDropdown {
                    padding: 0px;
                  }
    
                  .MuiSelect-select {
                    padding: 20px 6px 6px 6px;
                  }
    
                  svg {
                    transform: unset;
                    color: rgba(255, 255, 255, 0.6);
                    ;
                    right: 10px;
                  }
    
                  fieldset {
                    border: 0;
                  }
                }
              }
    
              .selectUom1 {
                .uomDrodown {
                  font-family: Inter;
                  font-size: 14px;
                  line-height: normal;
                  letter-spacing: normal;
                  color: #fff;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  vertical-align: middle;
                  height: 45px;
                  padding-left: 18px;
                  border: 0;
                  text-transform: uppercase;
    
                  &:focus-visible {
                    outline: none;
                  }
    
                  div:nth-child(1) {
                    padding: 0px 32px 0px 0px;
                  }
    
                  .selectDropdown {
                    padding: 0px;
                  }
    
                  .MuiSelect-select {
                    padding: 20px 6px 6px 6px;
                  }
    
                  svg {
                    transform: unset;
                    color: rgba(255, 255, 255, 0.6);
                    ;
                    right: 10px;
                  }
    
                  fieldset {
                    border: 0;
                  }
                }
              }
    
            }
    
            .poQty {
              color: rgba(255, 255, 255, 0.6);
              font-weight: normal;
            }
    
            .poQty {
              display: flex;
    
              .poQtyValue {
                border-radius: 10px;
                background-color: rgba(255, 255, 255, 0.04);
                width: 95px;
                height: 45px;
                text-align: center;
                display: flex;
                align-items: center;
                justify-content: center;
                font-family: Inter;
                font-size: 15px;
                font-weight: normal;
                line-height: normal;
                letter-spacing: normal;
                text-align: center;
                color: #fff;
                border: 0;
              }
    
              input {
                width: 95px;
                height: 45px;
                transition: all 0.1s;
    
                &:focus {
                  background: url(../../../assets/images/POInputActive.svg) no-repeat transparent;
                  background-position: bottom right;
                  box-shadow: inset 4px 4px 10.1px 0 #000;
                  border: 0;
                }
    
                &:disabled {
                  cursor: not-allowed;
                }
    
              }
    
    
            }
    
            .poQtyContainer {
              display: flex;
              flex-direction: column;
              align-items: center;
              gap: 35px;
              position: relative;
    
              .actionBtns {
                display: flex;
                flex-direction: column;
                gap: 10px;
                margin-left: 18px;
    
                .btn {
                  width: 95px;
                  height: 24px;
                  border-radius: 100px;
                  background-color: rgba(255, 255, 255, 0.04);
                  font-family: Inter;
                  font-size: 12px;
                  font-weight: normal;
                  font-stretch: normal;
                  font-style: normal;
                  line-height: 1.5;
                  letter-spacing: -0.13px;
                  text-align: center;
                  color: rgba(255, 255, 255, 0.6);
                  border: none;
                  cursor: pointer;
                  transition: all 0.2s ease;
    
                  &:not([disabled]):hover {
                    background-color: rgba(255, 255, 255, 0.08);
                  }

                  &[disabled] {
                    cursor: not-allowed;
                  }
    
                  .leftSideSpan {
                    font-weight: 500;
                    margin-right: 8px;
                  }
    
                  .rightSideSpan {
                    font-weight: normal;
                  }
    
                  .arrowIcon {
                    display: inline-flex;
                    align-items: center;
                    margin-left: 4px;
    
                    svg {
                      width: 12px;
                      height: 12px;
                      opacity: 0.6;
                    }
                  }
                }
    
                .deliverNewLineContainer.deliverNewLineContainer {
                  position: relative;
                  display: inline-block;
    
                  .btn {
                    width: 207px;
                    height: 36px;
                    padding: 0px 0px 0px 12px;
                    border-radius: 500px;
                    background-color: rgba(255, 255, 255, 0.04);
                    font-family: Inter;
                    font-size: 14px;
                    font-weight: 300;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.4;
                    letter-spacing: 0.98px;
                    text-align: left;
                    color: #fff;
                    display: inline-flex;
                    align-items: center;
    
                    .arrowIcon {
                      display: inline-flex;
                      align-items: center;
                      justify-content: center;
                      width: 30px;
                      height: 100%;
                      padding: 6px 2px;
                      border-left: solid 1px rgba(255, 255, 255, 0.1);
    
                      svg {
                        width: 18px;
                        height: 18px;
                      }
                    }
    
                  }
    
                  .calendarContainer {
                    position: absolute;
                    top: 100%;
                    left: 0;
                    z-index: 1000;
                    margin-top: 5px;
                    background-color: #222329;
                    border-radius: 8px;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                    // border: 1px solid rgba(255, 255, 255, 0.1);
                    min-width: 280px;
                  }
    
                  .deliveryDateContainer {
                    position: relative;
                    display: inline-block;
                  }
    
                  .clickableDeliveryDate {
                    cursor: pointer;
                    text-decoration: underline;
                    color: #007bff;
    
                    &:hover {
                      color: #0056b3;
                    }
                  }
    
                  .calendarWrapper {
                    position: absolute;
                    top: 100%;
                    left: 0;
                    z-index: 1000;
                    margin-top: 5px;
                  }

                  .selectedDateDisplay {
                    margin-top: 8px;
    
                    .selectedDateText {
                      font-family: Inter;
                      font-size: 14px;
                      font-weight: normal;
                      font-stretch: normal;
                      font-style: normal;
                      line-height: normal;
                      letter-spacing: 0.56px;
                      text-align: left;
                      color: #fff;
                    }
                  }
                }
              }
            }
    
            .skippedLineContent {
              display: flex;
              gap: 16px;
              font-family: Inter;
              font-size: 14px;
              font-weight: normal;
              line-height: 1.4;
              letter-spacing: 0.98px;
              color: #fff;
              height: 30px;
              align-items: center;
              margin-top: 5px;
    
              .skippedLineActionBtns {
                display: flex;
                gap: 8px;
    
                .btn {
                  width: 114px;
                  height: 20px;
                  border-radius: 100px;
                  background-color: rgba(255, 255, 255, 0.04);
                  font-family: Inter;
                  font-size: 12px;
                  font-weight: normal;
                  line-height: 1.5;
                  letter-spacing: -0.13px;
                  text-align: center;
                  color: rgba(255, 255, 255, 0.6);
                }
              }
            }
    
            .errorQty {
              box-shadow: inset 2.2px 2.2px 2.2px 0 #000;
              background-image: linear-gradient(159deg, #720d16 -254%, #ff4859 98%), linear-gradient(37deg, #8c8b99 168%, #2f2e33 19%) !important;
            }
    
            .poPerUm {
              font-family: Inter;
              font-size: 14px;
              text-align: right;
              color: #fff;
              display: flex;
              height: 45px;
              align-items: center;
              width: 105px;
              justify-content: center;
              margin: 0 auto;
            }
    
    
            .extendedValue {
              font-family: Inter;
              font-size: 14px;
              text-align: right;
              color: #fff;
              display: flex;
              align-items: center;
              justify-content: right;
              height: 45px;
            }
    
          }
    
          .skippedLineTd {
            width: 77% !important;
          }
        }
  }
}

.customCalendarHeaderBox.customCalendarHeaderBox {
  padding: 0px;
  padding-bottom: 10px;

  .customCalendarHeaderIcon {
    padding: 0px;
    height: 10px;
  }

  .customCalendarHeaderText {
    padding: 0px;
    height: 12px;
    font-family: Syncopate;
    font-size: 18px;
    font-weight: bold;
    line-height: 1;
    letter-spacing: 3.06px;
    text-align: left;
    color: #dfe2f0;
    text-transform: capitalize;
  }
}



.calendarOpenOverlay {
  width: 100%;
  height: 242px;
  pointer-events: none;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  position: absolute;
  z-index: 999;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
}

.isCalendarOpenDiabledInput {
  background-image: linear-gradient(79deg, #0f0f14 50%, #393e47 135%);

  input,
  .deliverToContainer,
  .addressInputs,
  .uploadBillContainer,
  .radioGroupContainer,
  .deliverByButton1,
  .deliverByButton2 {
    pointer-events: none;
  }
}

.poDescriptionFirstLine {
  &::first-line {
    font-weight: 500;
  }

  &.poDescriptionFirstLine1.poDescriptionFirstLine1 {
    padding-right: 0px !important;

    &::first-line {
      letter-spacing: 0;
    }
  }
}

.poDescriptionDivFocused {
  box-shadow: inset 4px 4px 10.1px 0 #000;

  .partNumberFiled {
    box-shadow: inset 4px 4px 10.1px 0 #000;
   
    input {
      &::placeholder {
        // color: #616575;
        font-weight: bold;
      }
    }
  }

  .poDescription {
    color: #459fff;
  }
}

.formInputGroup {
  will-change: transform, opacity;
  transform-origin: top;
  background-color: #191a20;
}

.addPoLineTable {
  will-change: transform;
}

.createPOContainer {
  position: relative;
  height: 100%;
  overflow: auto;
  background-color: #1e1e24;
}

.productDescription {
  width: 100%;
  display: inline-flex;
}

.miscellaneousText.miscellaneousText {
  padding: 8px 4px 8px 4px !important;

  .liFisrtLine.liFisrtLine {
    letter-spacing: 0;
  }
}


.uploadBOMLineTable {
  position: relative;
  max-height: calc(100% - 40px);
  overflow-y: scroll;
  z-index: 1;
  width: 100%;
  box-shadow: 0 -16px 15.1px -11px rgba(0, 0, 0, 0.6);
  border-image-source: radial-gradient(circle at 31% -2%, #fff, rgba(255, 255, 255, 0) 23%);
  background-image: linear-gradient(102deg, #0f0f14 -8%, #393e47 238%);
  min-height: 635px;

  &::-webkit-scrollbar {
    width: 0px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    border-image-source: radial-gradient(circle at 31% -2%, #fff, rgba(255, 255, 255, 0) 23%);
    background-image: linear-gradient(to bottom, #191a20, #191a20), radial-gradient(circle at 31% -2%, #fff, rgba(255, 255, 255, 0) 23%);
  }

  thead {
    position: sticky;
    top: 0px;
    z-index: 100;
    border-top: 1px solid rgba(255, 255, 255, 0.2980392157);
    background-image: linear-gradient(102deg, #0f0f14 -8%, #393e47 275%);
  }

  thead,
  tbody tr {
    display: table;
  }

  table {
    width: 100%;
    border-spacing: 1px;
    border-collapse: collapse;

    tr {

      th {
        font-family: Syncopate;
        font-size: 16px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: normal;
        text-align: left;
        color: #9b9eac;
        padding-bottom: 16px;

        &:nth-child(1) {
          width: 117px;
          padding: 0px;
          text-align: center;
        }

        &:nth-child(2) {
          width: 292px;
          padding-top: 16px;
          padding-left: 15px;
        }

        &:nth-child(3) {
          width: 233px;
          padding-left: 41px;
          padding-top: 16px;
        }

        &:nth-child(4) {
          width: 158px;
          padding-top: 16px;
        }
      }
    }

    tr {

      td {
        margin-bottom: 20px;
        width: 100%;
        vertical-align: top;

        &:nth-child(1) {
          width: 117px;
          display: flex;
          justify-content: center;
        }

        &:nth-child(2) {
          width: 292px;
        }

        &:nth-child(3) {
          width: 233px;
          padding-left: 16px;
        }

        &:nth-child(4) {
          width: 158px;
        }

        .prodId {
          display: flex;
          align-items: center;
          position: relative;

          .activeInactiveDot {
            cursor: pointer;
            width: 5px;
            height: 5px;
            border-radius: 50%;
            position: absolute;
            top: 18px;
            left: -19px;
          }

          .activeInactiveDot.active {
            background-color: #43f776;
          }

          .activeInactiveDot.inactive {
            background-color: #ff0000;
          }

          .lineNumberContainer {
            display: flex;
            flex-direction: column;
            gap: 5px;
            align-items: center;
            justify-content: center;
            text-align: center;

            .hiddenCheckbox {
              display: none;
            }

            .customNumberToggle {
              background-image: url(../../../assets/images/Product-Id-BG.svg);
              background-size: cover;
              background-position: center;
              background-repeat: no-repeat;
              border-radius: 5px;
              border: solid 0.5px rgba(255, 255, 255, 0.16);
              width: 30px;
              height: 30px;
              font-family: Syncopate;
              font-size: 16px;
              font-weight: bold;
              font-stretch: normal;
              font-style: normal;
              line-height: 1;
              letter-spacing: 1.12px;
              text-align: left;
              color: #9b9eac;
              display: flex;
              justify-content: center;
              align-items: center;
              padding-top: 3px;
              cursor: pointer;
              transition: all 0.2s ease;

              &.active {
                background-image: url(../../../assets/images/Usa-Only-Without-Text.svg);
                background-size: cover;
                background-position: center;
                background-repeat: no-repeat;
                color: #0f0f14;
                border: none;
              }

              &.disabled {
                opacity: 0.5;
                cursor: not-allowed;
              }

              &:focus-visible {
                outline: 1px solid #459fff;
                box-shadow: 0 0 3px rgba(69, 159, 255, 0.5);
              }
            }

            .usaOnlyText {
              font-family: Syncopate;
              font-size: 12px;
              color: rgba(255, 255, 255, 0.5);
              font-weight: normal;

              &.visibilityHidden {
                visibility: hidden;
              }
            }
          }

          .domesticCheckText {
            position: absolute;
            top: 44px;
            left: -28px;
          }
        }

        .skippedPlaceHolderText {
          position: absolute;
          font-family: Inter;
          font-size: 14px;
          font-weight: normal;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.4;
          letter-spacing: 0.98px;
          color: #616575;
          padding-left: 16px;
        }

        .poDescriptionDiv {
          width: 292px;
          height: 162px;
          border-radius: 13px;
          background-color: #23242a;

          &:focus-within {
            // box-shadow: inset 4px 4px 10.1px 0 #000;
            background-color: transparent;
            background: url(../../../assets/images/Upload_BOM_Description.svg) no-repeat;
            background-position: bottom right;

            .partNumberFiled {
              box-shadow: inset 4px 4px 10.1px 0 #000;
              width: calc(100% - 1px);
            }

            .poDescription {
              color: #459fff;
            }
          }

          &.disabled {
            cursor: not-allowed;
          }

          .poDescription {
            background-color: transparent;
            border: 0;
            width: 100%;
            height: 117px;
            resize: none;
            caret-color: #459fff;
            font-family: Inter;
            font-size: 14px;
            font-weight: 300;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.4;
            letter-spacing: 0.98px;
            text-align: left;
            color: #eaecf0;
            padding-left: 16px;
            padding-right: 16px;
            padding-top: 16px;
            overflow: hidden;

            &[readonly] {
              text-transform: uppercase;
            }



            &::placeholder {
              font-family: Syncopate;
              font-size: 14px;
              font-weight: normal;
              font-stretch: normal;
              font-style: normal;
              line-height: normal;
              letter-spacing: 0.56px;
              text-align: left;
              color: #b5b6bb;
            }

            &:focus-within {
              outline: none;
              box-shadow: none;

              &::placeholder {
                font-family: Syncopate;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: normal;
                letter-spacing: 0.56px;
                text-align: left;
                color: #616575;
                line-height: 22px;

                &::first-line {
                  font-weight: normal;
                }
              }
            }

            &:disabled {
              cursor: not-allowed;

              &::placeholder {
                font-family: Syncopate;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: normal;
                letter-spacing: 0.56px;
                text-align: left;
                color: #b5b6bb;
                line-height: 21px;
              }
            }
          }

          .poDescriptionOpen {
            height: 47px;
          }

          .partNumberFiled {
            width: 100%;
            height: 40px;
            background-color: transparent;
            border: none;
            padding: 0px 16px 0px 16px;
            border-bottom-left-radius: 13px;
            border-bottom-right-radius: 13px;
            text-transform: uppercase;

            input {
              font-family: Syncopate;
              font-size: 14px;
              font-weight: normal;
              font-stretch: normal;
              font-style: normal;
              line-height: normal;
              letter-spacing: 0.56px;
              text-align: left;
              color: #fff;
              width: 100%;
              height: 100%;
              border: none;
              background: transparent;
              padding: 0px 0px 0px 1px;
              text-transform: uppercase;

              &:disabled {
                cursor: not-allowed;
              }

              &::placeholder {
                color: #616575;
              }

              &:focus-within {
                border: none;
                outline: none;
                box-shadow: none;

                &::placeholder {
                  color: #616575;
                  font-weight: bold;
                }
              }
            }

            span {
              position: absolute;
              right: 10px;
              top: 4px;
            }

          }

        }

        .descriptionModeDisabled {
          position: relative;
          width: 100%;

          .textareaOverlay {
            position: absolute;
            z-index: 99;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
            border: 0;
            padding-left: 16px;
            padding-right: 10px;
            padding-top: 16px;
            overflow: hidden;
            border-radius: 13px;
            background-color: #23242a;
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.4;
            letter-spacing: 0.98px;
            text-align: left;
            color: #616575;
            cursor: pointer;

            .childCount {
              font-family: Syncopate;
              font-weight: bold;
              color: #fff;
            }

            .matches {
              font-family: Syncopate;
              font-size: 12px;
              line-height: 1.4;
              letter-spacing: 0.84px;
              color: #fff;
              text-transform: uppercase;
            }
          }
        }

        .poQty,
        .poPerUm {
          width: 80px;


          .errorInput {
            border: 1px solid red;

            &:focus-within {
              border: solid 1px red;
              outline: none;
              box-shadow: none;
            }
          }

          .selectUom {
            .uomDrodown {
              font-family: Inter;
              font-size: 14px;
              line-height: normal;
              letter-spacing: normal;
              color: #fff;
              display: flex;
              align-items: center;
              justify-content: center;
              vertical-align: middle;
              height: 45px;
              padding-left: 18px;
              border: 0;
              text-transform: uppercase;

              &:focus-visible {
                outline: none;
              }

              div:nth-child(1) {
                padding: 0px 32px 0px 0px;
              }

              .selectDropdown {
                padding: 0px;
              }

              .MuiSelect-select {
                padding: 20px 6px 6px 6px;
              }

              svg {
                transform: unset;
                color: rgba(255, 255, 255, 0.6);
                ;
                right: 10px;
              }

              fieldset {
                border: 0;
              }
            }
          }

          .selectUom1 {
            .uomDrodown {
              font-family: Inter;
              font-size: 14px;
              line-height: normal;
              letter-spacing: normal;
              color: #fff;
              display: flex;
              align-items: center;
              justify-content: center;
              vertical-align: middle;
              height: 45px;
              padding-left: 4px;
              border: 0;
              text-transform: uppercase;

              &:focus-visible {
                outline: none;
              }

              div:nth-child(1) {
                padding: 0px 32px 0px 0px;
              }

              .MuiSelect-select {
                padding: 20px 6px 6px 6px;
              }

              svg {
                transform: unset;
                color: rgba(255, 255, 255, 0.6);
                ;
                right: 10px;
              }

              fieldset {
                border: 0;
              }
            }
          }
        }

        .poQty {
          color: rgba(255, 255, 255, 0.6);
          font-weight: normal;
        }

        .poQty {
          display: flex;

          .poQtyValue {
            border-radius: 10px;
            background-color: rgba(255, 255, 255, 0.04);
            width: 95px;
            height: 45px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: Inter;
            font-size: 15px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: normal;
            letter-spacing: normal;
            text-align: center;
            color: #fff;
            border: 0;
          }

          input {
            width: 95px;
            height: 45px;
            font-family: Inter;
            font-size: 16px;
            line-height: 1.3;
            letter-spacing: -0.18px;
            text-align: center;
            color: #fff;
            caret-color: #fff;

            &:focus {
              background: url(../../../assets/images/POInputActive.svg) no-repeat transparent;
              background-position: bottom right;
              box-shadow: inset 4px 4px 10.1px 0 #000;
              border: 0;
            }

            &:disabled {
              cursor: not-allowed;
            }
          }

        }

        .poQtyBOM {
          input {
            text-align: center;
            padding: 4px 12px;
          }
        }

        .errorQty {
          box-shadow: inset 2.2px 2.2px 2.2px 0 #000;
          background-image: linear-gradient(159deg, #720d16 -254%, #ff4859 98%), linear-gradient(37deg, #8c8b99 168%, #2f2e33 19%) !important;
        }

        .poPerUm {
          font-family: Inter;
          font-size: 14px;
          text-align: center;
          color: #fff;
          display: flex;
          height: 45px;
          align-items: center;
          width: 105px;
          justify-content: center;
        }


        .extendedValue {
          font-family: Inter;
          font-size: 14px;
          text-align: right;
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: right;
          height: 45px;
        }

      }
    }
  }
}



.autocompleteDescPanelBom.autocompleteDescPanelBom {
  border-radius: 0px 0px 13px 13px;
  width: calc(100% - 1px);
  background: url(../../../assets/images/Mian-Dropdown-BG.svg) #1b1c21 no-repeat;
  background-position: bottom right;
}

.autocompleteDescInnerPanelBom.autocompleteDescInnerPanelBom {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  box-shadow: inset 4px 4px 10.1px 0 #000;
  border-radius: 0px 0px 13px 13px;
  flex-direction: column;
  width: calc(100% - 2px);
}

.matchesfoundSearch {
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: 0.98px;
  text-align: left;
  color: #616575;
  display: inline-block;
  width: 100%;

  .textStyle1 {
    font-weight: bold;
    color: #fff;
    display: inline-block;
    margin-right: 3px;
  }

  .textStyle2 {
    color: #fff;
    display: inline-block;
  }

  br {
    display: inline-block;
  }

}

.noOptionListPanelBom.noOptionListPanelBom {
  background: transparent !important;
  box-shadow: unset !important;
}

.noOptionListInnerPanelBom {
  border-radius: 0px !important;
  box-shadow: unset !important;
}

.noOptionPanelBom1.noOptionPanelBom1 {
  padding: 0px !important;
}

.noOptionPanelBom.noOptionPanelBom {
  display: none;
}


.visibleNoOptionBom.visibleNoOptionBom {
  display: flex;
  align-items: center;
  font-family: Inter;
  font-size: 14px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: left;
  color: #eaecf0;
  width: 100%;
  height: 50px;
  flex-grow: 0;
  padding: 6px 12px 4px 16px !important;
  box-shadow: inset 4px 4px 10.1px 0 #000;
  background-color: #1b1c21;
}


.listAutoComletePanelBom.listAutoComletePanelBom {
  width: calc(100% - 2px);
  max-height: 672px;
  padding: 6px 3px 6px 8px;
  // margin-top: 4px;
  box-shadow: inset 4px 4px 10.1px 0 #000;
  border-radius: 0px 0px 13px 13px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  span {
    padding: 8px;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 8px;
    font-family: Inter;
    font-size: 14px;
    font-weight: 300;
    line-height: 1.4;
    letter-spacing: 0.98px;
    text-align: left;
    color: #eaecf0;
    text-transform: uppercase;

    &:hover {
      border-radius: 10px;
      background: url(../../../assets/images/Dropdown_list_Hover.svg) no-repeat;
      background-size: cover;
      color: #0f0f14;
    }

    &[aria-selected="true"] {
      border-radius: 10px;
      background: url(../../../assets/images/Dropdown_list_Hover.svg) no-repeat;
      color: #0f0f14;
      background-size: cover;

      &.Mui-focused {
        background: url(../../../assets/images/Dropdown_list_Hover.svg) no-repeat;
        background-color: transparent;
        background-size: cover;
      }
    }
  }
}

.openAutoCompDropDown {
  background: url(../../../assets/images/Mian-Dropdown-BG.svg) #1b1c21 no-repeat;
  flex-direction: column;
  background-position: right;
  border-radius: 13px 13px 0px 0px;
  background-size: cover;
  width: 100%;
  box-shadow: inset 4px 4px 10.1px 0 #000;
  position: relative;
  z-index: 11;
}

.searchString {
  font-family: Inter;
  font-size: 16px;
  font-weight: normal;
  line-height: 1.3;
  letter-spacing: -0.18px;
  text-align: left;
  color: #fff;
  padding-left: 16px;
  padding-bottom: 9px;
}

.priceQty {
  width: 95px;
  font-family: Inter;
  font-size: 16px;
  font-weight: normal;
  line-height: 1.3;
  letter-spacing: -0.18px;
  text-align: center;
  color: #fff;
  padding-bottom: 9px;
  min-height: 21px;
  display: inline-block;
}

.noDataFoundBOM {
  padding-left: 16px;
  font-family: Inter;
  font-size: 16px;
  font-weight: 200;
  font-stretch: normal;
  font-style: italic;
  line-height: 1.3;
  letter-spacing: -0.18px;
  text-align: left;
  color: #fff;
  padding-bottom: 9px;
}

.marginBottom {
  margin-bottom: 20px;
}

.selectUomBOM {
  .uomDrodownBOM {
    font-size: 18px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
    height: 45px;
    padding-left: 4px;
    border: 0;
    text-transform: uppercase;

    &:focus-visible {
      outline: none;
    }

    div:nth-child(1) {
      padding: 5px 32px 0px 0px;
    }

    .selectDropdown {
      padding: 0px;
    }

    .MuiSelect-select {
      padding: 20px 6px 6px 6px;
    }

    svg {
      transform: unset;
      color: rgba(255, 255, 255, 0.6);
      right: 10px;
    }

    fieldset {
      border: 0;
    }
  }
}

.lineStatusContainer {
  height: 214px;
}

.companyNameInput {
  width: 100%;
  height: 32px;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.04);
  padding-left: 12px;
  resize: none;
  border: none;
  color: #459fff;
  caret-color: #459fff;
  font-family: Inter;
  font-size: 15px;
  letter-spacing: 0.6px;
  text-align: left;
  outline: none;
  caret-color: #1fbbff;
  color: var(--W--01);
  transition: all 0.1s ease;

  .errorInput {
    border-color: #ff3b3b !important;

    &:focus {
      border-color: #ff3b3b !important;
    }
  }


    &.errorInputHeaderDetails {
      box-shadow: inset 2.2px 2.2px 2.2px 0 #000;
      background-image: linear-gradient(122deg, #720d16 -73%, #ff4859 67%), linear-gradient(356deg, #8c8b99 203%, #2f2e33 38%);

      input{
        &::placeholder{
          color: #fff !important;
        }
      }
    
      &:focus {
        content: unset;
        box-shadow: none;
        background-image: linear-gradient(122deg, #720d16 -73%, #ff4859 67%), linear-gradient(356deg, #8c8b99 203%, #2f2e33 38%);
      }
    }
  input {
    height: 100%;
    width: fit-content;
    background-color: transparent;
    border: 0px;
    font-family: Inter;
    font-size: 15px;
    letter-spacing: 0.6px;
    text-align: left;
    outline: none;
    caret-color: #1fbbff;
    color: #1fbbff;

    &::placeholder {
      font-family: Syncopate;
      font-size: 14px;
      letter-spacing: 0.56px;
      text-align: left;
      color: #616575;
    }

    &:focus {
      outline: none;
      color: #459fff;
    }
  }

  &:focus-within {
    outline: none;
  }
}

.autoSuggestionText {
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: 0.6px;
  text-align: left;
  color: rgba(69, 159, 255, 0.33);
  position: relative;
  top: 1px
}

.zipErrorInput.zipErrorInput {
  box-shadow: inset 2.2px 2.2px 2.2px 0 #000;
  border: 0px;
  border-image-source: linear-gradient(358deg, var(--bdr-img-clr) 253%, #2f2e33 30%);
  background-image: linear-gradient(138deg, var(--error-bg-dark) -109%, var(--error-bg-light) 87%), linear-gradient(358deg, var(--bdr-img-clr) 253%, #2f2e33 30%);
  background-origin: border-box;
  background-clip: border-box, border-box;
  z-index: 2;

  input {
    color: #fff;

    &:focus {
      color: #fff;
      box-shadow: none;
    }

    &::placeholder {
      color: white;
    }
  }
}

// MUI TextField styling to match CustomTextField



.autocompleteContainer {
  position: relative;
  width: 100%;

  :global(.MuiAutocomplete-root) {
    width: 100%;

    :global(.MuiAutocomplete-inputRoot) {
      -webkit-box-flex-wrap: nowrap;
      -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
    }

    .MuiAutocomplete-inputRoot.MuiAutocomplete-inputRoot {
      -webkit-box-flex-wrap: nowrap;
      -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
    }

    .MuiAutocomplete-noOptions.MuiAutocomplete-noOptions {
      display: none;

    }
  }
}

.muiAutocompleteTextField.muiAutocompleteTextField {
  width: 100%;
  height: 32px;

  input {
    padding: 0px !important;
  }


  :global(.MuiInputBase-root) {
    height: 32px;
    width: 100%;
    border: 1px solid transparent;
    border-radius: 8px;
    font-family: Inter;
    font-size: 15px;
    letter-spacing: 0.6px;
    color: #1fbbff;
    padding: 0 12px;
    transition: all 0.1s ease;

    &:hover {
      border-color: transparent;
    }

    &.Mui-focused {
      border-color: transparent;
      color: #1fbbff;
      box-shadow: none;
    }

    &.Mui-error {
      box-shadow: inset 2.2px 2.2px 2.2px 0 #000;
      border: 0px;
      background-image: linear-gradient(138deg, var(--error-bg-dark) -109%, var(--error-bg-light) 87%), linear-gradient(358deg, var(--bdr-img-clr) 253%, #2f2e33 30%);
      background-origin: border-box;
      background-clip: border-box, border-box;
      color: #fff;

      &.Mui-focused {
        color: #fff;
        box-shadow: none;
      }
    }
  }

  :global(.MuiInputBase-input) {
    height: 100%;
    padding: 0;
    color: inherit;
    font-family: inherit;
    font-size: inherit;
    letter-spacing: inherit;
    border: none;
    outline: none;
    background-color: transparent;
    caret-color: #1fbbff;

    &::placeholder {
      font-family: Syncopate;
      font-size: 14px;
      letter-spacing: 0.56px;
      color: #616575;
      opacity: 1;
    }

    &:focus {
      outline: none;
      color: #1fbbff;
    }

    &.Mui-error {
      color: #fff;

      &::placeholder {
        color: white;
      }
    }
  }

  :global(.MuiInputAdornment-root) {
    color: #616575;
  }

  :global(.MuiFormHelperText-root) {
    display: none;
  }

  :global(.MuiInputLabel-root) {
    display: none;
  }

  :global(.MuiOutlinedInput-notchedOutline) {
    border: none;
  }
}


// .stateDropdownContainer {
//   input {
//     text-align: center;
//   }
// }

.autocompleteDropdown.autocompleteDropdown {
  max-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  gap: 8px;
   padding:0px  8px;
  border-radius: 10px;
  -webkit-backdrop-filter: blur(22.7px);
  backdrop-filter: blur(22.7px);
  background-color: rgba(113, 115, 127, 0.7);
  margin-top: 4px;

  ul {
    &::-webkit-scrollbar {
      width: 6px;
    }
     

    li {
      margin-right: 3px;
       padding: 8px 12px;
      border-radius: 6.8px;
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: normal;
      text-align: left;
      color: rgba(255, 255, 255, 0.7);
      background-color: transparent;

      &:hover {
          background-color: #c3c4ca;
          font-weight: bold;
          color: #0f0f14;
      }

      &:global(.Mui-focused) {
          background-color: #c3c4ca;
          font-weight: bold;
          color: #0f0f14;
      }

      &[aria-selected='true'] {
        background-color: transparent !important;
        box-shadow: unset;
        color: #fff;

        &:hover {
          background-color: #c3c4ca !important;
          font-weight: bold;
          color: #0f0f14;
        }

      
      }
    }
  }

  &.autocompleteDBA {
    ul {
      li {
        font-size: 16px;
        padding: 8px 16px;
      }
    }
  }
}

.confirmHeaderDetailsPopup {
  padding: 20px 24px;

  .dialogContent {
    width: 495px;
    height: 220px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 12px;
    background-image: linear-gradient(to bottom, #191a20, #191a20), radial-gradient(circle at 31% -2%, #fff, rgba(255, 255, 255, 0) 21%);
    background-origin: border-box;
    background-clip: content-box, border-box;
    background-color: transparent;

    .confirmHeaderDetailsContainer {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      row-gap: 40px;

      span {
        font-family: Syncopate;
        font-size: 16px;
        font-weight: bold;
        font-style: normal;
        line-height: 1.2;
        letter-spacing: 0.56px;
        text-align: center;
        color: #fff;
      }

      button {
        width: 160px;
        height: 40px;
        border-radius: 4px;
        border: solid 1px #4cff06;
        font-family: Syncopate;
        font-size: 16px;
        font-weight: normal;
        line-height: normal;
        letter-spacing: 0.52px;
        text-align: center;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background-color: #32ff6c;
          color: #0f0f14;
          border: solid 1px #32ff6c;
        }

        &:focus {
          background-color: #32ff6c;
          color: #0f0f14;
          border: solid 1px #32ff6c;
        }
      }
    }

  }
}

.uploadBOMLineTableMinHeight {
  min-height: 844px;
}

.minLineItemHeight {
  min-height: 214px;
}

.customNumberToggle {
  background-image: url(../../../assets/images/Product-Id-BG.svg);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 5px;
  border: solid 0.5px rgba(255, 255, 255, 0.16);
  width: 30px;
  height: 30px;
  font-family: Syncopate;
  font-size: 16px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: 1.12px;
  text-align: left;
  color: #9b9eac;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 3px;
  cursor: pointer;
  transition: all 0.2s ease;

  &.active {
    background-image: url(../../../assets/images/Usa-Only-Without-Text.svg);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    color: #0f0f14;
    border: none;
    border-radius: 2px;
  }

  &.domesticSkipDisabled {
    opacity: 0.5;
  }

  &.disabled {
    cursor: not-allowed;
  }

  &:focus-visible {
    outline: 1px solid #459fff;
    box-shadow: 0 0 3px rgba(69, 159, 255, 0.5);
  }
}

.poQtyEmpty {
  width: 95px;
  height: 45px;
  background-color: rgba(255, 255, 255, 0.04);
  border-radius: 12px;
}

.createPoHeaderInfoGrid {
  display: flex;
  column-gap: 20px;

  .leftGridHeader {
    flex: 0 75%;
    display: flex;
    flex-direction: column;
    row-gap: 20px;

    .formInputHeaderTop {
      display: flex;

      .col1 {
        flex: 1;

        input {
          width: 100%;
        }

        .deliverByContainer {
          width: 100%;
          position: relative;
        }

      }

    }


    .deliverToContainer {
      display: flex;
      flex: 1;
      height: 100%;
      min-height: 120px;
      border-radius: 12px;
      background-color: rgba(255, 255, 255, 0.04);
      padding: 8px;
      cursor: pointer;

      &:focus-visible {
        outline: none;
        border: 1px solid #459fff;
      }

      &.boxShadow {
        box-shadow: inset 5px 5px 7.9px -2px #000;
        // border: solid 1px transparent;
        background: linear-gradient(#1f2127, #1f2127) padding-box, linear-gradient(to bottom right, rgba(0, 0, 0, 0) 54%, rgba(255, 255, 255, 0.2901960784) 85%) border-box;
        background-color: unset;
      }

      .deliverToLabel {
        width: 100%;
        display: flex;
        flex-direction: column;
        row-gap: 4px;
        flex: 1;

        .addressInputs {
          width: 100%;
          height: 32px;
          border-radius: 8px;
          background-color: rgba(255, 255, 255, 0.04);
          // padding-left: 12px;
          resize: none;
          border: none;
          color: #459fff;
          caret-color: #459fff;

          &.hideInputBackground {
            background-color: transparent;
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            line-height: normal;
            letter-spacing: 1.4px;
            text-align: left;
            color: #fff;
            width: 100%;
            height: 32px;
            display: flex;
            align-items: center;
            padding-left: 12px;
          }

          &:focus {
            box-shadow: none;
          }

          &:focus-within {
            outline: none;
            box-shadow: none;
          }

          &::placeholder {
            font-family: Syncopate;
            font-size: 14px;
            letter-spacing: 0.56px;
            text-align: left;
            color: #616575;
          }

          .stateInput {
            width: 120px;
            background-color: rgba(0, 0, 0, 0.35);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 4px;

            .CustomMenu {
              height: 40px;
              color: rgba(255, 255, 255, 0.9);

              &::placeholder {
                color: rgba(255, 255, 255, 0.4);
                text-transform: uppercase;
              }
            }

            &:focus-within {
              border-color: #70ff00;
              background-color: rgba(0, 0, 0, 0.45);
            }
          }

          // Zip code input
          input[placeholder="ZIP CODE"] {
            width: 120px;
          }

          &.errorInputHeaderDetails {
            box-shadow: inset 2.2px 2.2px 2.2px 0 #000;
            background-image: linear-gradient(122deg, #720d16 -73%, #ff4859 67%), linear-gradient(356deg, #8c8b99 203%, #2f2e33 38%);
            &::placeholder{
              color: #fff !important;
            }
            input{
              &::placeholder{
                color: #fff !important;
              }
            }
          
            &:focus {
              content: unset;
              box-shadow: none;
              background-image: linear-gradient(122deg, #720d16 -73%, #ff4859 67%), linear-gradient(356deg, #8c8b99 203%, #2f2e33 38%);
            }
          }
        }

        .lastAddressFiled {
          display: flex;
          gap: 4px;

          .addressInputsCol1 {
            display: flex;
            flex: 1;
            position: relative;
            z-index: 1;

            input {
              width: 100%;
            }

            &.hideInputBackground {
              background-color: transparent;
              padding-left: 12px;
              color: #fff;
              font-family: Inter;
              font-size: 14px;
              font-weight: normal;
              line-height: normal;
              letter-spacing: 1.4px;
              text-align: left;
              color: #fff;
              height: 32px;
              display: flex;
              align-items: center;
            }
          }

          .addressInputsCol2 {
            position: relative;

            .shape1 {
              width: 13px;
              height: 14px;
              background-color: #3e3f47;
              position: absolute;
              transform: rotate(-390deg);
              top: -6px;
              left: -6px;

              &::before {
                content: "";
                position: absolute;
                width: 100%;
                height: 100%;
                background-color: #1f2127;
                border-radius: 8px;
                left: -78%;
                top: 18%;
              }
            }

            .shape2 {
              width: 13px;
              height: 14px;
              background-color: #3e3f47;
              position: absolute;
              transform: rotate(-113deg);
              top: -6px;
              right: -8px;

              &::before {
                content: "";
                position: absolute;
                width: 100%;
                height: 100%;
                background-color: #1f2127;
                border-radius: 8px;
                left: -57%;
                top: 20%;
              }
            }

            &.selectShade {
              background-color: #3e3f47;
              height: 36px;
              top: -4px;
              position: relative;
              border-radius: 0px 0px 8px 8px;
            }

            &.hideInputBackground {
              background-color: transparent;
              padding-left: 12px;
              color: #fff;
              font-family: Inter;
              font-size: 14px;
              font-weight: normal;
              line-height: normal;
              letter-spacing: 1.4px;
              text-align: left;
              color: #fff;
              height: 32px;
              display: flex;
              align-items: center;
            }

            .stateWrapper {
              height: 34px;
              border-radius: 8px;
              background-color: #3e3f47;
              display: flex;
              align-items: center;
              padding: 4px 0px 0px 5px;

              svg {
                position: absolute;
                right: 2px;
                top: 8px;
              }

              input {
                width: 63px;
                height: 24px;
                padding: 5px 2.4px 5px 4.5px;
                border-radius: 6px;
                background-color: #111217;
                font-family: Syncopate;
                font-size: 14px;
                font-weight: normal;
                line-height: 1;
                letter-spacing: 0.56px;
                text-align: left;
                color: #459fff;

                &::placeholder {
                  color: rgba(97, 101, 117, 0.5);
                }
              }
            }
          }

          .addressInputsCol2,
          .addressInputsCol3 {
            display: flex;
            flex: 0 90px;

            input {
              width: 100%;
              padding: 12px;
            }

            &.hideInputBackground {
              background-color: transparent;
              padding-left: 12px;
              color: #fff;
              font-family: Inter;
              font-size: 14px;
              font-weight: normal;
              line-height: normal;
              letter-spacing: 1.4px;
              text-align: left;
              color: #fff;
              height: 32px;
              display: flex;
              align-items: center;
            }
          }

          .addressInputsCol3 {
            position: relative;
            z-index: 1;
            flex: 0 84px;

            .errorInput {
              color: white;
              box-shadow: none;
            }
          }
        }
      }
      .deliverToPadding{
        padding: 8px;
      }

    }
  }

    .poGridHeader {
      flex: 0 100%;
      display: flex;
      flex-direction: column;
      row-gap: 16px;

      .formInputHeaderTop {
        display: flex;

        .col1 {
          flex: 1;

          input {
            width: 100%;
          }

          .deliverByContainer {
            width: 100%;
            position: relative;
          }

        }

      }
    }

    .rightGridHeader {
      flex: 0 25%;
      display: flex;
      flex-direction: column;
      row-gap: 20px;
  
      .updatePricingContainer {
          button {
            position: relative;
            font-family: Syncopate;
            font-size: 14px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: -0.56px;
            text-align: center;
            background: none;
            border: none;
            cursor: pointer;
            transition: all 0.4s ease;
            width: 100%;
            z-index: 1;
      
            &:disabled {
              cursor: not-allowed;
      
              .buttonText {
                width: 100%;
                height: 40px;
                flex-grow: 0;
                display: flex;
                flex-direction: row;
                justify-content: center;
                align-items: flex-start;
                padding: 12px 0 10px;
                opacity: 0.1;
                border-radius: 5000px;
                background-color: #222329;
                color: #fff;
                font-family: Syncopate;
                font-size: 14px;
                font-weight: bold;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.3;
                letter-spacing: -0.56px;
                text-align: center;
              }
            }
          }
      
          // Enabled .buttonText
          button:not(:disabled) .buttonText {
            width: 100%;
            height: 40px;
            flex-grow: 0;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: flex-start;
            padding: 12px 0 10px;
            border-radius: 5000px;
            background-color: #ffb800;
            overflow: visible;
            position: relative;
      
            &::before,
            &::after {
              content: "";
              position: absolute;
              inset: -6px;
              border: 8px solid rgba(255, 184, 0, 0.3);
              border-radius: inherit;
              animation: pulseOut 2s ease-out infinite;
              opacity: 0;
            }
      
            &::after {
              animation-delay: 1.2s;
            }
          }
      
          @keyframes pulseOut {
            0% {
              transform: scale(1);
              opacity: 1;
            }
      
            100% {
              transform: scale(1.1);
              opacity: 0;
            }
          }
        }
  
      .uploadBillContainer {
        width: 100%;
        height: 100%;
        // min-height: 180px;
        flex-grow: 0;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 8px;
        padding: 0;
        border-radius: 12px;
        background-color: rgba(255, 255, 255, 0.04);
        cursor: pointer;
        transition: all 0.2s ease;
  
        .uploadIcon2 {
          display: none;
        }
  
        &.disabled {
          cursor: not-allowed;
          // opacity: 0.5;
  
          &:hover {
            background: rgba(255, 255, 255, 0.04);
  
            .uploadLabel {
              font-weight: normal;
              color: #616575;
            }
  
            .uploadIcon2 {
              display: none;
            }
  
            .uploadIcon1 {
              display: block;
            }
          }
        }
  
        &:hover {
          background: #fff;
  
          .uploadLabel {
            color: #1b1b21;
            font-weight: bold;
          }
  
          .uploadIcon2 {
            display: block;
          }
  
          .uploadIcon1 {
            display: none;
          }
        }
  
        &:focus-visible {
          outline: none;
          background: #fff;
  
          .uploadLabel {
            color: #1b1b21;
            font-weight: bold;
          }
  
          .uploadIcon2 {
            display: block;
          }
  
          .uploadIcon1 {
            display: none;
          }
        }
  
        .uploadIcon {
          display: flex;
          justify-content: center;
          align-items: center;
        }
  
        .uploadLabel {
          font-family: Syncopate;
          font-size: 14px;
          font-weight: normal;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.2;
          letter-spacing: 0.56px;
          text-align: center;
          color: #616575;
        }
      }
    }



}


.isEditMode{
  cursor: not-allowed;
  .formInputGroup1{
    pointer-events: none;
  }
}

.customeAddressPopup {
  .closeIcon {
    position: absolute;
    right: 20px;
    z-index: 1;
    top: 10px;
  }

  .dialogContent {
    border-radius: 20px;
    width: 100%;
    padding: 50px 40px;
    background: url(../../../assets/images/Create-Account/dialogBG.svg) #0f0f14 no-repeat;
    background-size: cover;
    background-position: center;
  }

}

.confirmHeaderDetailsPopup {
  .dialogContentHeader {
    width: 100%;
    max-width: 600px;
    padding: 24px 24px 24px 23px;
    border-radius: 16px;
    box-shadow: 0 0 67.4px 4px #000;
    background-color: #222329;

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }

    .title {
      font-family: Syncopate;
      font-size: 18px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: -0.72px;
      text-align: left;
      color: #fff;
    }

    .cancelBtn {
      display: flex;
      align-items: center;
      column-gap: 4px;

      &:focus{
        .cancelText{
          color: #fff;
        }
      }

      .cancelText {
        font-family: Inter;
        font-size: 12px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: normal;
        text-align: left;
        color: #71737f;
      }
      

    }

    .formInputGroup {
      padding: 0px;
      background-color: transparent;

      .col1 {
        display: flex;
        flex-direction: column;
        gap: 10px;
      }

      .pOInputLabel {
        font-family: Syncopate;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: 0.56px;
        text-align: left;
        color: #9b9eac;
      }
    }

    .deliverToContainerMain {
      display: flex;
      flex-direction: column;
      gap: 10px;
      padding: 8px;
    }

    .footerPopup {
      display: flex;
      justify-content: flex-end;
      margin-top: 33px;

      .saveButton {
        width: 120px;
        height: 36px;
        padding: 8px 6px 8px 6px;
        border-radius: 10px;
        cursor: pointer;
        transition: all 0.2s ease;
        font-family: Syncopate;
        font-size: 14px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: normal;
        text-align: center;
        color: #fff;
         background-image: linear-gradient(143deg, #1c40e7 -57%, #16b9ff 138%);
        &[disabled] {
         background-color: #2b2c32;
        cursor: not-allowed;
        color: #9b9eac;
        background-image: none;
        opacity: unset;
        }

        &:focus{ 
          outline: 1px solid #fff;
        }
      }
    }

  }
}


.orderManagementNoDataContainer{
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-image: linear-gradient(351deg, #2b2d33 237%, #0f0f14 -120%);
  opacity: 0.5;
  .noOrderDetailsContainer{
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      gap: 12px;
      font-family: Syncopate;
      font-size: 16px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: 1.12px;
      text-align: center;
      color: #fff;
  }
}

.orderManagementContainer {
  .buttonContainer {
    .buttonContainerOrder {
      display: flex;
      column-gap: 8px;
      padding: 16px;

      button {
        height: 36px;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        gap: 8px;
        padding: 0px 12px 0px 12px;
        border-radius: 500px;
        background-color: rgba(255, 255, 255, 0.04);
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: normal;
        text-align: left;
        color: #9b9eac;
        &:hover{
          color: #fff;
          svg{
            path{
              fill: #fff;
            }
          }
        }

        &.cancelOrderLineBtn{
           &:hover{
              color: #ff4848;
              svg{
                path{
                  fill: #ff4848;
                }
              }
          }
        }

        &.cancelSelectedLines{
          background-color: #222329;
          color: #fff;
          svg{
            path{
              fill: #fff;
            }
          }
        }

         &[disabled] {
          background-color: #222329;
          color: rgba(255, 255, 255, 0.3);
          opacity: unset;
          cursor: not-allowed;
          &:hover{
           background-color: #222329;
           color: rgba(255, 255, 255, 0.3);
             svg{
            path{
              fill: #9b9eac;
            }
          }
          }
        }
      }

      .requestChangesButton {
        background-color: #2abcfb;
        color: #0f0f14;

        &:hover{
          color: #0f0f14;
        }
   
      }
    }
  }
  .orderManagementLeftGridHeader{ 
     padding: 16px 54px 16px 12px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.04);

    .formInputHeaderTop{
      flex-direction: column;
    }
    .orderManagementPoName{
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: 0.56px;
        text-align: left;
        color: rgba(255, 255, 255, 0.5);
        .pOInputValue{
          color: #fff;
        }

    }
  }
}


.rightGridDisputeItemCounter{
  display: flex;
  flex-direction: column;
  row-gap: 6px;
  align-items: flex-end;
}


// Export dropdown styles
.exportContainer.exportContainer {
  position: relative;
  border-radius: 500px;

  .exportButton.exportButton {
    height: 34px;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0px 0px 0px 12px;
    background-color: rgba(255, 255, 255, 0.04);
    color: #9b9eac;
    border: none;
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    cursor: pointer;
    transition: all 0.2s ease;
    &[disabled]{
        background-color: rgba(255, 255, 255, 0.04);
       color: rgba(255, 255, 255, 0.3);
        cursor: not-allowed;
          svg{
            path{
              fill: rgba(255, 255, 255, 0.3);;
            }
          }
        &:hover {
        background-color: rgba(255, 255, 255, 0.04);
        color: rgba(255, 255, 255, 0.3);
         svg{
            path{
              fill: rgba(255, 255, 255, 0.3);;
            }
          }
       }
    }

    .exportArrow {
      height: 100%;
      min-width: 30px;
      transition: transform 0.2s ease;
      border-radius: 0% 50% 50% 0%;
      display: flex;
      align-items: center;
      justify-content: center;
      border-left: solid 1px rgba(255, 255, 255, 0.1);

      svg {
        width: 20px;
        height: 20px;
      }
    }
  }

    .exportDropdown.exportDropdown {
      position: absolute;
      top: 100%;
      left: 0;
      margin-top: 2px;
      border-radius: 8px;
      -webkit-backdrop-filter: blur(20px);
      backdrop-filter: blur(20px);
      background-color: rgba(113, 115, 127, 0.7);
      z-index: 1000;
      overflow: hidden;
      padding: 4px;
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 4px;
      width: 164px;
  
      .exportOption {
        width: 100%;
        padding: 6px 10px;
        cursor: pointer;
        transition: background-color 0.2s ease;
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: normal;
        text-align: left;
        color: rgba(255, 255, 255, 0.7);
        text-align: left;
        border-radius: 6px;
        background-color: transparent;
        height: auto;
  
        &:not(:disabled):hover {
          background-color: rgba(255, 255, 255, 0.4);
          color: #191a20;
          font-weight: bold;
        }
  
        &[disabled] {
          cursor: not-allowed;
          background-color: transparent;
          color: rgba(255, 255, 255, 0.3);
        }
  
  
      }

      &.cancelDropdown{
        .exportOption{
          font-size: 12px;
        }
      }
    }
}

.selectedLine {
  border-radius: 10px;
  background-color: rgba(255, 255, 255, 0.08);
  cursor: pointer;
}

.exitEditMode{
  padding: 8px 12px 8px 16px;
  border-radius: 500px;
  background-color: rgba(255, 255, 255, 0.12);
   font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: left;
  color: #fff;
  margin-left: auto;
}


.stateWrapper.stateWrapper  {
     height: 34px;
     border-radius: 8px;
     background-color: #3e3f47;
     display: flex;
     align-items: center;
     padding: 4px 0px 0px 5px;

     svg {
         position: absolute;
         right: 2px;
         top: 8px;
     }

     input {
         width: 85%;
         height: 24px;
         padding: 5px 2.4px 5px 4.5px;
         border-radius: 6px;
         background-color: #111217;
         font-family: Syncopate;
         font-size: 14px;
         font-weight: normal;
         line-height: 1;
         letter-spacing: 0.56px;
         text-align: left;
         color: #459fff;
         border: none;

         &:focus {
             outline: none;
         }

         &::placeholder {
             color: rgba(97, 101, 117, 0.5);
         }
     }
 }

 
 .stateMain {
     width: 497px;
     position: absolute;
     top: -67px;
     left: -327px;
     z-index: 100;
 }

 .stateWrapper1 {
     .stateWrapper.stateWrapper {
         height: 25px;
         border-radius: 4px;
         padding-top: 1px;
         input {
            height: 18px;
             border: none;
             font-size: 12px;
         }
         svg{
            top: 5px;
         }
     }

     .stateMain{
         top: -67px;
         left: -105px;
     }
 }

 .duplicatePoDialog.duplicatePoDialog{
  .dialogContent{
    width: 100%;
    max-width: 504px;
      padding: 80px 64px 80px 64px;
     border-radius: 50px;
     background-color: #0f0f14;
     box-shadow: none;
     .duplicatePoDialogContainer{
      .header{
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 40px;
        .title{
          font-family: Syncopate;
          font-size: 24px;
          font-weight: bold;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.3;
          letter-spacing: -0.96px;
          text-align: center;
          color: #fff;
        }
        .closeIcon{
          cursor: pointer;
          position: absolute;
          top: 24px;
          right: 24px;

          svg{
           width: 12px;
           height: 12px;
          }
        }
      }

      .formInputGroupDuplicate{
        width: 100%;
        margin: 0 auto;
        padding: 0px 63px;

        .inputfiled{
           width: 100%;
           height: 40px;
           margin-bottom: 16px;
        }
      }

      .btnSectionPopup{
        margin-top: 40px;
        button{
          opacity: unset;
          transition: all 0.2s ease;
         &:first-child{
           width: 100%;
          height: 36px;
          flex-grow: 0;
          margin: 0 0 4px;
          padding: 13px 19px 11px 20px;
          border-radius: 1000px;
           background-color: #32ff6c;
          font-family: Syncopate;
          font-size: 12px;
          font-weight: bold;
          font-stretch: normal;
          font-style: normal;
          line-height: 1;
          letter-spacing: -0.48px;
          text-align: center;
          color: #000;
          
          &[disabled]{
            background-color: rgba(255, 255, 255, 0.04);
            color: #393e47;
          }

          &:not([disabled]):hover{
              background-color: #2b2c32;

              color: #32ff6c;

          }

        }

        &:last-child{
          width: 100%;
           font-family: Syncopate;
            font-size: 12px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: -0.48px;
            text-align: center;
            color: #71737f;
            background-color: transparent;
           padding: 11px 0px;
           &:hover{
            color: #fff;
           }
        }
      }
      }
   
     }
  }
}


.orderConfirmationDialog {
  .dialogContent {
    padding: 56px 40px 56px 40px;
    border-radius: 50px;
    background-color: #0f0f14;
    max-height: 100%;
    box-shadow: none;
    max-width: 600px !important;    
  }
}

.selectQtyUnitDispute {
  .uomDrodown {
    font-family: Inter;
    font-size: 14px;
    line-height: normal;
    letter-spacing: normal;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
    height: 45px;
    padding-left: 18px;
    border: 0;
    text-transform: uppercase;

    &:focus-visible {
      outline: none;
    }

    div:nth-child(1) {
      padding: 0px 32px 0px 0px;
    }

    .selectDropdown {
      padding: 0px;
    }

    .MuiSelect-select {
      padding: 20px 6px 6px 6px;
    }

    svg {
      transform: unset;
      color: rgba(255, 255, 255, 0.6);
      right: 10px;
    }

    fieldset {
      border: 0;
    }
  }
}

.cancelLineWhenUnclaimedDialog{
  .dialogContent{
      width: 100%;
      max-height: 100%;
      max-width: 1100px;
      padding: 30px 32px;
      border-radius: 50px;
      background-color: #0f0f14;
      box-shadow: none;
      .closeButton{
        position: absolute;
        top: 24px;
        right: 24px;
        cursor: pointer;
      }
      .titlePopup{
        font-family: Syncopate;
        font-size: 24px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: -0.96px;
        text-align: left;
        color: #fff;
      }
      .subTitlePopup{
        font-family: Inter;
        font-size: 16px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: 0.64px;
        text-align: left;
        color: #9b9eac;
        margin:8px 0px 24px 0px;
      }
      .cancelLineSummaryDataMain{
        display: flex;
         height: 46px;
         padding: 5px 10px 5px 10px;
        background-color: rgba(255, 255, 255, 0.15);
        border-radius: 0px 0px 10px 10px;
        .cancelLineSummary{
          display: flex;
          align-items: center;
          justify-content: center;
          column-gap:41px;
          flex:1;
          span{
              font-family: Inter;
              font-size: 18px;
              font-weight: normal;
              font-stretch: normal;
              font-style: normal;
              line-height: 1;
              letter-spacing: normal;
              text-align: right;
              color: #fff;
            &:first-child{
               font-family: Syncopate;
                font-size: 14px;
                font-weight: bold;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.3;
                letter-spacing: -0.56px;
                text-align: right;
                color: #fff;
            }
          }
        }
      }
      .cancelLineSummaryBtnMain{
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 24px;
        padding:0px 15px;
        border-radius: 4px;
        column-gap: 36px;
        .cancelLineBtn{
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 500px;
          height: 69px;
          padding: 12px 10.3px 11px 10px;
          border-radius: 50px;
          background-color: #2b2c32;
          font-family: Syncopate;
          font-size: 18px;
          font-weight: bold;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.3;
          letter-spacing: -0.72px;
          text-align: center;
          color: #fff;
          span{
            font-weight: normal;
          }
          &:nth-child(2){
            background-color: #32ff6c;
            color: #000;
          }
        }
      }
  }
}

.filterHeader{
   height: 52px;
  margin: 0 0 16px;
  padding: 8px 16px 8px 16px;
  background-color: #27272d;
  display: flex;
  align-items: center;
  border-radius: 4px;
  gap: 12px;

  .filterIcon {
    width: 20px;
    height: 20px;
    fill: #ffffff;
    opacity: 0.8;
    cursor: pointer;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 1;
    }
  }

  .exportContainer {
    position: relative;
    min-width: 200px;
    flex: 1;

    .disputeFilterSelect {
      background-color: transparent;
      color: #ffffff;
      border: none;
      width: 100%;

      &:before,
      &:after {
        display: none;
      }
    }
  }
}

// Custom styles for the dispute filter dropdown menu
:global(.disputeFilterMenu) {
  .MuiPaper-root {
    background-color: #2d2d33 !important;
    border: 1px solid #3a3a40 !important;
    border-radius: 4px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
    margin-top: 4px !important;

    .MuiList-root {
      padding: 4px 0 !important;
    }

    .MuiMenuItem-root {
      color: #ffffff !important;
      font-size: 14px !important;
      padding: 8px 16px !important;

      &:hover {
        background-color: #3a3a40 !important;
      }

      &.Mui-selected {
        background-color: #2d2d33 !important;

        &:hover {
          background-color: #3a3a40 !important;
        }
      }
    }
  }
}

.filterHeaderDropdownMain{
   width: 450px;
   height: 36px;
   border-radius: 500px;
   background-color: rgba(0, 0, 0, 0.7);
   display: flex;
    align-items: center;
   .filterIconContainer{
    width: 40px;
    height: 100%;
    padding: 7px 6px 5px 10px;
    border-right: solid 1px rgba(255, 255, 255, 0.1);
    background-color: rgba(255, 255, 255, 0.15);
    border-radius: 500px 0px 0px 500px;
    display: flex;
    align-items: center;
    justify-content: center;
   }
   .disputeFilterSelect{
    width: 100%;
    height: 100%;
   }
   .filterSelectContainer{
    width: 100%;
    height: 100%;
     flex: 1;
     display: flex;
     align-items: center;
   }
}
.disputeFilterMenu.disputeFilterMenu {
  padding: 4px;
  border-radius: 8px;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  background-color: rgba(113, 115, 127, 0.7);

  ul {
    li {
      font-family: Inter;
      font-size: 12px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: normal;
      text-align: left;
      color: #c3c4ca;

      &:hover {
        border-radius: 6px;
        background-color: rgba(255, 255, 255, 0.4);
        color: #0f0f14;

      }
      &[aria-selected="true"] {
        background-color: transparent;
         color: #c3c4ca;
           &:hover {
        border-radius: 6px;
        background-color: rgba(255, 255, 255, 0.4);
        color: #0f0f14;

      }
      }
    }
  }
}

.dot{
  width: 3px;
  height: 8px;
  flex-grow: 0;
  border-radius: 1.9px;
  margin-right: 8px;
}

.headerToolbar.headerToolbar{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px 30px 16px 16px;
  .buttonContainerOrder{
    padding: 0px;
  }
}

 .disputeCalendarPopover.disputeCalendarPopover {
      box-shadow: none;
      background-color: transparent;
  }

  .canceledOrderDescription{
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.3;
    letter-spacing: 0.98px;
    text-align: right;
    color: #ff4848;
    padding-right: 16px;
  }

  .lineCanceledContainer{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 10px;
    .lineCanceledText{
       font-family: Inter;
      font-size: 14px;
      font-weight: 300;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: 0.98px;
      text-align: left;
      color: #fff;
    }
    .lineCanceledShowDetails{
       font-family: Inter;
      font-size: 14px;
      font-weight: 300;
      font-stretch: normal;
      font-style: italic;
      line-height: 1.4;
      letter-spacing: 0.98px;
      text-align: left;
      color: #9b9eac;
      display: flex;
      align-items: center;
      column-gap: 4px;
      cursor: pointer;
      .arrowIcon{
      display: flex;
      transition: transform 0.2s ease;
      &.arrowIconRotate{
        transform: rotate(180deg);
      }
    }
    }

    
  }

  .hiddenLineCanceledDetails.hiddenLineCanceledDetails{
      display: none !important;
  }

  .showLineCanceledDetails{
    display: inline-flex;
  }

  .showLineCanceledDescDetails{
    display: inline-flex;
    flex-direction: column;
  }
  .buyerCancellationLine{
    font-family: Inter;
    font-size: 12px;
    font-weight: 300;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: 0.84px;
    text-align: left;
    color: #dbdcde;
  }

  .lineAcceptedDeliveryDateContainer.lineAcceptedDeliveryDateContainer {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
     gap: 4px;
     padding: 8px 12px 8px 16px;
     border-radius: 500px;
     background-color: rgba(255, 255, 255, 0.04);
    position: absolute;
    margin-top: 16px;
    margin-left: 24px;
    min-width: 300px;
    &.showLineCanceledDetails.showLineCanceledDetails{
      padding-top: 8px;
    }
    .deliveryDateLbl{
        font-family: Inter;
        font-size: 12px;
        font-weight: 300;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: 0.84px;
        text-align: left;
        color: #dbdcde;
    }
    .deliveryDate{
       font-family: Inter;
        font-size: 12px;
        font-weight: 300;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: 0.84px;
        text-align: left;
        color: #fff;
    }
  }

  .lineAcceptedDeliveryDateContainerDispute{
    display: flex;
    flex-direction: row;
     align-items: center;
    column-gap: 4px;
    padding: 6px 12px;
      .deliveryDateLbl{
        font-family: Inter;
        font-size: 12px;
        font-weight: 300;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: 0.84px;
        text-align: left;
        color: #dbdcde;
    }
    .deliveryDate{
       font-family: Inter;
        font-size: 12px;
        font-weight: 300;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: 0.84px;
        text-align: left;
        color: #fff;
    }
  }

  .cancelBtnOpen{
    span{
      color: #fff;
    }
    .exportArrow{
    transition: all 0.1s;
    svg{
      transform: rotate(180deg);
      path{
        fill: #fff;
      }
    }
  }
  }
  

    .exportDropdownPanel.exportDropdownPanel {
      margin-top: 2px;
      border-radius: 8px;
      -webkit-backdrop-filter: blur(20px);
      backdrop-filter: blur(20px);
      background-color: rgba(113, 115, 127, 0.7);
      z-index: 1000;
      overflow: hidden;
      padding: 4px;
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 4px;
      width: 164px;
      box-shadow: none;
      margin-bottom: 2px;
  
      .exportOption {
        width: 100%;
        padding: 6px 10px;
        cursor: pointer;
        transition: background-color 0.2s ease;
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: normal;
        text-align: left;
        color: rgba(255, 255, 255, 0.7);
        text-align: left;
        border-radius: 6px;
        background-color: transparent;
        height: auto;
  
        &:not(:disabled):hover {
          background-color: rgba(255, 255, 255, 0.4);
          color: #191a20;
          font-weight: bold;
        }
  
        &[disabled] {
          cursor: not-allowed;
          background-color: transparent;
          color: rgba(255, 255, 255, 0.3);
        }
  
  
      }

      &.cancelDropdown{
        .exportOption{
          font-size: 12px;
        }
      }
    }

.undoIcon{
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
    svg {
      rect{
        fill: transparent;
      }
      path {
        fill: #fff
      }
    }
}

 .undoTooltip.undoTooltip {
    padding: 10px 12px;
    max-width: 194px;
    font-family: Inter;
    font-size: 12px;
    font-weight: 300;
    line-height: 1.4;
    letter-spacing: 0.7px;
    text-align: left;
    color: #fff;
    border-radius: 8px;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.15);

    .MuiTooltip-arrow {
      color: rgba(255, 255, 255, 0.15);
    }
  }

  .tooltipDisabled{
    display: contents;
  }

  .containerChk {
    display: inline-block;
    position: relative;
    cursor: pointer;
    padding-left: 12px;
    text-align: left;

    // &:focus-within{
    //   .checkmark{
    //     border: 1px solid #fff;
    //     border-radius: 6px;
    //   }
    // }

    input {
        position: absolute;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;
    }

    .checkmark {
        position: absolute;
        top: 0px;
        left: -7px;
        z-index: 1;
        width: 24px;
        height: 24px;
        background: url(../../../assets/images/Create-Account/uncheckmark.svg) no-repeat;
        background-size: contain;
      

    }

    .checkmark:after {
        content: "";
        position: absolute;
        display: none;
    }

    input:checked~.checkmark {
        background: url(../../../assets/images/blue-checkmark.svg) no-repeat;
        background-size: contain;
    }

    input:checked~.checkmark:after {
        display: block;
    }

    input:disabled~.checkmark {
        opacity: 0.5;
        cursor: not-allowed;
    }

    input:disabled~.checkmark {
        background: url(../../../assets/images/Create-Account/uncheckmark.svg) no-repeat;
        background-size: contain;
        opacity: 0.5;
    }

}