.tableContainer {
  width: 100%;
  background-color: #2a2b32;
  border-radius: 8px;
  overflow: hidden;
  font-family: Inter, sans-serif;
}

.userTable {
  width: 100%;
  border-collapse: collapse;
  background-color: #2a2b32;
  color: #ffffff;
}

.tableHeader {
  background-color: #1f2025;
  height: 60px;

  th {
    padding: 16px 20px;
    text-align: left;
    font-family: Syncopate;
    font-size: 12px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.3;
    letter-spacing: 0.84px;
    text-align: left;
    color: #fff;
    text-transform: uppercase;
    color: #ffffff;
    border-bottom: 1px solid #404149;

    &:first-child {
      padding-left: 24px;
    }

    &:last-child {
      padding-right: 24px;
    }
  }
}

.tableRow {
  background-color: #2a2b32;
  border-bottom: 1px solid #404149;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #323339;
  }

  &:last-child {
    border-bottom: none;
  }

  // Placeholder row styling - grey and less opacity
  &.placeholderRow {
    opacity: 0.6;
    background-color: #25262c;

    &:hover {
      background-color: #2a2b31;
    }
  }
}

.placeholderRow {
  background-color: rgba(42, 43, 50, 0.6);
  opacity: 0.7;

  &:hover {
    background-color: rgba(50, 51, 57, 0.7);
  }

  .tableCell {
    color: rgba(255, 255, 255, 0.6);
  }

}

.tableCell {
  padding: 16px 20px;
  font-size: 14px;
  font-weight: 400;
  color: #ffffff;
  vertical-align: middle;

  &:first-child {
    padding-left: 24px;
  }

  &:last-child {
    padding-right: 24px;
  }
}

.editableCell {
  cursor: pointer;
  display: inline-block;
  width: 100%;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #404149;
  }
}

.editInput {
  color: #ffffff;
  font-family: Inter;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.2;
  letter-spacing: normal;
  text-align: left;
  color: #fff;
  width: 100%;
  outline: none;
  transition: border-color 0.2s ease, background-color 0.2s ease, opacity 0.2s ease;
  background: transparent;
  border: none;

  &:focus {
    outline: none;
  }

  &::placeholder {
    opacity: 0.3;
    color: #fff;
  }


  // Disabled input styling
  &.disabled {
    cursor: not-allowed;
    opacity: 0.3;

    &:focus {
      outline: none;
    }
  }

  &.error {
    border-color: #dc2626;
    background-color: rgba(220, 38, 38, 0.1);
  }

  &.disabled {
    background-color: rgba(64, 65, 73, 0.4);
    border-color: rgba(90, 93, 107, 0.4);
    color: rgba(255, 255, 255, 0.4);
    cursor: not-allowed;

    &::placeholder {
      color: rgba(255, 255, 255, 0.2);
    }
  }
}

.dropdown {
  background-color: #404149;
  border: 1px solid #5a5d6b;
  border-radius: 6px;
  color: #ffffff;
  font-size: 13px;
  font-family: Inter, sans-serif;
  padding: 8px 32px 8px 12px;
  cursor: pointer;
  outline: none;
  min-width: 120px;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px;
  appearance: none;
  transition: background-color 0.2s ease, border-color 0.2s ease, opacity 0.2s ease;
  -webkit-appearance: none;
  -moz-appearance: none;

  &:hover {
    background-color: #4a4d5a;
    border-color: #6b6e7c;
  }

  &:focus {
    border-color: #6366f1;
    background-color: #4a4d5a;
  }

  option {
    background-color: #404149;
    color: #ffffff;
    padding: 8px 12px;
  }

  &.disabled {
    background-color: #2a2b32;
    border-color: #35363c;
    color: rgba(255, 255, 255, 0.4);
    cursor: not-allowed;
    opacity: 0.6;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");

    &:hover {
      background-color: #2a2b32;
      border-color: #35363c;
    }

    &:focus {
      background-color: #2a2b32;
      border-color: #35363c;
      border-color: rgba(90, 93, 107, 0.4);
    }
  }
}

.status {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

// New styles for React Hook Form
.formContainer {
  width: 100%;
}

.cellContainer {
  position: relative;
  width: 100%;
}

.licenseCellContainer {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.licenseDisabledHint {
  font-size: 11px;
  color: #f59e0b;
  font-weight: 500;
  text-align: center;
}

.errorMessage {
  position: absolute;
  top: 100%;
  left: 0;
  color: #dc2626;
  font-size: 11px;
  font-weight: 500;
  margin-top: 2px;
  z-index: 10;
}

.licenseStatus {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 16px 24px 0;
  gap: 8px;

  >span:first-child {
    color: #9ca3af;
    font-size: 14px;
    font-weight: 500;
  }
}

.licenseWarning {
  display: flex;
  justify-content: center;
  padding: 12px 16px;
  background-color: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.3);
  border-radius: 6px;
  color: #f59e0b;
  font-size: 14px;
  font-weight: 500;
  text-align: center;

  span {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.saveButtonContainer {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
  padding: 0 24px;
}

.saveButton {
  background-color: #6366f1;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  font-family: Inter, sans-serif;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;

  &:hover:not(.disabled) {
    background-color: #5855eb;
    transform: translateY(-1px);
  }

  &:active:not(.disabled) {
    transform: translateY(0);
  }

  &.disabled {
    background-color: #6b7280;
    color: #9ca3af;
    cursor: not-allowed;
    transform: none;
  }
}



.tableCard {
  border: 0;
  background: transparent;
  padding-bottom: 16px;
  overflow: auto;
  border-radius: 10px 10px 0px 0px;
  height:100%;

  &::-webkit-scrollbar {
    width: 8px;
    height: 6px;
  }
}

.disableUserTable{
  filter: blur(1px);
  pointer-events: none;
}

.table {
  width: 100%;
  border-collapse: collapse;

}

.tableHeader {
  background-color: rgba(255, 255, 255, 0.15);
  border-top-left-radius: 13px;
  border-top-right-radius: 13px;
}

.tableHeaderRow {
  border: none;

  th{
  position: sticky;
  top: 0px;
  background-color: #3b3c41 ;
  z-index: 100;
  }
}

.tableHeaderCell {
  height: 46px;
  font-family: 'Syncopate', Helvetica;
  font-weight: bold;
  color: white;
  font-size: 14px;
  letter-spacing: -0.56px;
  line-height: 18.2px;
  padding: 12px 16px;
  text-align: left;
  vertical-align: middle;
  padding: 6px 0px 4px 12px;
  text-transform: uppercase;
}

.tableBody {
  tr {
    td:nth-child(1) {
      width: 200px;
    }
  }
}

.tableRow {
  height: 61px;
  background-color: transparent;
  border: none;

  &:nth-child(even) {
    background-color: #222329;
  }

  &.spacer {
    margin-top: 10px;
  }
}

.tableCell {
  padding: 6px 0px 6px 12px;
  font-family: Inter;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.3;
  letter-spacing: normal;
  text-align: left;
  color: #fff;

  &:last-child {
    padding-right: 8px;
  }

  &.center {
    text-align: center;
  }

  .hoursCellContent {
    overflow: auto;
    max-height: 48px;
    padding-right: 10px;
    width: 125px;

    &::-webkit-scrollbar {
      width: 2px;
      height: 6px;
    }
  }
}

.addressCell {
  white-space: pre-line;
}

.hoursCell {
  div {
    margin-bottom: 2px;
  }
}

.contactCell {
  div {
    margin-bottom: 2px;
  }

  a {
    color: white;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.emailCell {
  a {
    color: white;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}


.selectDropdown.selectDropdown {
  width: 100%;

  :global(.MuiSelect-select) {
    width: 100%;
    height: 40px;
    flex-grow: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    gap: 20px;
    padding: 0 0 0 16px;
    background-color: transparent;
    border: 1px solid transparent;
    font-family: Inter;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.2;
    letter-spacing: normal;
    text-align: left;
    color: #fff;


  }

  :global(.Mui-disabled) {
    opacity: 0.3;
    cursor: not-allowed;
    -webkit-text-fill-color: unset;
  }

  &.selectState {
    :global(.MuiSelect-select) {
      height: 40px;
      font-size: 14px;
    }
  }

  :global(.MuiSelect-icon) {
    width: 20px;
    height: 20px;

    path {
      fill: #fff;
      stroke-opacity: unset;
    }

    top: unset;
    width: 19px;
    height: 19px;
    right:12px;
  }

  &:global(.Mui-focused) {
    :global(.MuiSelect-select) {
      background-color: transparent;
    }
  }

  :global(fieldset) {
    border-color: transparent !important;
  }
}

.Dropdownpaper.Dropdownpaper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  gap: 8px;
  padding: 4px;
  border-radius: 8px;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  background-color: #9c9da5;
  padding: 0px;
  margin-top: 1px;

  .muiMenuList {
    padding: 4px;

    li {
      padding: 6px 16px;
      border-radius: 6px;
      font-family: Inter;
      font-size: 14px;
      font-weight: 500;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: normal;
      text-align: center;
      color: #191a20;
      background-color: transparent;

      &:hover {
        background-color: #e0e0e0;
        color: #191a20;
        font-weight: bold;
      }
    }
  }


}

.editPaymentInfoPopup {
  .titleGrid {
    display: flex;
    justify-content: space-between;
    margin-bottom: 18px;

    h1 {
      font-family: Syncopate;
      font-size: 18px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.2;
      letter-spacing: -0.72px;
      text-align: left;
      color: #fff;
      text-transform: uppercase;
    }

    .closeIcon {
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: normal;
      text-align: left;
      color: rgba(255, 255, 255, 0.85);
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
    }

  }

   &.uploadUserListPopup {
    .titleGrid {
      margin-bottom: 46px;
    }
      .uploadUserBtnContainer{
        display: flex;
        flex-direction: column;
        align-items: center;
        row-gap: 24px;
        button{
          height: 36px;
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          gap: 8px;
          padding: 8px 14px 8px 14px;
          border-radius: 500px;
          background-color: rgba(255, 255, 255, 0.04);
          font-family: Inter;
          font-size: 18px;
          font-weight: normal;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.4;
          letter-spacing: normal;
          text-align: center;
          color: #71737f;

        }
      }
    }

  .licenseInfoContainer {
    font-family: Syncopate;
    font-size: 18px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.3;
    letter-spacing: -0.72px;
    text-align: right;
    color: #32ccff;
    text-transform: uppercase;
    &.editLicenseInfoContainer{
     font-size: 14px;
    }

    span {
      font-weight: normal;
    }
  }

}

.successDialogContainer{
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 0px 0px 0px;
    h1{
        font-family: Syncopate;
        font-size: 24px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: 5.04px;
        text-align: right;
        color: #fff;
        text-transform: uppercase;
        margin-bottom: 16px;
    }
    .successDialogContent{
        font-family: Inter;
        font-size: 16px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.5;
        letter-spacing: normal;
        text-align: center;
        color: #fff;
    }

}