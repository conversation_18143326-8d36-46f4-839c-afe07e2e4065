import React, { useEffect, useRef, useState } from 'react';
import styles from './ShipmentTab.module.scss';
import { yupResolver } from '@hookform/resolvers/yup';
import { useForm, Controller } from 'react-hook-form';
import InputWrapper from 'src/renderer/component/InputWrapper';
import CustomTextField from 'src/renderer/component/CustomTextField';
import CustomToggleCheckbox from 'src/renderer/component/CustomToggleCheckbox';
import clsx from 'clsx';
import { shipmentsSchema } from '../schemas';
import { Dialog, Fade, Tooltip, Autocomplete, TextField, Select, MenuItem } from '@mui/material';
import { CustomMenu } from '../../CustomMenu';
import axios from 'axios';
import { buyerSettingConst, formatPhoneNumber, formatPhoneNumberRemovingCountryCode, useBuyerSettingStore, useGlobalStore } from '@bryzos/giss-ui-library';
import { commomKeys, defaultResaleCertificateLine, prefixUrl, reactQueryKeys, RecevingHoursFrom, RecevingHoursTo } from 'src/renderer/common';
import useDialogStore from 'src/renderer/component/DialogPopup/DialogStore';
import { ReactComponent as DropdownIcon } from '../../../../assets/images/StateIconDropDpown.svg';
import { ReactComponent as CloseIcon } from '../../../../assets/images/shipment-popup-close.svg';
import useSaveUserSettings from 'src/renderer/hooks/useSaveUserSettings';
import SingleStateSelector from '../components/StateSelector/SingleStateSelector';
import { EmailTagInputField } from 'src/renderer/component/EmailTagInput';
import usePostDeleteShipment from 'src/renderer/hooks/usePostDeleteShipment';

interface InputFocusState {
  locationNickName: boolean;
  locationAddress: boolean;
  deliveryContactFirstName: boolean;
  deliveryContactLastName: boolean;
  deliveryContactPhoneNumber: boolean;
  deliveryContactEmail: boolean;
  shippingDocsEmail: boolean;
  deliveryApptRequired: boolean;
  receivingHours: boolean;
}

const MenuPropsTop = {
  classes: {
    paper: clsx(styles.Dropdownpaper, styles.resaleCertdropdown, styles.receivingHoursTop),
    list: styles.muiMenuList
  },
  // anchorOrigin: {
  //   vertical: -5,
  //   horizontal: "left"
  // },
  // transformOrigin: {
  //   vertical: "bottom",
  //   horizontal: "left"
  // },
}
const MenuPropsBottom = {
  classes: {
    paper: clsx(styles.Dropdownpaper, styles.resaleCertdropdown,styles.receivingHoursBottom),
    list: styles.muiMenuList
  },
  // anchorOrigin: {
  //   vertical: 27,
  //   horizontal: "left"
  // },
  // transformOrigin: {
  //   vertical: "top",
  //   horizontal: "left"
  // },
}



const ShipmentsTab = ({selectedShipment, isCreate, closeDialog, onSuccess}: any) => {
  const { userData, showLoader, setShowLoader, referenceData }: any = useGlobalStore();
  const [States, setStates] = useState([]);
  const [ResaleExpiration, setResaleExpiration] = useState([]);
  const { showCommonDialog, resetDialogStore }: any = useDialogStore();
  const [openDeleteConfirmation, setOpenDeleteConfirmation] = useState(false);
  const shipmentPopupRef = useRef(null);
  const { mutateAsync: saveUserSettings } = useSaveUserSettings();
  const { buyerSetting }: any = useBuyerSettingStore();
  const {
    register,
    handleSubmit,
    clearErrors,
    setError,
    setValue,
    reset,
    watch,
    control,
    getValues,
    trigger,
    resetField,
    formState: { errors, dirtyFields, isDirty, isValid, isSubmitting },
    getFieldState,
  } = useForm({
    resolver: yupResolver(shipmentsSchema),
    mode: 'onSubmit',
  });
  const isButtonDisabled =  !isDirty || isSubmitting;
  const { mutateAsync: postDeleteShipment } = usePostDeleteShipment();
  const [isAddressContainerClicked, setIsAddressContainerClicked] = useState(false);
  const stateSelectorRef = useRef<HTMLDivElement>(null);
  const [isStateSelectorFocused, setIsStateSelectorFocused] = useState(false);
  const addressContainerRef = useRef<HTMLDivElement>(null);
  const line1InputRef = useRef<HTMLInputElement>(null);
  const line2InputRef = useRef<HTMLInputElement>(null);
  const cityInputRef = useRef<HTMLInputElement>(null);
  const stateInputRef = useRef<HTMLInputElement>(null);
  const zipInputRef = useRef<HTMLInputElement>(null);
  const locationNickNameInputRef = useRef<HTMLInputElement>(null);
  const [isZipcodeValid, setIsZipcodeValid] = useState(false);
  const [isCompanyAddressPresent, setIsCompanyAddressPresent] = useState(false);
  const [useCompanyAddress, setUseCompanyAddress] = useState(false);
  const [isInputFocused, setIsInputFocused] = useState<any>({
    locationNickName: false,
    locationAddress: false,
    deliveryContactFirstName: false,
    deliveryContactLastName: false,
    deliveryContactPhoneNumber: false,
    deliveryContactEmail: false,
    shippingDocsEmail: false,
    deliveryApptRequired: false,  
    receivingHours: false,
  });
  const [fadeLoaderMessage, setFadeLoaderMessage] = useState('');
  const [isFadeLoaderOpen, setIsFadeLoaderOpen] = useState(false);

  const defaultUserAvailability = [
    {
      "day": "Monday",
      "from": "7",
      "to": "16",
      "display_name": "Mon",
      "is_user_available": 1,
      "receivingHrsFrom": RecevingHoursFrom,
      "receivingHrsTo": RecevingHoursTo,
    },
    {
      "day": "Tuesday",
      "from": "7",
      "to": "16",
      "display_name": "Tue",
      "is_user_available": 1,
      "receivingHrsFrom": RecevingHoursFrom,
      "receivingHrsTo": RecevingHoursTo,
    },
    {
      "day": "Wednesday",
      "from": "7",
      "to": "16",
      "display_name": "Wed",
      "is_user_available": 1,
      "receivingHrsFrom": RecevingHoursFrom,
      "receivingHrsTo": RecevingHoursTo,
    },
    {
      "day": "Thursday",
      "from": "7",
      "to": "16",
      "display_name": "Thu",
      "is_user_available": 1,
      "receivingHrsFrom": RecevingHoursFrom,
      "receivingHrsTo": RecevingHoursTo,
    },
    {
      "day": "Friday",
      "from": "7",
      "to": "16",
      "display_name": "Fri",
      "is_user_available": 1,
      "receivingHrsFrom": RecevingHoursFrom,
      "receivingHrsTo": RecevingHoursTo,
    }
  ]

  useEffect(() => {
    if (isCreate) {
      setValue("dates", defaultUserAvailability)
      setValue('deliveryApptRequired', false)
      if(buyerSetting?.delivery_address?.length < 1){
        setValue('isDefault', true)
      }
    }else{
      setValue("id", selectedShipment?.id);
      setValue("locationNickName", selectedShipment?.location_nickname);
             setValue('locationAddress', {
         line1: selectedShipment?.line1 || '',
         line2: selectedShipment?.line2 || '',
         city: selectedShipment?.city || '',
         state: selectedShipment?.state_id || null,
         stateCode: selectedShipment?.state_code || null,
         zip: selectedShipment?.zip || '',
       });
      setValue("deliveryApptRequired", Boolean(selectedShipment?.is_appt_required));
      setValue("deliveryContact", {
        firstName: selectedShipment?.first_name,
        lastName: selectedShipment?.last_name,
        phone: selectedShipment?.phone
        ? formatPhoneNumber(
            formatPhoneNumberRemovingCountryCode(selectedShipment?.phone)
          )
        : '',
        email: selectedShipment?.email_id,
      });
      setValue("shippingDocsEmail", selectedShipment?.shipping_docs_to);
      setValue("isDefault", Boolean(selectedShipment?.is_default));
      if (selectedShipment?.user_delivery_receiving_availability_details) {
        const weeks =
          selectedShipment?.user_delivery_receiving_availability_details.map(
            (day: any) => {
              day.receivingHrsFrom = [...RecevingHoursFrom];
              day.receivingHrsTo = [...RecevingHoursTo];
              return day;
            }
          );
        setValue('dates', weeks);
      }
    }
    if(buyerSetting?.company_address?.line1){
      setIsCompanyAddressPresent(true)
    }
  }, [isCreate, buyerSetting , selectedShipment]);

  useEffect(() => {
    if(locationNickNameInputRef?.current){
      setTimeout(() => {
        locationNickNameInputRef.current.focus();
      }, 100)
    }
  }, [locationNickNameInputRef?.current])
  
  useEffect(() => {
    if (referenceData) {
      setStates(referenceData.ref_states);
    }
  }, [referenceData]);

  useEffect(() => {
    if(errors?.locationAddress){
        setIsAddressContainerClicked(true)
    }
  }, [errors])

    // Custom clickaway handler
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (!isAddressContainerClicked) return;
        
        const target = event.target as HTMLElement;
        
        // Check if click is inside the address container
        if (addressContainerRef.current && addressContainerRef.current.contains(target)) {
          return;
        }
        
        // Check if click is inside any state selector
        const stateSelectorElement = document.querySelector('[data-state-selector]');
        if (stateSelectorElement && stateSelectorElement.contains(target)) {
          return;
        }
        
        // If we get here, the click was outside both the container and state selector
        handleShipmentAddressContainerClickAway();
      };
  
      // Add event listener when address container is clicked
      if (isAddressContainerClicked) {
        document.addEventListener('mousedown', handleClickOutside);
      }
  
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [isAddressContainerClicked]);

    useEffect(() => {
      handleStateZipValidation("locationAddress.zip", "locationAddress.state");
    }, [watch('locationAddress.zip'), watch('locationAddress.state')])

    useEffect(() => {
      console.log('isFadeLoaderOpen', isFadeLoaderOpen)
      console.log('fadeLoaderMessage', fadeLoaderMessage)
      if(isFadeLoaderOpen){
        setFadeLoaderMessage('Saving...');
      }else{
        if(fadeLoaderMessage === 'Saving...'){
          setFadeLoaderMessage('Saved');
          setTimeout(() => {
            setFadeLoaderMessage('Saved');
            if(!onSuccess) 
              closeDialog()
          }, 1000);
        }
      }
    }, [isFadeLoaderOpen, fadeLoaderMessage]);


  const handleInputFocus = (inputName: keyof InputFocusState): void => {
    setIsInputFocused((prevState) => ({
      ...prevState,
      [inputName]: true,
    }));
  };

  const handleInputBlur = (inputName: keyof InputFocusState): void => {
    setIsInputFocused((prevState) => ({
      ...prevState,
      [inputName]: false,
    }));
  };

  const handleAddressFieldClick = (fieldType: 'line1' | 'line2' | 'city' | 'state' | 'zip') => {
    setIsAddressContainerClicked(true);

    // Focus on the appropriate input after dialog opens
    setTimeout(() => {
        switch(fieldType) {
            case 'line1':
                line1InputRef?.current?.focus();
                break;
            case 'line2':
                line2InputRef?.current?.focus();
                break;
            case 'city':
                cityInputRef?.current?.focus();
                break;
            case 'state':
                stateInputRef?.current?.focus();
                break;
            case 'zip':
                zipInputRef?.current?.focus();
                break;
            default:
                line1InputRef?.current?.focus();
        }
    }, 100);
  };

  const handleAddressContainerClick = () => {
    if(!(watch('locationAddress.line1') || watch('locationAddress.line2') || watch('locationAddress.city') || watch('locationAddress.stateCode') || watch('locationAddress.zip'))){
        setIsAddressContainerClicked(true);
        setTimeout(() => {
            line1InputRef?.current?.focus();
        }, 100);
    }
  };

  const changeReceivingHrs = (dateIndex: any, isReceivingHrsFrom: any, dropdownValue: any) => {
    setValue(`dates.${dateIndex}.is_user_available`, true);
    const receivingHrsOption: any[] = [];
    let currentDropdown = `dates.${dateIndex}.to`;
    let adjacentDropdown = `dates.${dateIndex}.from`;
    let adjDropDownOptionsCopy = RecevingHoursFrom;
    let dropDownOptionsToBeDisabled = `dates.${dateIndex}.receivingHrsFrom`;
    let onChangingCancelAdjDropDownValue = RecevingHoursFrom[0].value;
    if (isReceivingHrsFrom) {
      currentDropdown = `dates.${dateIndex}.from`;
      adjacentDropdown = `dates.${dateIndex}.to`;
      adjDropDownOptionsCopy = RecevingHoursTo;
      onChangingCancelAdjDropDownValue = RecevingHoursTo[RecevingHoursTo.length - 2].value;
      dropDownOptionsToBeDisabled = `dates.${dateIndex}.receivingHrsTo`;
    }
    setValue(currentDropdown, dropdownValue.toString());
    if (dropdownValue === 'closed') {
      setValue(adjacentDropdown, dropdownValue.toString());
      setValue(`dates.${dateIndex}.is_user_available`, 0);
    }
    else if (watch(adjacentDropdown) === 'closed') {
      setValue(`dates.${dateIndex}.is_user_available`, 1);
      setValue(adjacentDropdown, onChangingCancelAdjDropDownValue.toString());
    }
    adjDropDownOptionsCopy.forEach(timeOption => {
      const time = { ...timeOption };
      if (dropdownValue !== 'closed' && ((!isReceivingHrsFrom && time.value >= dropdownValue) || (isReceivingHrsFrom && time.value <= dropdownValue))) time.disabled = true;
      receivingHrsOption.push(time);
    })
    setValue(dropDownOptionsToBeDisabled, receivingHrsOption);
  }

  const handleStateZipValidation = async (zipCode: any, stateCode: any) => {
    try {
      if (getValues(zipCode)?.length > 4 && getValues(stateCode)) {
        const payload = {
        data: {
          state_id: getValues(stateCode),
          zip_code: parseInt(getValues(zipCode)),
        },
      };
      const checkStateZipResponse = await axios.post(
        import.meta.env.VITE_API_SERVICE + "/user/checkStateZip",
        payload
      );
      if (checkStateZipResponse.data.data === true) {
        clearErrors([stateCode, zipCode]);
        setIsZipcodeValid(true);
        return true
      } else {
          setError(stateCode, { message: "The zip code and state code do not match" });
          setError(zipCode, { message: "The zip code and state code do not match" });
          setIsZipcodeValid(false);
          return false
        }
      }
    } catch (err) {
      console.error(err)
    }
  };

  const handleSaveShipment = async (data: any) => {
    try {
      setIsFadeLoaderOpen(true)
      let isZipValid : any = isZipcodeValid;
      if(!isZipValid){
        isZipValid = await handleStateZipValidation("locationAddress.zip", "locationAddress.state");
      }
      if(isZipValid){
        const isDeliveryAddressPresent = buyerSetting.delivery_address && buyerSetting.delivery_address.length > 0;
        const payload = {
          address_nickname: data.locationNickName,
          delivery_address: {
            line1: data.locationAddress.line1,
            line2: data.locationAddress.line2 || null,
            city: data.locationAddress.city,
            state_id: Number(data.locationAddress.state),
            zip: data.locationAddress.zip,
          },
          delivery_appt_required: data.deliveryApptRequired,
          delivery_contact_first_name: data.deliveryContact.firstName || null,
          delivery_contact_last_name: data.deliveryContact.lastName || null,
          delivery_phone: data.deliveryContact.phone || null,
          delivery_email_id: data.deliveryContact.email || null,
          shipping_docs_to: data.shippingDocsEmail,
          user_delivery_receiving_availability_details: data.dates.map((date: any) => {
            const { receivingHrsFrom, receivingHrsTo, ...rest } = date;
            return rest;
          }),
          is_default: isDeliveryAddressPresent ? data.isDefault : true,
        }
        if(data.id &&  !isCreate){
          payload.id = data.id;
        }
        const res =await saveUserSettings({ route: 'user/buyer/settings/shipment', data: payload });
        if(res){
          // queryClient.invalidateQueries()
          if(onSuccess){
            onSuccess()
            setIsFadeLoaderOpen(false)
          }else{
            setIsFadeLoaderOpen(false)
          }
        }
      }
    } catch (err) {
      console.error(err)
    } finally {
      setIsFadeLoaderOpen(false)
    }
  }

  const handleDeleteShipment = async () => {
    try{
      setIsFadeLoaderOpen(true)
      const payload = {
        data: {
          delivery_address_id: watch('id')
        }
      }
      const res = await postDeleteShipment(payload)
      if(res){
        // queryClient.invalidateQueries([reactQueryKeys.getBuyingPreference])
      }
    }catch(err){
      console.error(err)
    }finally{
      setIsFadeLoaderOpen(false)
      setOpenDeleteConfirmation(false)
    }
  }

  const handleShipmentAddressContainerClickAway = () => {
    setTimeout(() => {
    if (!isStateSelectorFocused) {
      if(!(errors?.locationAddress?.line1 || errors?.locationAddress?.line2 || errors?.locationAddress?.city || errors?.locationAddress?.state || errors?.locationAddress?.zip || errors?.locationAddress?.stateCode)){
        setIsAddressContainerClicked(false)
      }else{
        setIsAddressContainerClicked(true)
      }
    }
    handleInputBlur('locationAddress')
  }, 100)
  }

  const handleSameAsCompanyAddress = (checked: boolean) => {
    setUseCompanyAddress(checked);
    if (checked) {
      setValue('locationAddress', {
        line1: buyerSetting?.company_address?.line1 || '',
        line2: buyerSetting?.company_address?.line2 || '',
        city: buyerSetting?.company_address?.city || '',
        state: buyerSetting?.company_address?.state_id || '',
        stateCode: buyerSetting?.company_address?.state_code || '',
        zip: buyerSetting?.company_address?.zip || '',
      }, {
        shouldDirty: true,
        shouldTouch: true,
      });
    }
  }


  return (<>
    <div className={clsx(styles.shipmentTabContentContainer, isFadeLoaderOpen && styles.fadeLoaderOpen)} data-hover-video-id="settings-shipping">
      <div className={styles.shipmentTabContentHeader}>
        <h2>{isCreate ? "CREATE NEW SHIP-TO" : "EDIT SHIP-TO"}</h2>
        <button onClick={closeDialog}>{isCreate ? "Cancel" : "Cancel Editing"}<CloseIcon /></button>
      </div>
      <div className={styles.shipmentTabContentBody}>
      <div className={styles.shipmentTabContent}>
          <div className={clsx(styles.shipmentTabContentTitle)} htmlFor="isDefault">
            Set as default
          </div>
          <div className={styles.shipmentTabContentValue}>
            <CustomToggleCheckbox
              name="isDefault"
              control={control}
              canOnlyBeTrue={Boolean(selectedShipment?.is_default)}
              disabled={!buyerSetting?.delivery_address || buyerSetting.delivery_address.length === 0}
              onChange={
                (e: any) => {
                  setValue('isDefault', e);
                }
              }
            />
          </div>
        </div>
        <div className={styles.shipmentTabContent}>
          <div className={clsx(styles.shipmentTabContentTitle, isInputFocused.locationNickName && styles.focusLbl)}>
            Location Nickname
          </div>
          <div className={clsx(styles.shipmentTabContentValue, styles.inputMain)}>
            <InputWrapper>
              <CustomTextField
                className={clsx(styles.inputCreateAccount, errors?.locationNickName && styles.error)}
                type='text'
                register={register("locationNickName")}
                placeholder='Example: Houston Yard'
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register("locationNickName").onBlur(e);
                  handleInputBlur('locationNickName')
                }}
                autoFocus={true}
                onFocus={() => handleInputFocus('locationNickName')}
                inputRef={(e: any) => {
                  locationNickNameInputRef.current = e;
                }}
                errorInput={errors?.locationNickName}
                onKeyDown={(e: any) => {
                  if(e.key === 'Tab'){
                    if(!e.shiftKey){
                      if(!isCompanyAddressPresent) {
                        e.preventDefault();
                        setIsAddressContainerClicked(true)
                        setTimeout(() => {
                          line1InputRef.current?.focus();
                        }, 100)
                      }
                    }
                  }
                }}
              />
            </InputWrapper>
          </div>
        </div>
        <div className={clsx(styles.shipmentTabContent, styles.companyHQAddressContainer)}>
          <div className={clsx(styles.shipmentTabContentTitle, isInputFocused.locationAddress && styles.focusLbl)}>
            location address
            <div className={clsx(styles.sameAsCompanyAddressLabel, styles.checkingThisbox)}> 
              <label className={styles.containerChk}>
                <input
                  type="checkbox"
                  id="useCompanyAddress"
                  disabled={!isCompanyAddressPresent}
                  checked={useCompanyAddress}
                  onChange={(e) => {
                    handleSameAsCompanyAddress(e.target.checked);
                    if (e.target.checked) {
                      setIsAddressContainerClicked(false);
                    }
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Tab' && !e.shiftKey) {
                      setIsAddressContainerClicked(true)
                    }
                    if (e.key === 'Enter' || e.key === ' ') {

                      e.preventDefault();

                      if (!isCompanyAddressPresent) return; // Don't toggle if disabled

                      const newValue = !useCompanyAddress;

                      handleSameAsCompanyAddress(newValue);

                      if (newValue) {

                        setIsAddressContainerClicked(false);

                      }

                    }
                  }}
                  onFocus={() => handleInputFocus('locationAddress')}
                  style={{ marginRight: '8px' }}
                />
                <span className={styles.checkmark} />

              </label>
              <span >
                Same as Company Address
              </span>
            </div>
          </div>
          <div className={clsx(styles.shipmentTabContentValue, styles.locationAddressContainer)}>
              {
                (isAddressContainerClicked && !useCompanyAddress) ? 
                <div className={clsx(styles.customAddressContainer)} ref={addressContainerRef}>
                   <span className={clsx(styles.addresInputMain)}>
                    <InputWrapper>
                      <CustomTextField
                        className={clsx(styles.inputCreateAccount, errors?.locationAddress?.line1 && styles.error)}
                        type='text'
                        register={register('locationAddress.line1')}
                        placeholder='Address 1'
                        onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                          register('locationAddress.line1').onBlur(e);
                          handleInputBlur('locationAddress')
                        }}
                        onFocus={() => handleInputFocus('locationAddress')}
                        errorInput={errors?.locationAddress?.line1}
                        inputRef={(e: any) => {
                          line1InputRef.current = e;
                        }}
                        onKeyDown={(e) => {
                          if(e.key === 'Tab'){
                            if(e.shiftKey){
                              setIsAddressContainerClicked(false)
                            }
                          }
                        }}

                      />
                    </InputWrapper>
                </span>
                 <span className={clsx(styles.addresInputMain)}>
                  <InputWrapper>
                    <CustomTextField
                      className={clsx(styles.inputCreateAccount, errors?.locationAddress?.line2 && styles.error)}
                      type='text'
                      // autoFocus={true}
                      register={register('locationAddress.line2')}
                      placeholder='Address 2'
                      onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                        register('locationAddress.line2').onBlur(e);
                        handleInputBlur('locationAddress')
                      }}
                      onFocus={() => handleInputFocus('locationAddress')}
                      errorInput={errors?.locationAddress?.line2}
                      inputRef={(e: any) => {
                        line2InputRef.current = e;
                      }}
                    />
                  </InputWrapper>
                </span>
  
                <span className={styles.zipInputContainer}>
                  <span className={clsx(styles.col1,styles.addresInputMain)}>
                    <InputWrapper>
                      <CustomTextField
                        className={clsx(styles.inputCreateAccount, errors?.locationAddress?.city && styles.error)}
                        type='text'
                        register={register('locationAddress.city')}
                        placeholder='City'
                        onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                          register('locationAddress.city').onBlur(e);
                          handleInputBlur('locationAddress')
                        }}
                        onFocus={() => handleInputFocus('locationAddress')}
                        errorInput={errors?.locationAddress?.city}
                        inputRef={(e: any) => {
                          cityInputRef.current = e;
                        }}
                      />
                    </InputWrapper>
                  </span>
                  <span className={clsx(styles.inputSection, styles.yourLocationAdressState, styles.col2, styles.bdrRadius0, styles.bdrRight0)}>
                    <Controller
                      name="locationAddress.state"
                      control={control}
                      render={({ field }) => (
                        <div data-state-selector>
                          <SingleStateSelector
                            states={States.map((state: any) => ({ state_code: state.code }))}
                            value={field.value}
                            onChange={(stateCode) => {
                              const selectedState = States.find((state: any) => state.code === stateCode);
                              if (selectedState) {
                                field.onChange(selectedState.id);
                                setValue('locationAddress.stateCode', selectedState.code);
                                if (errors?.locationAddress?.state) {
                                  clearErrors('locationAddress.state');
                                }
                                setTimeout(() => {
                                  trigger('locationAddress.state');
                                }, 0);
                              } else {
                                console.error('State not found for code:', stateCode);
                              }
                            }}
                            onBlur={() => {
                              field.onBlur();
                              handleInputBlur('locationAddress');
                            }}
                            error={!!errors?.locationAddress?.state}
                            placeholder="State"
                            stateIdToCode={(stateId) => {
                              const state = States.find((s: any) => s.id === stateId);
                              return state ? state.code : watch('locationAddress.stateCode');
                            }}
                            onFocus={() => handleInputFocus('locationAddress')}
                            inputRef={stateInputRef}
                            />
                        </div>
                      )}
                    />
                  </span>
                  <span className={clsx(styles.col3,styles.addresInputMain)}>
                    <InputWrapper>
                      <CustomTextField
                        className={clsx(styles.inputCreateAccount, (errors?.locationAddress?.zip || errors?.locationAddress?.state) && styles.error)}
                        type='text'
                        maxLength={5}
                        autoFocus={false}
                        register={register('locationAddress.zip')}
                        placeholder='Zip Code'
                        onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                          register('locationAddress.zip').onBlur(e);
                          handleInputBlur('locationAddress');
                        }}
                        onFocus={() => handleInputFocus('locationAddress')}
                        errorInput={errors?.locationAddress?.zip || errors?.locationAddress?.state}
                        mode="wholeNumber"
                        inputRef={(e: any) => {
                          zipInputRef.current = e;
                        }}
                        onKeyDown={(e) => {
                          if(e.key === 'Tab'){
                            if(!e.shiftKey){
                              handleShipmentAddressContainerClickAway()
                            }
                          }
                        }}
                      />
                    </InputWrapper>
                  </span>
                </span>
  
              </div> : <div className={styles.addressDisplayContainer} onClick={handleAddressContainerClick}>
                {
                  (watch('locationAddress.line1') || watch('locationAddress.line2') || watch('locationAddress.city') || watch('locationAddress.state') || watch('locationAddress.zip')) ? (
                    <div className={styles.valueDiv}>
                      <p className={clsx(styles.addressInputs,styles.hideInputBackground)}
                         onClick={(e) => {
                             e.stopPropagation();
                             handleAddressFieldClick('line1');
                         }}>
                         {watch('locationAddress.line1') ? `${watch('locationAddress.line1')}` : ''}
                      </p>
                      <p className={clsx(styles.addressInputs,styles.hideInputBackground)}
                         onClick={(e) => {
                             e.stopPropagation();
                             handleAddressFieldClick('line2');
                         }}>
                         {watch('locationAddress.line2') ? `${watch('locationAddress.line2')}` : ''}
                      </p>
                      <span className={styles.lastAddressFiled}>
                        <p className={clsx(styles.addressInputsCol1,styles.hideInputBackground)}
                           onClick={(e) => {
                               e.stopPropagation();
                               handleAddressFieldClick('city');
                           }}>
                           {watch('locationAddress.city') ? `${watch('locationAddress.city')}` : ''}
                        </p>
                        <p className={clsx(styles.addressInputsCol2,styles.hideInputBackground)}
                           onClick={(e) => {
                               e.stopPropagation();
                               handleAddressFieldClick('state');
                           }}>
                           {watch('locationAddress.stateCode') ? `${watch('locationAddress.stateCode')}` : ''}
                        </p>
                        <p className={clsx(styles.addressInputsCol3,styles.hideInputBackground)}
                           onClick={(e) => {
                               e.stopPropagation();
                               handleAddressFieldClick('zip');
                           }}>
                           {watch('locationAddress.zip') ? `${watch('locationAddress.zip')}` : ''}
                        </p>
                      </span>
                    </div>
                  ) : (
                    <span className={styles.placeHolderDiv} onClick={handleAddressContainerClick}></span>
                  )
                }
              </div>
              }
            </div>
          </div>
        <div className={clsx(styles.shipmentTabContent, styles.receivingHoursInput)}>
          <div className={clsx(styles.shipmentTabContentTitle, isInputFocused.receivingHours && styles.focusLbl)} htmlFor="deliveryApptRequired">
            RECEIVING HOURS
          </div>
          <div className={styles.shipmentTabContentValue}>
            {watch('dates')?.map((x: any, i: any) => (<span key={x.day} className={styles.inputSectionRecevingHours}>
              <span className={`${watch(`dates.${i}.from`) !== 'closed' ? styles.daylbl1 : styles.daylbl1}`}>{x.display_name}</span>
              <span className={clsx(styles.daylbl2, 'w100 dflex')}>
                <CustomMenu
                  control={control}
                  defaultValue={x.from}
                  name={`dates.${i}.from`}
                  onfocus={() => handleInputFocus('receivingHours')}
                    onBlur={() => handleInputBlur('receivingHours')}
                  className={clsx((!dirtyFields.dates?.[i]?.from && 'disabledDropdown'), (x.from === 'closed' && 'txtClosed'), 'selectReceivingHours selectUploadCertDropdown')}
                  MenuProps={MenuPropsTop}
                  items={x.receivingHrsFrom}
                  IconComponent={DropdownIcon}
                  onChange={(events: any) => {
                    changeReceivingHrs(i, true, events.target.value);
                  }}
                  onFocusKeyDown={(e) => {
                    if(i === 0 && e.key === 'Tab' && e.shiftKey){
                      setIsAddressContainerClicked(true)
                    }
                  }}
                />
              </span>
              <span className={clsx(styles.daylbl3, 'w100 dflex')}>
                <CustomMenu
                  defaultValue={x.to}
                  control={control}
                  onfocus={() => handleInputFocus('receivingHours')}
                  onBlur={() => handleInputBlur('receivingHours')}
                  name={`dates.${i}.to`}
                  className={clsx((!dirtyFields.dates?.[i]?.to && 'disabledDropdown'), (x.to === 'closed' && 'txtClosed'), 'selectReceivingHours selectUploadCertDropdown')}
                  MenuProps={MenuPropsBottom}
                  IconComponent={DropdownIcon}
                  items={x.receivingHrsTo}
                  onChange={(events: any) => {
                    changeReceivingHrs(i, false, events.target.value);
                  }}
                />
              </span>
            </span>))}
          </div>
        </div>
        <div className={styles.shipmentTabContent}>
          <div className={clsx(styles.shipmentTabContentTitle, isInputFocused.shippingDocsEmail && styles.focusLbl)}>
            Email shipping docs to
          </div>
          <div className={styles.shipmentTabContentValue}>
            <EmailTagInputField
              value={watch('shippingDocsEmail') ? watch('shippingDocsEmail').split(',').filter(Boolean) : []}
              onChange={(emails) => {
                setValue('shippingDocsEmail', emails.join(','), { 
                  shouldDirty: true, 
                  shouldTouch: true 
                });
                // Trigger validation after setting the value
                clearErrors('shippingDocsEmail');
              }}
              placeholder=""
              maxEmails={5}
              register={register("shippingDocsEmail")}
              error={errors?.shippingDocsEmail}
              onBlur={() => {
                handleInputBlur('shippingDocsEmail');
              }}
              onFocus={() => {
                handleInputFocus('shippingDocsEmail');
              }}
              inputBlur={() => {
                handleInputBlur('shippingDocsEmail');
              }}
              control={control}
            />
          </div>
        </div>
        
        <div className={styles.shipmentTabContent}>
          <div className={clsx(styles.shipmentTabContentTitle, isInputFocused.deliveryApptRequired && styles.focusLbl)} htmlFor="deliveryApptRequired">
            DELIVERY APPT REQUIRED?
          </div>
          <div className={clsx(styles.shipmentTabContentValue)}>
            <Select
              value={watch('deliveryApptRequired') || false}
              onChange={(event: any) => {
                const value = event.target.value;
                setValue('deliveryApptRequired', value, {
                  shouldDirty: true,
                  shouldTouch: true,
                });
                
                // Clear delivery contact fields if "No" is selected
                if (value === false) {
                  setValue('deliveryContact.firstName', '', {
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue('deliveryContact.lastName', '', {
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue('deliveryContact.phone', '', {
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue('deliveryContact.email', '', {
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  
                  // Force clear any errors
                  clearErrors('deliveryContact.firstName');
                  clearErrors('deliveryContact.lastName');
                  clearErrors('deliveryContact.phone');
                  clearErrors('deliveryContact.email');
                }
              }}
              IconComponent={DropdownIcon}
              onFocus={() => handleInputFocus('deliveryApptRequired')}
              onBlur={() => handleInputBlur('deliveryApptRequired')}
              className={clsx('selectDropdown', styles.selectDropdown)}
              MenuProps={
                {
                  classes: {
                    paper: styles.dropDownBG
                  },
                }
              }
            >
              <MenuItem value={true}>Yes</MenuItem>
              <MenuItem value={false}>No</MenuItem>
            </Select>
          </div>
        </div>
        <div className={clsx(styles.shipmentTabContent, !watch('deliveryApptRequired') && styles.deliveryApptRequiredContainer)}>
          <div className={clsx(styles.shipmentTabContentTitle, (isInputFocused.deliveryContactFirstName || isInputFocused.deliveryContactLastName) && styles.focusLbl)} htmlFor="deliveryContactFirstName">
            DELIVERY CONTACT NAME
          </div>
          <div className={styles.shipmentTabContentValue}>
            <div className={styles.inputMain}>
               <InputWrapper>
              <CustomTextField
                className={clsx(
                  styles.inputCreateAccount,
                  errors?.deliveryContact?.firstName && styles.error
                )}
                id='deliveryContactFirstName'
                type='text'
                register={register('deliveryContact.firstName')}
                placeholder='FIRST NAME'
                tabIndex={watch('deliveryApptRequired') ? 0 : -1}
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register('deliveryContact.firstName').onBlur(e);
                  handleInputBlur('deliveryContactFirstName');
                }}
                onFocus={() => handleInputFocus('deliveryContactFirstName')}
                errorInput={errors?.deliveryContact?.firstName}
              // onKeyDown={(e) => {
              //   if(e.key === 'Tab'){
              //     if(e.shiftKey){
              //       setActiveTab('COMPANY');
              //     }
              //   }
              // }}
              />
            </InputWrapper>

            </div>
            <div className={styles.inputMain}>
            <InputWrapper>
              <CustomTextField
                className={clsx(
                  styles.inputCreateAccount,
                  errors?.deliveryContact?.lastName && styles.error
                )}
                id='deliveryContactLastName'
                type='text'
                register={register('deliveryContact.lastName')}
                placeholder='LAST NAME'
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register('deliveryContact.lastName').onBlur(e);
                  handleInputBlur('deliveryContactLastName');
                }}
                onFocus={() => handleInputFocus('deliveryContactLastName')}
                errorInput={errors?.deliveryContact?.lastName}
                tabIndex={watch('deliveryApptRequired') ? 0 : -1}
              // onKeyDown={(e) => {
              //   if(e.key === 'Tab'){
              //     if(e.shiftKey){
              //       setActiveTab('COMPANY');
              //     }
              //   }
              // }}
              />
            </InputWrapper>
            </div>
          </div>
        </div>
        <div className={clsx(styles.shipmentTabContent, !watch('deliveryApptRequired') && styles.deliveryApptRequiredContainer)}>
          <div className={clsx(styles.shipmentTabContentTitle, isInputFocused.deliveryContactPhoneNumber && styles.focusLbl)} htmlFor="deliveryPhoneNumber">
            DELIVERY PHONE NUMBER
          </div>
          <div className={clsx(styles.shipmentTabContentValue, styles.inputMain)}>
            <InputWrapper>
              <CustomTextField
                className={clsx(styles.inputCreateAccount, errors?.deliveryContact?.phone && styles.error)}
                type='tel'
                register={register("deliveryContact.phone")}
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register("deliveryContact.phone").onBlur(e);
                  handleInputBlur('deliveryContactPhoneNumber')
                }}
                onFocus={() => handleInputFocus('deliveryContactPhoneNumber')}
                errorInput={errors?.deliveryContact?.phone}
                mode="phoneNumber"
                placeholder='(XXX) XXX-XXXX'
                tabIndex={watch('deliveryApptRequired') ? 0 : -1}
              />
            </InputWrapper>
          </div>
        </div>
        <div className={clsx(styles.shipmentTabContent, !watch('deliveryApptRequired') && styles.deliveryApptRequiredContainer)}>
          <div className={clsx(styles.shipmentTabContentTitle, isInputFocused.deliveryContactEmail && styles.focusLbl)} htmlFor="deliveryEmailAddress">
            DELIVERY EMAIL ADDRESS
          </div>
          <div className={styles.shipmentTabContentValue}>
            <EmailTagInputField
              key={`email-${watch('deliveryContact.email') || 'empty'}`}
              value={watch('deliveryContact.email') ? watch('deliveryContact.email').split(',').filter(Boolean) : []}
              onChange={(emails) => {
                setValue('deliveryContact.email', emails.join(','), { 
                  shouldDirty: true, 
                  shouldTouch: true 
                });
                clearErrors('deliveryContact.email');
              }}
              placeholder=""
              maxEmails={5}
              register={register("deliveryContact.email")}
              error={errors?.deliveryContact?.email}
              onBlur={() => {
                handleInputBlur('deliveryContactEmail');
              }}
              onFocus={() => {
                handleInputFocus('deliveryContactEmail');
              }}
              inputBlur={() => {
                handleInputBlur('deliveryContactEmail');
              }}
              control={control}
              tabIndex={watch('deliveryApptRequired') ? 0 : -1}
            />
          </div>
        </div>
        </div>
        <div className={styles.footerContainer}>
          {
            (!isCreate) && (
                  <button className={styles.deleteBtn} disabled={buyerSetting?.delivery_address?.length <= 1 || selectedShipment?.is_default} onClick={() => setOpenDeleteConfirmation(true)}>Delete Ship-To</button>
            )
          }
          <div className={styles.saveBtnContainer}>
            <span className={clsx(styles.fadeLoaderMessage, fadeLoaderMessage === 'Saved' && styles.saveBtnFadeLoaderMessage)}>{fadeLoaderMessage}</span>
            <button className={styles.saveBtn} onClick={() => handleSubmit(handleSaveShipment)()} disabled={isButtonDisabled} >Save</button>
          </div>
          
        </div>
        <Dialog
          open={openDeleteConfirmation}
          transitionDuration={200}
          hideBackdrop
          classes={{
            root: styles.ErrorDialog,
            paper: styles.dialogContent
          }}
          container={shipmentPopupRef.current}
          style={{
            position: 'absolute',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            opacity: showLoader ? 0 : 1
          }}
          PaperProps={{
            style: {
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              margin: 0,
              backgroundColor: 'transparent'
            }
          }}
        >
          <div className={styles.deleteDialogContainer}>
            <p className={styles.deleteDialogTitle}>Are you sure you want to delete ?</p>
            <div className={styles.deleteBtnSection}>
              <button className={styles.submitYesBtn} onClick={handleDeleteShipment}>Yes</button>
              <button className={styles.submitNoBtn} onClick={() => setOpenDeleteConfirmation(false)}>No</button>
            </div>
          </div>
        </Dialog>

      </div>
    </>
    );
};

    export default ShipmentsTab;


