.searchTypeBtn.searchTypeBtn {
    background-color: #2b2c32;
    width: 220px;
    height: 48px;
    border-radius: 30px 0px 0px 30px;
    border: none;
    font-family: Inter;
    font-size: 16px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: 0.64px;
    text-align: left;
    color: #9b9eac;
    text-transform: capitalize;

    &:hover {
        background-color: #3b3c42;
        border: none;
    }
}

.dropdownIcon {
    margin-left: 10px;
}

.dropdownMenu {
    ul {
        padding: 8px;
        border-radius: 8px;
        -webkit-backdrop-filter: blur(22.7px);
        backdrop-filter: blur(22.7px);
        background-color: rgba(113, 115, 127, 0.7);

        li {
            padding: 8px 12px;
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: normal;
            text-align: left;
            color: rgba(255, 255, 255, 0.7);
            padding-left: 30px;
            position: relative;

            svg {
                position: absolute;
                left: 10px;
            }

            &:hover {
                border-radius: 6.8px;
                background-color: #c3c4ca;
                font-family: Inter;
                font-size: 14px;
                font-weight: bold;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.3;
                letter-spacing: normal;
                text-align: left;
                color: #0f0f14;

                svg {
                    path {
                        stroke: #0f0f14;
                        fill: #0f0f14;
                    }
                }
            }
        }
    }
}

.globalSearchField {
    position: relative;

    &:focus-within {
        .searchIcon {
            path {
                fill: #0f0f14;
            }
        }

        &:hover {
            .searchIcon {
                path {
                    fill: #0f0f14;
                }
            }
        }
    }

    &:hover {
        .searchInput {
            color: #fff;

            &::placeholder {
                color: #fff;
            }
        }

        .searchIcon {
            path {
                fill: #fff;
            }
        }
    }

    .searchIcon {
        position: absolute;
        left: 16px;
        top: 50%;
        transform: translateY(-50%);
    }

    .searchInput {
        background: transparent;
        border-radius: 0px 30px 30px 0px;
        background-color: rgba(255, 255, 255, 0.04);
        width: 523px;
        height: 48px;
        padding: 16px 16px 16px 56px;
        font-family: Inter;
        font-size: 16px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: 0.64px;
        text-align: left;
        color: #9b9eac;
        outline: none;
        border: none;

        &:focus {
            background-color: #fff;
            color: #0f0f14;

            &::placeholder {
                color: #0f0f14;
            }

            .searchIcon {
                path {
                    stroke: #0f0f14;
                }
            }
        }
    }
}


@keyframes searchMenuPulse {
    0% {
        background-color: rgba(55, 50, 50, .5);
    }

    50% {
        background-color: rgb(50, 50, 50);
    }

    100% {
        background-color: rgba(55, 50, 50, 0.5);
    }
}

.searchResultMenuPaper.searchResultMenuPaper {
    padding: 8px;
    border-radius: 8px;
    -webkit-backdrop-filter: blur(22.7px);
    backdrop-filter: blur(22.7px);
    background-color: rgba(113, 115, 127, 0.7);
    box-shadow: none;
    width: 523px;
    overflow-y: auto;
    max-height: 300px;

    &.searchResultMenuPaperLoading {
        animation: searchMenuPulse 2s ease-in-out infinite;
        min-height: 140px;
        /* ensure visible area while loading */
    }

    &::-webkit-scrollbar {
        height: 5px;
        width: 5px;
    }

    .searchResultMenuItem {
        padding: 8px 12px;
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: normal;
        text-align: left;
        color: rgba(255, 255, 255, 0.7);
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;

        &:hover {
            border-radius: 6.8px;
            background-color: #c3c4ca;
            font-family: Inter;
            font-size: 14px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: normal;
            text-align: left;
            color: #0f0f14;

            .searchResultMenuItemDateTime {
                color: #0f0f14;
            }
        }

        &.selected {
            border-radius: 6.8px;
            background-color: #c3c4ca;
            font-family: Inter;
            font-size: 14px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: normal;
            text-align: left;
            color: #0f0f14;

            .searchResultMenuItemDateTime {
                color: #0f0f14;
            }
        }

        .searchResultMenuItemLabel {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 80%
        }

        .searchResultMenuItemDateTime {
            text-align: right;
            flex: 1;
            font-family: Inter;
            font-size: 12px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: normal;
            color: rgba(255, 255, 255, 0.7);
        }
    }
}

.subscribeBtn.subscribeBtn {
    font-family: Syncopate;
    font-size: 14px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: 0.56px;
    text-align: left;
    color: #0f0f14;
    width: 152px;
    height: 48px;
    border-radius: 5000px;
    background-color: #32ff6c;
    margin-right: 16px;

    &:hover {
        background-color: #68ee8e;
    }
}

.searchResultGroupHeader {
    font-weight: 600;
    font-size: 0.8rem;
    opacity: 0.7;
    padding: 6px 10px;
    text-transform: uppercase;
}

.searchResultDivider {
    margin: 4px 0;
}

.searchResultLoading,
.searchResultEmpty {
    padding: 8px 12px;
    font-style: italic;
}


.searchResultMenuPaper {
    position: relative;
    min-width: 360px;
    max-height: 320px;
    overflow: auto;
    border-radius: 12px;
}

.searchResultMenuPaperLoading {
    /* animated gradient from dark to light and back */
    //   background: linear-gradient(90deg, #e9edf2, #f7f9fc, #e9edf2);
    background-size: 200% 100%;
    animation: shimmerBg 2.5s ease-in-out infinite;
}

.loadingOverlay {
    position: absolute;
    inset: 0;
    pointer-events: none;
    background: linear-gradient(132deg, #444444, #4f505a, #444444);
    background-size: 200% 100%;
    animation: shimmerBg 2.5s ease-in-out infinite;
    opacity: 0;
    transition: opacity 180ms ease;
    /* fade */
    will-change: opacity;
    border-radius: inherit;
}

.loadingOverlayVisible {
    opacity: 1;
}

.loadingOverlayHidden {
    opacity: 0;
}

@keyframes shimmerBg {
    0% {
        background-position: 0% 0%;
    }

    50% {
        background-position: 100% 0%;
    }

    100% {
        background-position: 0% 0%;
    }
}

/* Content fades opposite to the overlay */
.contentLayer {
    position: relative;
    /* scrollable area stays the same */
    transition: opacity 180ms ease;
    will-change: opacity;
}

.contentLayerHidden {
    opacity: 0;
}

.contentLayerVisible {
    opacity: 1;
}

@keyframes shimmerBg {
    0% {
        background-position: 0% 0%;
    }

    50% {
        background-position: 100% 0%;
    }

    100% {
        background-position: 0% 0%;
    }
}

/* ensures the empty dropdown has some height while loading */
.searchResultLoadingSpacer {
    height: 180px;
    /* adjust to taste */
}

/* existing helpers you already had */
.searchResultEmpty {
    padding: 8px 12px;
    font-style: italic;
    min-height: 180px;
    /* stabilizes height on empty */
}