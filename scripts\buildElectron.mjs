/**
 * Client Build Script
 * Usage: node scripts/buildClient.mjs <client-id> <environment>
 * Example: node scripts/buildClient.mjs bryzos demo
 */
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const args = process.argv.slice(2);

let clientId, environment;

if (args.length >= 2) {
    console.log(`args: ${args}`);
    [clientId, environment] = args;
} else {
    // Fall back to environment variables (for npm script usage)
    console.log(`process.env: ${process.env}`);
    clientId = process.env.CLIENT_ID;
    environment = process.env.NODE_ENV;
}

// if (args.length < 2) {
//   console.error('Usage: node scripts/buildClient.mjs <client-id> <environment>');
//   console.error('Example: node scripts/buildClient.mjs bryzos demo');
//   console.error('\nAvailable environments: development, demo, qa, staging, production');
//   process.exit(1);
// }
console.log(`clientId: ${clientId}, environment: ${environment}`);
// const [clientId, environment] = args;

/**
 * Client Configuration Manager
 * This script handles loading and applying client-specific configurations
 * Each client has:
 * - client.config.json: metadata (name, version, icons, etc.)
 * - config.{environment}.json: environment-specific settings
 */

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

/**
 * Get client metadata configuration
 * @param {string} clientId - The client identifier (e.g., 'bryzos', 'example-client')
 * @returns {object} Client metadata configuration object
 */
export function getClientConfig(clientId) {
  const clientConfigPath = path.join(rootDir, 'clients', clientId, 'client.config.json');
  
  if (!fs.existsSync(clientConfigPath)) {
    throw new Error(`Client configuration not found for: ${clientId}. Path: ${clientConfigPath}`);
  }
  
  const configContent = fs.readFileSync(clientConfigPath, 'utf-8');
  return JSON.parse(configContent);
}

/**
 * Get environment-specific configuration for a client
 * @param {string} clientId - The client identifier
 * @param {string} environment - The environment (development, demo, qa, staging, production)
 * @returns {object} Environment configuration object
 */
export function getEnvironmentConfig(clientId, environment) {
  const envConfigPath = path.join(rootDir, 'clients', clientId, `config.${environment}.json`);
  
  if (!fs.existsSync(envConfigPath)) {
    throw new Error(`Environment config not found for client '${clientId}' in environment '${environment}'. Path: ${envConfigPath}`);
  }
  
  const configContent = fs.readFileSync(envConfigPath, 'utf-8');
  return JSON.parse(configContent);
}

/**
 * Generate environment-specific config for electron
 * Simply copies the client's environment config to config-electron/
 * This maintains compatibility with the existing configMoveElectron.mjs workflow
 * 
 * @param {string} clientId - The client identifier
 * @param {string} environment - The environment (development, demo, qa, staging, production)
 */
export function generateElectronConfig(clientId, environment) {
  const envConfig = getEnvironmentConfig(clientId, environment);
  
  // Write to config-electron directory (used by configMoveElectron.mjs)
//   const outputPath = path.join(rootDir, 'config-electron', `config.${environment}.json`);
  const outputPath = path.join(rootDir, 'src', 'main', `config.js`);
  const content = `
// this is is auto generated and should not be edited manually, can be created using configMoveElectron.mjs
const config = ${JSON.stringify(envConfig, null, 2)}

export default config;
`
  fs.writeFileSync(outputPath,content, 'utf-8');
  
  console.log(`✓ Generated electron config: ${outputPath}`);
  return envConfig;
}

/**
 * Copy branding assets from client folder to public folder
 * @param {string} clientId - The client identifier
 */
export function copyBrandingAssets(clientId) {
  const clientConfig = getClientConfig(clientId);
  const brandingDir = path.join(rootDir, 'clients', clientId, 'branding');
  const publicDir = path.join(rootDir, 'public');
  
  if (!fs.existsSync(brandingDir)) {
    console.warn(`⚠ Branding directory not found: ${brandingDir}`);
    return;
  }
  
  // Get list of files to copy from client config
  const filesToCopy = [
    clientConfig.icon.ico,
    clientConfig.icon.icns,
    clientConfig.icon.png,
    clientConfig.dmg?.background,
    clientConfig.squirrel?.loadingGif,
    'favicon.ico',
    'onboardingLogo.png',
    'bg-app.png',
    'bg-img.jpg'
  ].filter(Boolean); // Remove undefined values
  
  let copiedCount = 0;
  filesToCopy.forEach(filename => {
    const sourcePath = path.join(brandingDir, filename);
    const destPath = path.join(publicDir, filename);
    
    if (fs.existsSync(sourcePath)) {
      fs.copyFileSync(sourcePath, destPath);
      copiedCount++;
    }
  });
  
  console.log(`✓ Copied ${copiedCount} branding assets to public folder`);
}

/**
 * Update package.json with client-specific information
 * @param {string} clientId - The client identifier
 */
export function updatePackageJson(clientId) {
  const clientConfig = getClientConfig(clientId);
  const packageJsonPath = path.join(rootDir, 'package.json');
  
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'));
  
  // Update with client-specific values
  packageJson.name = clientConfig.packageName;
  packageJson.productName = clientConfig.productName;
  packageJson.version = clientConfig.version;
  
  // Backup original package.json if not exists
  const backupPath = path.join(rootDir, 'package.json.backup');
  if (!fs.existsSync(backupPath)) {
    fs.copyFileSync(packageJsonPath, backupPath);
  }
  
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n', 'utf-8');
  console.log(`✓ Updated package.json for client: ${clientConfig.clientName}`);
}

/**
 * Copy all asset files from client folder to public/asset folder
 * @param {string} clientId - The client identifier
 */
export function copyAssetFiles(clientId) {
  const clientAssetDir = path.join(rootDir, 'clients', clientId, 'asset');
  const publicAssetDir = path.join(rootDir, 'public', 'asset');
  
  if (!fs.existsSync(clientAssetDir)) {
    console.log(`ℹ No asset directory found in clients/${clientId}/`);
    return;
  }
  
  // Ensure public/asset directory exists
  if (!fs.existsSync(publicAssetDir)) {
    fs.mkdirSync(publicAssetDir, { recursive: true });
  }
  
  // Read all files in client asset directory
  const files = fs.readdirSync(clientAssetDir);
  let copiedCount = 0;
  
  files.forEach(filename => {
    const sourcePath = path.join(clientAssetDir, filename);
    const destPath = path.join(publicAssetDir, filename);
    
    // Check if it's a file (not a directory)
    if (fs.statSync(sourcePath).isFile()) {
      // Copy the file (overwrites if exists)
      fs.copyFileSync(sourcePath, destPath);
      copiedCount++;
    }
  });
  
  console.log(`✓ Copied ${copiedCount} asset files to public/asset folder`);
}

/**
 * Generate forge config data for client
 * @param {string} clientId - The client identifier
 * @returns {object} Forge config overrides
 */
export function getForgeConfigOverrides(clientId) {
  const clientConfig = getClientConfig(clientId);
  
  return {
    name: clientConfig.appName,
    icon: `./public/${clientConfig.icon.base}`,
    squirrel: {
      loadingGif: `./public/${clientConfig.squirrel.loadingGif}`,
      authors: clientConfig.authors,
      description: clientConfig.description,
      iconUrl: clientConfig.squirrel.iconUrl
    },
    dmg: {
      icon: `./public/${clientConfig.icon.icns}`,
      background: `./public/${clientConfig.dmg.background}`,
      iconSize: clientConfig.dmg.iconSize
    }
  };
}

/**
 * Restore package.json from backup
 */
export function restorePackageJson() {
  const packageJsonPath = path.join(rootDir, 'package.json');
  const backupPath = path.join(rootDir, 'package.json.backup');
  
  if (fs.existsSync(backupPath)) {
    fs.copyFileSync(backupPath, packageJsonPath);
    console.log(`✓ Restored package.json from backup`);
  }
}

/**
 * Main function to setup client configuration
 * @param {string} clientId - The client identifier
 * @param {string} environment - The environment
 */
export function setupElectronConfig(clientId, environment) {
  console.log(`\n🔧 Setting up configuration for client: ${clientId} (${environment})\n`);
  
  try {
        
    // Update package.json
    updatePackageJson(clientId);
    
    // Generate electron config (copies from clients/{clientId}/config.{env}.json to config-electron/)
    generateElectronConfig(clientId, environment);
    
    // Copy branding assets
    copyBrandingAssets(clientId);
    
    // Copy asset files
    copyAssetFiles(clientId);
    
    // Copy environment files
    // copyEnvFiles(clientId, environment);

    
    console.log(`\n✅ Client configuration setup complete!`);
    console.log(`   Next: Run configMoveElectron.mjs to generate src/main/config.js\n`);
  } catch (error) {
    console.error(`\n❌ Error setting up client config: ${error.message}\n`);
    process.exit(1);
  }
}

// If run directly (not imported)
if (import.meta.url === `file://${process.argv[1]}`) {
  const clientId = process.env.CLIENT_ID || 'bryzos';
  const environment = process.env.NODE_ENV || 'development';
  console.log(`clientId: ${clientId}, environment: ${environment}`);
//   setupClientConfig(clientId, environment);
}

// Setup client configuration
setupElectronConfig(clientId, environment);