import { compareVersions, getChannelWindow } from '@bryzos/giss-ui-library';
import { useElementSize } from '@mantine/hooks';
import { useEffect } from 'react';
import { routes } from 'src/renderer/common';
export const useHeightListener = (appVersion, resumeApp) => {
    const { ref, height, width } = useElementSize();
    const channelWindow:any =  getChannelWindow() ;
    const minHeight = 102;
    const minWidth = 800;
    useEffect(() => {
        const newHeight = ref?.current.offsetHeight <= minHeight ? minHeight : ref?.current.offsetHeight;
        const newWidth = ref?.current.offsetWidth !== 0 ? ref?.current.offsetWidth : minWidth;
        if(channelWindow?.updateHeight && location.pathname !== routes.newUpdate)
            if(compareVersions(appVersion, '2.0.0') >= 0)
            (window as any).electron.send({ channel: channelWindow.updateHeight, data: {height: newHeight, width: newWidth}})
            else
            (window as any).electron.send({ channel: channelWindow.updateHeight, data: newHeight})
    }, [height, width, resumeApp]);
    return ref
}
