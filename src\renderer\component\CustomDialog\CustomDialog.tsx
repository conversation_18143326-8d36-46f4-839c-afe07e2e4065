import React from 'react';
import { Dialog, DialogContent, DialogActions, Button } from '@mui/material';
import Draggable from 'react-draggable';

const CustomDialog = ({ open, onClose, data }) => {
  return (
    <Draggable 
  handle={'[class*="MuiDialog-root"]'} 
  cancel={'[class*="MuiDialogContent-root"]'}>
    <Dialog open={open} 
    onClose={(event, reason) => {
        if (reason !== 'backdropClick') {
          onClose();
        }
      }}
    disableEnforceFocus 
    hideBackdrop
    disableAutoFocus
    disableBackdropClick 

    style={{
        width:500,
        height:300,
        left:400,
        top:0
    }}

    PaperProps={{
        sx: {
          backgroundColor: 'black',
          color: 'white'
        }
  }}>
      <DialogContent sx={{ color: 'white', userSelect: 'text' }}>
        <div style={{ userSelect: 'text' }}>
        <p><span style={{ userSelect: 'text' }}>PDF String- </span><span style={{ userSelect: 'text' }}>{data.pdf_string}</span></p>
        <p><span style={{ userSelect: 'text' }}>Split String- </span><span style={{ userSelect: 'text' }}>{data.split_string}</span></p>
        <p><span style={{ userSelect: 'text' }}>Search String- </span><span style={{ userSelect: 'text' }}>{data.search_string}</span></p>
        </div>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} sx={{ color: 'white' }}>OK</Button>
      </DialogActions>
    </Dialog>
    </Draggable>
  );
};

export default CustomDialog;
