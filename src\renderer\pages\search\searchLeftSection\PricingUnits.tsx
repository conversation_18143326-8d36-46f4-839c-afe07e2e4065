import React from 'react'

import styles from '../home.module.scss'
import clsx from 'clsx'
import { units } from 'src/renderer/common'
import { useSearchStore } from '@bryzos/giss-ui-library'

const PricingUnits = () => {
    const { selectedPriceUnit, setSelectedPriceUnit } = useSearchStore();

    return (
        <div className={styles.pricingUnit} data-hover-video-id='pricing-unit-on-search'>
            <div className={styles.leftFilterTitle}>PRICING UNITS</div>
            {units.map((unit) => (
                <button
                    key={unit.title}
                    className={clsx(styles.filterBtn, {
                        [styles.activeBtn]: selectedPriceUnit === unit.value,
                    })}
                    onClick={() => setSelectedPriceUnit(unit.value)}
                >
                    {unit.title}
                </button>
            ))}
        </div>
    )
}

export default PricingUnits