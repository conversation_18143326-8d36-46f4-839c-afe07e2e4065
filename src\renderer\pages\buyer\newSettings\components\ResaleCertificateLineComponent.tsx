import { useRef } from "react";
import styles from "../tabs/TabContent.module.scss"
import { Fade, Tooltip } from "@mui/material";
import clsx from "clsx";
import { CustomMenu } from "../../CustomMenu";
import { deleteCertificateLineTooltip } from "src/renderer/tooltip";
import { ReactComponent as DeleteIcon } from '../../../../assets/images/delete.svg';
import { ReactComponent as AddCertiIcon } from '../../../../assets/images/AddLine.svg';
import { ReactComponent as DeleteCertiIcon } from '../../../../assets/images/Remove-line.svg';
import { ReactComponent as IconUpload } from '../../../../assets/images/icon-upload.svg';
import { ReactComponent as DropdownIcon } from '../../../../assets/images/StateIconDropDpown.svg';

const ResaleCertificateLineComponent = ({ index, resaleCertificate, deleteResaleCertificateLine, deleteCertificateTooltip, openDeletePopup, register, uploadCertFile, States, ResaleExpiration, control, errors, setValue}) => {
    const resaleCertFileRef = useRef();
    const resaleCertEditHandler = () => {
        resaleCertFileRef?.current?.click();
    }
    const ExpirationMenuPropsBottom = {
        classes: {
                paper: clsx(styles.Dropdownpaper,styles.resaleCertdropdown),
                list: styles.muiMenuList,
                select: styles.selectClassName,
        },
        anchorOrigin: {
            vertical: 27,
            horizontal: "left"
        },
        transformOrigin: {
            vertical: "top",
            horizontal: "left"
        },
    }

    const handleCertFileChange = (e, index) => {
        if(e.target.files[0]){
            setValue(`resaleCertificateList.${index}.uploadCertProgress`, false)
            setValue(`resaleCertificateList.${index}.state_id`, '');
            setValue(`resaleCertificateList.${index}.expiration_date`, '');
            setValue(`resaleCertificateList.${index}.status`, null);
        }
    }

    const isSubmitDisabled = () => {
        return !resaleCertificate?.resaleCertFile || !resaleCertificate?.state_id || !resaleCertificate?.expiration_date || !resaleCertificate?.resaleCertFile?.length 
    }

    const handleCertFileSubmit = () => {
        const file = resaleCertificate?.resaleCertFile[0]
        uploadCertFile(file, index)
    }

    return (
        <div className={clsx('dflex',styles.resaleCertContainerMain, !!!resaleCertificate.is_deletable && styles.disabledCert)} key={index}>
            <span className={clsx(styles.lblInput, styles.resaleFirstBottomDiv, styles.delIconCertif)}>
                {/* {index > 0 && !resaleCertificate.id &&
                    <Tooltip
                        title={deleteCertificateLineTooltip()}
                        arrow
                        placement={'right'}
                        disableInteractive
                        TransitionComponent={Fade}
                        TransitionProps={{ timeout: 200 }}
                        classes={{
                            tooltip: 'inputTooltip',
                        }}
                    >
                        <button onClick={() => deleteResaleCertificateLine(index)} className={clsx(styles.deleteCertBtn, styles.deleteCertLineBtn)}><DeleteCertiIcon /></button>
                    </Tooltip>
                } */}
                <span className={clsx(styles.state1, index > 1 && styles.state1AddLine)}><span className={styles.stateLbl}>{ 'State ' + (index + 1)}</span>
                    {resaleCertificate?.cerificate_url_s3 ?
                        <>
                            <a href={resaleCertificate?.cerificate_url_s3} className={styles.viewCert}>(View Cert)</a>
                            {resaleCertificate?.id && !!resaleCertificate.is_deletable && resaleCertificate?.status !== 'Pending' &&
                                <Tooltip
                                    title={deleteCertificateTooltip()}
                                    arrow
                                    placement={'right'}
                                    disableInteractive
                                    TransitionComponent={Fade}
                                    TransitionProps={{ timeout: 200 }}
                                    classes={{
                                        tooltip: 'inputTooltip',
                                    }}
                                >
                                    <button onClick={() => openDeletePopup(resaleCertificate?.id, index)} className={styles.deleteCertBtn}><DeleteIcon /></button>
                                </Tooltip>
                            }

                        </>
                        :   <>
                                {index > 0 && <button onClick={() => deleteResaleCertificateLine(index)} className={styles.deleteCertBtn}><DeleteIcon /></button>}
                            </>
                        }
                </span>
            </span> 
            <span className={styles.uploadCertCol}>
                
                {resaleCertificate?.uploadCertProgress === true ?
                    <label>
                        <span className={styles.uploadText}>Uploading</span>
                    </label>
                    : resaleCertificate?.uploadCertProgress === false ?
                            <button className={styles.uploadFileBtn} onClick={resaleCertEditHandler}>Replace </button>
                        :
                            <button className={styles.uploadIcon} onClick={resaleCertEditHandler}><IconUpload/></button>

                }
                {/* <input {...register(`resaleCertificateList.${index}.resaleCertFile`)} type='file' onChange={(e) => { uploadCertFile(e, index); register(`resaleCertificateList.${index}.resaleCertFile`).onChange(e) }} ref={(e) => { resaleCertFileRef.current = e; register(`resaleCertificateList.${index}.resaleCertFile`).ref(e); }} /> */}
                <input className={styles.uploadFileInput} {...register(`resaleCertificateList.${index}.resaleCertFile`)} type='file'  onChange={(e) => { handleCertFileChange(e, index); register(`resaleCertificateList.${index}.resaleCertFile`).onChange(e) }} ref={(e) => { resaleCertFileRef.current = e; register(`resaleCertificateList.${index}.resaleCertFile`).ref(e); }} />

            </span>

            <span className={styles.resaleCertDropdown}>
                {
                     resaleCertificate?.status ?  (
                        <p className={styles.dropdownValue}>{resaleCertificate?.state_code ?? ''}</p>
                     ) : 

                    <CustomMenu
                        control={control}
                        name={`resaleCertificateList.${index}.state_id`}
                        placeholder={''}
                        IconComponent={DropdownIcon}
                        MenuProps={{
                            classes: {
                                paper: clsx(styles.Dropdownpaper, styles.resaleCertdropdown),
                                list: styles.muiMenuList,
                                select: styles.selectClassName,
                            },
                            anchorOrigin: {
                                vertical: 27,
                                horizontal: "left"
                            },
                            transformOrigin: {
                                vertical: "top",
                                horizontal: "left"
                            },
                        }}
                        items={States.map(x => ({ title: x.code, value: x.id }))}
                        className={clsx('selectUploadCertDropdown', errors?.resaleCertificateList?.[index]?.state_id && styles.borderOfError)}

                    />
                                    }

            </span>
            <span className={clsx(styles.resaleCertDropdown,styles.resaleCertDropdown1)}>
                {
                    resaleCertificate?.status ? 
                    <p className={clsx(styles.dropdownValue,styles.dropdownValue1)}>
                        {ResaleExpiration.find(exp => exp.value === resaleCertificate?.expiration_date)?.title || resaleCertificate?.expiration_date}
                    </p> :
                    <CustomMenu
                    control={control}
                    name={`resaleCertificateList.${index}.expiration_date`}
                    placeholder={''}
                    IconComponent={DropdownIcon}
                    MenuProps={ExpirationMenuPropsBottom}
                    items={ResaleExpiration}
                    className={clsx('selectUploadCertDropdown', errors?.resaleCertificateList?.[index]?.expiration_date && styles.borderOfError)}
                    />
                }
            </span>    
            <span className={styles.uploadCertBtnStatus}>
                {(!resaleCertificate?.status) ? <>
                    <button className={styles.uploadCertBtn} disabled={isSubmitDisabled()} onClick={handleCertFileSubmit}><span>SUBMIT</span></button>
                </> :
                    <>
                        <div className={clsx(styles.UploadCertStatus, resaleCertificate?.status === 'Pending' && styles.pendingStatus)}>
                            {resaleCertificate?.status && resaleCertificate.status}
                        </div>
                    </>
                }

            </span>
        </div>
    )
}

export default ResaleCertificateLineComponent;