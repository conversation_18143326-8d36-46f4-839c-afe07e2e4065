import React, { useState, useEffect, useRef } from 'react';
import styles from './InteractiveStateSelector.module.scss';
import clsx from 'clsx';
import Popover from '@mui/material/Popover';
import { ReactComponent as DropdownIcon } from '../../../../../assets/images/StateIconDropDpown.svg';
import { ReactComponent as Shape1 } from '../../../../../assets/images/shape1.svg';
// import { ReactComponent as Shape2 } from '../../../../../assets/images/shape2.svg';

interface State {
  state_code: string;
}

interface SingleStateSelectorProps {
  states: State[];
  value?: string;
  onChange: (stateCode: string) => void;
  onBlur?: () => void;
  error?: boolean;
  placeholder?: string;
  stateIdToCode?: (stateId: string) => string;
  onFocus?: () => void;
  inputRef?: React.RefObject<HTMLInputElement>;
}

const SingleStateSelector: React.FC<SingleStateSelectorProps> = ({
  states,
  value,
  onChange,
  onBlur,
  error,
  placeholder = "Type to filter states...",
  onFocus,
  stateIdToCode,
  inputRef: externalInputRef
}) => {
  const [filterText, setFilterText] = useState('');
  const [filteredStates, setFilteredStates] = useState<State[]>(states);
  const [isFocused, setIsFocused] = useState(false);
  const [hoveredIndex, setHoveredIndex] = useState(-1);
  const internalInputRef = useRef<HTMLInputElement>(null);
  const inputRef = externalInputRef || internalInputRef;

  const getDisplayValue = () => {
    if (isFocused) return filterText;
    if (!value) return '';
    if (stateIdToCode) {
      return stateIdToCode(value);
    }
    const state = states.find(s => s.state_code === value);
    return state ? state.state_code : value;
  };

  const inputValue = getDisplayValue();

  useEffect(() => {
    setFilteredStates(states);
  }, [states]);

  const getNavigableStates = () => {
    if (!filterText.trim()) {
      return states;
    }
    return states.filter(state =>
      state.state_code.toLowerCase().startsWith(filterText.toLowerCase())
    );
  };

  const navigableStates = getNavigableStates();

  const getStateClass = (stateCode: string, index: number) => {
    const isSelected = value === stateCode;
    const isExactMatch = filterText && stateCode.toLowerCase() === filterText.toLowerCase();
    const isStartsWithMatch = filterText && stateCode.toLowerCase().startsWith(filterText.toLowerCase());
    const navigableIndex = navigableStates.findIndex(state => state.state_code === stateCode);
    const isHovered = navigableIndex === hoveredIndex && navigableIndex >= 0;

    return clsx(
      styles.stateItem,
      isSelected && styles.selected,
      isHovered && styles.hovered,
      !filterText && styles.default,
      isExactMatch && styles.exactMatch,
      isStartsWithMatch && !isExactMatch && styles.startsWithMatch,
      filterText && !isStartsWithMatch && styles.noMatch
    );
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilterText(e.target.value.toUpperCase());
    setHoveredIndex(-1);
  };

  // Helper function to move focus to next input
  const moveToNextInput = () => {
    // Find all focusable elements in the document
    const focusableElements = document.querySelectorAll(
      'input, select, textarea, button, [tabindex]:not([tabindex="-1"])'
    );
    
    // Find the current input's index
    const currentIndex = Array.from(focusableElements).findIndex(el => el === inputRef.current);
    
    if (currentIndex !== -1 && currentIndex < focusableElements.length - 1) {
      // Focus the next element
      const nextElement = focusableElements[currentIndex + 1] as HTMLElement;
      if (nextElement && nextElement.focus) {
        nextElement.focus();
      }
    }
  };

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === 'Tab') {
      if(!e.shiftKey){
      e.preventDefault();
      }
      if (hoveredIndex >= 0 && hoveredIndex < navigableStates.length) {
        onChange(navigableStates[hoveredIndex].state_code);
        setFilterText('');
        setHoveredIndex(-1);
        setIsFocused(false);
        // Move to next input instead of just blurring
        if(!e.shiftKey){
          setTimeout(() => moveToNextInput(), 0);
        }
        return;
      }

      const exactMatches = states.filter(state =>
        state.state_code.toLowerCase() === filterText.toLowerCase()
      );

      if (exactMatches.length === 1) {
        onChange(exactMatches[0].state_code);
        setFilterText('');
        setHoveredIndex(-1);
        setIsFocused(false);
        // Move to next input instead of just blurring
        if(!e.shiftKey){  
          setTimeout(() => moveToNextInput(), 0);
        }
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      if (navigableStates.length > 0) {
        const newIndex = hoveredIndex < navigableStates.length - 1 ? hoveredIndex + 1 : 0;
        setHoveredIndex(newIndex);
      }
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (navigableStates.length > 0) {
        const newIndex = hoveredIndex > 0 ? hoveredIndex - 1 : navigableStates.length - 1;
        setHoveredIndex(newIndex);
      }
    }
  };

  const handleStateMouseDown = (stateCode: string) => {
    // Handle selection here BEFORE input blur happens
    onChange(stateCode);
    setFilterText('');
    setHoveredIndex(-1);
    setIsFocused(false);
    // Move to next input instead of just blurring
    setTimeout(() => moveToNextInput(), 0);
  };

  const handleStateMouseEnter = (stateCode: string) => {
    const navigableIndex = navigableStates.findIndex(state => state.state_code === stateCode);
    if (navigableIndex >= 0) {
      setHoveredIndex(navigableIndex);
    }
  };

  const handleStateMouseLeave = () => {
    setHoveredIndex(-1);
  };

  const handleInputFocus = () => {
    onFocus?.();
    setIsFocused(true);
    // Instead of clearing, show the current selected state code
    if (value && stateIdToCode) {
      setFilterText(stateIdToCode(value));
    } else if (value) {
      const state = states.find(s => s.state_code === value);
      setFilterText(state ? state.state_code : value);
    } else {
      setFilterText('');
    }
  };

  const handlePopoverClose = () => {
    // Check if there's a matching state when closing
    if (filterText.trim()) {
      const exactMatches = states.filter(state =>
        state.state_code.toLowerCase() === filterText.toLowerCase()
      );
      
      if (exactMatches.length === 1) {
        // Select the matching state before closing
        onChange(exactMatches[0].state_code);
        // Add a small delay to ensure selection completes before closing
        setTimeout(() => {
          setIsFocused(false);
          setFilterText('');
          setHoveredIndex(-1);
          onBlur?.();
        }, 100);
        return; // Exit early to prevent immediate closing
      }
    }
    
    // If no matching state or no filter text, close immediately
    setIsFocused(false);
    setFilterText('');
    setHoveredIndex(-1);
    onBlur?.();
  };

  return (
    <div className={clsx(styles.addressInputsMain, isFocused && styles.selectShade)}>
      {isFocused && <>
        <div className={styles.shape1}>
          <Shape1/>
        </div>
        <div className={styles.shape2}>
          <Shape1/>
        </div>
      </>

      }
      <div className={clsx(styles.inputContainer, isFocused && styles.stateWrapperSingle)}>
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleInputKeyDown}
          onFocus={handleInputFocus}
          onBlur={handlePopoverClose}
          placeholder={placeholder}
       
          className={clsx(
            styles.input,
            error && styles.inputError,
            isFocused && styles.inputFocused
          )}
        />
         {isFocused && <DropdownIcon />}
      </div>

      <Popover
        open={isFocused}
        anchorEl={inputRef.current}
        onClose={handlePopoverClose}
        classes={{
          paper: styles.popperPaper,
        }}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal:-100,
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
        disableAutoFocus
        disableEnforceFocus
      >
        <div className={styles.statesGrid}>
          {filteredStates.map((state, index) => (
            <button
              type="button"
              key={state.state_code}
              className={getStateClass(state.state_code, index)}
              onMouseDown={e => {
                e.stopPropagation(); // Prevent clickaway
                handleStateMouseDown(state.state_code);
              }}
              onMouseEnter={() => handleStateMouseEnter(state.state_code)}
              onMouseLeave={handleStateMouseLeave}
            >
              {state.state_code}
            </button>
          ))}
        </div>
      </Popover>
    </div>
  );
};

export default SingleStateSelector;
