<svg width="325" height="161" viewBox="0 0 325 161" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#cdcp5t1eza)">
        <g clip-path="url(#mxidnd5rzb)">
            <path d="M1.4 16.01C1.4 7.168 8.57 0 17.41 0H307.59c8.842 0 16.01 7.168 16.01 16.01v138.085H1.401V16.01z" fill="#0F0F14"/>
            <g opacity=".8" filter="url(#rw6agxkncc)">
                <circle cx="258.662" cy="191.918" r="40.025" fill="#9786FF"/>
                <circle cx="258.662" cy="191.918" r="40.025" fill="url(#cbzd7z3z9d)" fill-opacity=".8"/>
                <g clip-path="url(#dg7nqycnpe)" data-figma-skip-parse="true">
                    <foreignObject x="-952.598" y="-952.598" width="1905.2" height="1905.2" transform="matrix(.04002 .05242 -.03751 .07323 258.662 195.169)">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,#fefefe 0deg,rgba(255,255,255,0) 66.6644deg,#fff 143.541deg,rgba(255,255,255,0) 199.792deg,#fff 272.918deg,rgba(255,255,255,0) 349.795deg,#fefefe 360deg);height:100%;width:100%;opacity:.5"/>
                    </foreignObject>
                </g>
                <circle cx="258.662" cy="191.918" r="40.025" data-figma-gradient-fill="{&quot;type&quot;:&quot;GRADIENT_ANGULAR&quot;,&quot;stops&quot;:[{&quot;color&quot;:{&quot;r&quot;:0.99964576959609985,&quot;g&quot;:0.99964576959609985,&quot;b&quot;:0.99964576959609985,&quot;a&quot;:1.0},&quot;position&quot;:0.0},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:0.0},&quot;position&quot;:0.18517892062664032},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:1.0},&quot;position&quot;:0.39872443675994873},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:0.0},&quot;position&quot;:0.55497723817825317},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:1.0},&quot;position&quot;:0.75810593366622925},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:0.0},&quot;position&quot;:0.97165143489837646}],&quot;stopsVar&quot;:[],&quot;transform&quot;:{&quot;m00&quot;:80.049301147460938,&quot;m01&quot;:-75.026176452636719,&quot;m02&quot;:256.15020751953125,&quot;m10&quot;:104.83615112304688,&quot;m11&quot;:146.46975708007812,&quot;m12&quot;:69.515800476074219},&quot;opacity&quot;:0.50,&quot;blendMode&quot;:&quot;NORMAL&quot;,&quot;visible&quot;:true}"/>
            </g>
            <g filter="url(#nnozo58r7f)" opacity=".56">
                <ellipse cx="64.44" cy="-113.57" rx="116.071" ry="129.58" fill="#9786FF"/>
                <ellipse cx="64.44" cy="-113.57" rx="116.071" ry="129.58" fill="url(#y5r0rk85ag)" fill-opacity=".8"/>
                <g clip-path="url(#iq00kwexjh)" data-figma-skip-parse="true">
                    <foreignObject x="-937.498" y="-937.498" width="1875" height="1875" transform="matrix(.11607 .1697 -.10879 .2371 64.44 -103.046)">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,#fefefe 0deg,rgba(255,255,255,0) 66.6644deg,#fff 143.541deg,rgba(255,255,255,0) 199.792deg,#fff 272.918deg,rgba(255,255,255,0) 349.795deg,#fefefe 360deg);height:100%;width:100%;opacity:.5"/>
                    </foreignObject>
                </g>
                <ellipse cx="64.44" cy="-113.57" rx="116.071" ry="129.58" data-figma-gradient-fill="{&quot;type&quot;:&quot;GRADIENT_ANGULAR&quot;,&quot;stops&quot;:[{&quot;color&quot;:{&quot;r&quot;:0.99964576959609985,&quot;g&quot;:0.99964576959609985,&quot;b&quot;:0.99964576959609985,&quot;a&quot;:1.0},&quot;position&quot;:0.0},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:0.0},&quot;position&quot;:0.18517892062664032},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:1.0},&quot;position&quot;:0.39872443675994873},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:0.0},&quot;position&quot;:0.55497723817825317},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:1.0},&quot;position&quot;:0.75810593366622925},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:0.0},&quot;position&quot;:0.97165143489837646}],&quot;stopsVar&quot;:[],&quot;transform&quot;:{&quot;m00&quot;:232.14297485351562,&quot;m01&quot;:-217.57589721679688,&quot;m02&quot;:57.156063079833984,&quot;m10&quot;:339.40704345703125,&quot;m11&quot;:474.195800781250,&quot;m12&quot;:-509.84713745117188},&quot;opacity&quot;:0.50,&quot;blendMode&quot;:&quot;NORMAL&quot;,&quot;visible&quot;:true}"/>
            </g>
        </g>
        <path d="M17.41.5H307.59c8.566 0 15.51 6.944 15.51 15.51v137.585H1.901V16.01C1.901 7.444 8.845.5 17.411.5z" stroke="url(#n3p8hi427i)" stroke-opacity=".4"/>
        <path d="M17.41.5H307.59c8.566 0 15.51 6.944 15.51 15.51v137.585H1.901V16.01C1.901 7.444 8.845.5 17.411.5z" stroke="url(#5545lcpyrj)" stroke-opacity=".27"/>
    </g>
    <defs>
        <linearGradient id="cbzd7z3z9d" x1="258.797" y1="176.164" x2="313.13" y2="205.829" gradientUnits="userSpaceOnUse">
            <stop stop-color="#E7ECEF"/>
            <stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
        </linearGradient>
        <linearGradient id="y5r0rk85ag" x1="64.408" y1="-165.026" x2="229.465" y2="-84.302" gradientUnits="userSpaceOnUse">
            <stop stop-color="#E7ECEF"/>
            <stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
        </linearGradient>
        <linearGradient id="5545lcpyrj" x1="-13.098" y1="436.815" x2="226.049" y2="297.847" gradientUnits="userSpaceOnUse">
            <stop stop-color="#fff"/>
            <stop offset="1" stop-color="#0F0F14"/>
        </linearGradient>
        <clipPath id="dg7nqycnpe">
            <circle cx="258.662" cy="191.918" r="40.025"/>
        </clipPath>
        <clipPath id="iq00kwexjh">
            <ellipse cx="64.44" cy="-113.57" rx="116.071" ry="129.58"/>
        </clipPath>
        <clipPath id="mxidnd5rzb">
            <path d="M1.4 16.01C1.4 7.168 8.57 0 17.41 0H307.59c8.842 0 16.01 7.168 16.01 16.01v138.085H1.401V16.01z" fill="#fff"/>
        </clipPath>
        <filter id="cdcp5t1eza" x="0" y="0" width="325" height="160.999" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feMorphology radius="3" in="SourceAlpha" result="effect1_dropShadow_1596_3430"/>
            <feOffset dy="5.5"/>
            <feGaussianBlur stdDeviation="2.2"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.91 0"/>
            <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_1596_3430"/>
            <feBlend in="SourceGraphic" in2="effect1_dropShadow_1596_3430" result="shape"/>
        </filter>
        <filter id="rw6agxkncc" x="158.6" y="91.856" width="200.123" height="200.123" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feGaussianBlur stdDeviation="30" result="effect1_foregroundBlur_1596_3430"/>
        </filter>
        <filter id="nnozo58r7f" x="-151.694" y="-343.211" width="432.266" height="459.283" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_1596_3430"/>
        </filter>
        <radialGradient id="n3p8hi427i" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="rotate(131.981 51.086 21.198) scale(49.7748 62.4313)">
            <stop stop-color="#fff"/>
            <stop offset="1" stop-color="#fff" stop-opacity="0"/>
        </radialGradient>
    </defs>
</svg>
