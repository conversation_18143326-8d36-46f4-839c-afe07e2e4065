import { useMutation, useQuery } from '@tanstack/react-query';
import axios from 'axios';
import useDialogStore from '../component/DialogPopup/DialogStore';
import { commomKeys } from '@bryzos/giss-ui-library';

const useGetChats = () => {
  const { showCommonDialog, resetDialogStore } = useDialogStore();
  return useMutation( async () => {
    try {
      let url = `${import.meta.env.VITE_API_CHAT_SERVICE}/get-chats`;

      const response = axios.get(url);
      const responseData = await response;

      if (responseData.data && responseData.data?.data) {
        return responseData.data.data;
      } else {
        return null;
      }
    } catch (error: any) {
        showCommonDialog(null, commomKeys.errorContent, null, resetDialogStore, [{name: commomKeys.errorBtnTitle, action: resetDialogStore}])
    }
  });
};

export default useGetChats;
