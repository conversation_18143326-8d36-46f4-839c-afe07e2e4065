import type { ForgeConfig } from '@electron-forge/shared-types';
import { MakerSquirrel } from '@electron-forge/maker-squirrel';
import { MakerZIP } from '@electron-forge/maker-zip';
import { MakerDeb } from '@electron-forge/maker-deb';
import { MakerRpm } from '@electron-forge/maker-rpm';
import { WebpackPlugin } from '@electron-forge/plugin-webpack';
import { MakerDMG } from '@electron-forge/maker-dmg'
import { mainConfig } from './webpack.main.config';
import { preloadConfig } from './webpack.preload.config';
import fs from 'fs';
import path from 'path';

/**
 * Load client configuration dynamically
 */
function loadClientConfig() {
  const clientId = process.env.CLIENT_ID || 'bryzos';
  console.log(`clientId: ${clientId}`);
  const clientConfigPath = path.join(__dirname, 'clients', clientId, 'client.config.json');
  
  if (!fs.existsSync(clientConfigPath)) {
    console.warn(`Client config not found for: ${clientId}, using default values`);
    return null;
  }
  console.log(`clientConfigPath: ${clientConfigPath}`);
  
  const configContent = fs.readFileSync(clientConfigPath, 'utf-8');
  return JSON.parse(configContent);
}

const clientConfig = loadClientConfig();

console.log(`clientConfig: ${JSON.stringify(clientConfig)}`);
// Default configuration (fallback to Bryzos default)
const appName = clientConfig?.appName || 'BRYZOS';
const iconBase = clientConfig?.icon?.base || 'GISS2_0';
const iconIcns = clientConfig?.icon?.icns || 'GISS2_0.icns';
const loadingGif = clientConfig?.squirrel?.loadingGif || 'GISS2_0.png';
const authors = clientConfig?.authors || 'Bryzos LLC';
const description = clientConfig?.description || 'Bryzos Gone In 60 Seconds (GISS) 2.0';
const iconUrl = clientConfig?.squirrel?.iconUrl || 'https://widget-ui-wip.s3.amazonaws.com/GISS2_0.ico';
const dmgBackground = clientConfig?.dmg?.background || 'dmgBackground.png';
const dmgIconSize = clientConfig?.dmg?.iconSize || 120;
// Setup exe name for Windows (defaults to appName if not specified)
const setupExeName = appName;

const config: ForgeConfig = {
  packagerConfig: {
    name: appName,
    icon: `./public/${iconBase}`,
    ignore:
      /^\/((?!package\.json|\.webpack).)*$|^\/((?!node_modules).)*[\\/]node_modules[\\/].*$/i,
    osxSign: {},
    osxNotarize: {
      tool: 'notarytool',
      appleId: process.env.APPLE_ID ?? '',
      appleIdPassword: process.env.APPLE_ID_PASSWORD ?? '',
      teamId: process.env.APPLE_TEAM_ID ?? '',
    }
  },
  rebuildConfig: {},
  makers: [
    new MakerSquirrel({
      name: setupExeName,
      loadingGif: `./public/${loadingGif}`,
      authors: authors,
      description: description,
      iconUrl: iconUrl,
    }),
    new MakerZIP({}),
    new MakerRpm({}),
    new MakerDeb({}),
    new MakerDMG({
      icon: `./public/${iconIcns}`,
      format: 'ULFO',
      background: `./public/${dmgBackground}`,
      iconSize: dmgIconSize,
      contents: (arg) => {

        return [
          { x: 460, y: 182, type: 'link', path: '/Applications', name: '' },
          // @ts-ignore
          { x: 185, y: 182, type: 'file', path: arg.appPath, name: '' }
        ]
      },
      additionalDMGOptions: {
        "background-color": '#fff',
        window: {
          size: {
            width: 600,
            height: 414,
          }
        }
      }
    })
  ],
  plugins: [
    new WebpackPlugin({
      mainConfig,
      renderer: {
        config: preloadConfig,
        entryPoints: [
          {
            name: 'main_window',
            preload: {
              js: './src/preload/index.ts',
            },
          },
        ],
      },
    }),
  ],
};

export default config;
