import { useMutation } from "@tanstack/react-query";
import axios from "axios";

const usePutUpdateUserSubscription = () => {
  return useMutation(async (data: any) => {
    try {
      const response = await axios.put(
        `${import.meta.env.VITE_API_SUBSCRIPTION_SERVICE}/subscription-service-api/api/v1/subscriptions`,
        data
      );

      if (response.data?.data) {
        if (
          typeof response.data.data === "object" &&
          "error_message" in response.data.data
        ) {
          throw new Error(response.data.data.error_message);
        } else {
          return response.data.data;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default usePutUpdateUserSubscription;
