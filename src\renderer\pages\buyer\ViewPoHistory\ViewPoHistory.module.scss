.savedBomPreviewContainer {
    width: 800px;
    height: 880px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: url(../../../assets/images/AppBG.svg) no-repeat;

    .noOrderDetailsContainer {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 300px;
        height: 880px;
        gap: 12px;
        font-family: Syncopate;
        font-size: 16px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: 1.12px;
        text-align: center;
        color: #fff;
    }

    .viewPoHistoryDetailsBg {
        width: 100%;
        display: flex;
        column-gap: 16px;
        padding: 16px 24px;

        .viewPoHistDetailsLeftSection {
            padding: 12px 75px 12px 16px;
            border-radius: 10px;
            background-color: rgba(255, 255, 255, 0.04);
            display: flex;
            flex-direction: column;
            row-gap: 15.5px;
            width: 100%;

            .detailRow {
                font-family: Inter;
                font-size: 15px;
                font-weight: 600;
                font-stretch: normal;
                font-style: normal;
                line-height: normal;
                letter-spacing: 0.6px;
                text-align: left;
                display: flex;
                column-gap: 12px;

                span {
                    &:first-child {
                        color: #9b9eac;
                        flex: 0 197px;
                    }

                    &:nth-child(2) {
                        color: #fff;
                        font-weight: normal;
                        flex: 1;
                    }
                }
            }
        }

        .orderDocumentsMain {
            width: 104px;
            height: 139px;
            padding: 8px 10px 5px;
            border-radius: 10px;
            background-image: linear-gradient(to right, #0f0f14 -11%, rgba(57, 62, 71, 0.67) 32%, rgba(57, 62, 71, 0.72) 67%, #0f0f14 123%);

            .title {
                font-family: Syncopate;
                font-size: 10px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: normal;
                letter-spacing: 0.4px;
                text-align: center;
                color: #fff;
                display: flex;
                margin-bottom: 4px;
            }

            .orderBtn {
                display: flex;
                flex-direction: column;
                align-items: center;
                row-gap: 2px;

                button {
                    width: 50px;
                    height: 24px;
                    padding: 7px 6px;
                    border-radius: 5px;
                    background-color: rgba(0, 0, 0, 0.25);
                    font-family: Syncopate;
                    font-size: 10px;
                    font-weight: normal;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: normal;
                    letter-spacing: -0.5px;
                    text-align: center;
                    color: #8c8b99;
                }
            }
        }
    }

}

.createPOContainer {
    // overflow-y: auto;
    width: 100%;
    height: 100%;
    max-height: calc(100% - 173px);
    position: relative;
    scroll-behavior: smooth;
    box-shadow: 0 -16px 15.1px -11px rgba(0, 0, 0, 0.6);
    border-top: 1px solid rgba(255, 255, 255, 0.2980392157);

    .addPoLineTableContainer {
        height: calc(100% - 40px);
        overflow: auto;

        // &.addPoLineTableContainerMinHeight {
        //     height: calc(100% - 174px);
        // }

        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
    }



}

.createPOContainer {
    position: relative;
    height: 100%;
    
}

.addPoLineTable {
    position: relative;
    max-height: calc(100% - 40px);
    overflow-y: scroll;
    z-index: 3;
    width: 100%;
    width: 100%;
    border-spacing: 1px;
    border-collapse: collapse;

    &::-webkit-scrollbar {
        width: 8px;
        height: 6px;
    }

    thead {
        width: 100%;
        position: sticky;
        top: 0px;
         background-image:linear-gradient(102deg, #0f0f14 -8%, #393e47 275%);
        z-index: 100;
    }

    tr {
        th {
            font-family: Syncopate;
            font-size: 16px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: normal;
            text-align: left;
            color: #9b9eac;
            padding-top: 24px;
            padding-bottom: 16px;

            &:nth-child(1) {
                padding-left: 40px;
            }

            &:nth-child(2) {
                width: 292px;
            }

            &:nth-child(3) {
                width: 135px;
                padding-left: 0px;
                text-align: center;
            }

            &:nth-child(4) {
                width: 130px;
                text-align: center;
            }

            &:nth-child(5) {
                width: 140px;
                text-align: right;
                padding-right: 24px;
            }
        }

        td {
            vertical-align: top;
            padding-bottom: 32px;

            &:nth-child(1) {
                padding-left: 36px;
                padding-right: 18px;
            }

            &:nth-child(2) {
                width: 292px;
            }

            &:nth-child(3) {
                width: 135px;
                padding-left: 0px;
                text-align: center;
            }

            &:nth-child(4) {
                width: 130px;
                text-align: center;
            }

            &:nth-child(5) {
                width: 140px;
                text-align: right;
                padding-right: 24px;
            }


        }
    }
}

.addPoLineTable {
    will-change: transform;
}

.poDescription {
    background-color: transparent;
    border: 0;
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.43;
    letter-spacing: 1.4px;
    text-align: left;
    color: #fff;
}

.priceAndUnit {
    font-family: Inter;
    font-size: 14px;
    text-align: right;
    color: #fff;
    display: flex;
    gap: 8px;
    justify-content: right;

}

.extendedValue {
    font-family: Inter;
    font-size: 14px;
    text-align: right;
    color: #fff;
    display: flex;
    justify-content: right;
}

.prodId {
    display: flex;
    align-items: center;
    position: relative;

    .lineNumberContainer {
        display: flex;
        flex-direction: column;
        gap: 5px;
        align-items: center;
        justify-content: center;
        text-align: center;

        .hiddenCheckbox {
            display: none;
        }

        .customNumberToggle {
            background-image: url(../../assets/images/Product-Id-BG.svg);
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            border-radius: 5px;
            border: solid 0.5px rgba(255, 255, 255, 0.16);
            width: 30px;
            height: 30px;
            font-family: Syncopate;
            font-size: 16px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: 1.12px;
            text-align: left;
            color: #9b9eac;
            display: flex;
            justify-content: center;
            align-items: center;
            padding-top: 3px;
            cursor: pointer;
            transition: all 0.2s ease;

            &.active {
                background-image: url(../../assets/images/Usa-Only-Without-Text.svg);
                background-size: cover;
                background-position: center;
                background-repeat: no-repeat;
                color: #0f0f14;
                border: none;
                border-radius: 2px;
            }
        }

        .usaOnlyText {
            font-family: Syncopate;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.5);
            font-weight: normal;

            &.visibilityHidden {
                visibility: hidden;
            }
        }
    }
}

.skippedLineContent {
    display: flex;
    gap: 16px;
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: 0.98px;
    color: #fff;

    .skippedLineActionBtns {
        display: flex;
        gap: 8px;

        .btn {
            width: 114px;
            height: 20px;
            border-radius: 100px;
            background-color: rgba(255, 255, 255, 0.04);
            font-family: Inter;
            font-size: 12px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.5;
            letter-spacing: -0.13px;
            text-align: center;
            color: rgba(255, 255, 255, 0.6);
        }
    }
}

.footerMain {
    width: 100%;
    height: 40px;
    padding: 0px 24px 0px 40px;
    background-color: #000;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .cancelBtn {
        font-family: Syncopate;
        font-size: 10px;
        font-weight: normal;
        line-height: 1.3;
        letter-spacing: 0.4px;
        text-align: center;
        color: rgba(255, 255, 255, 0.8);
    }

    .saveOrderBtn {
        font-family: Syncopate;
        font-size: 12px;
        font-weight: bold;
        line-height: 1.3;
        letter-spacing: -0.48px;
        text-align: center;
        color: #fff;
    }
}