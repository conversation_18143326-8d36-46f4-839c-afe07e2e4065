.container {
  display: flex; // Use flex instead of inline-flex for full width
  width: 100%;
  margin-bottom: 10px;
  justify-content: space-between;
  box-sizing: border-box; // Ensure proper width calculation
  gap: 1%; // Add responsive gap between gear buttons
}

.gearButton {
  flex: 1; // Allow buttons to grow and fill available space
  min-width: 38px; // Maintain minimum width
  height: 38px;
  padding: 2px 10px;
  background-color: rgba(255, 255, 255, 0.04);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  font-family: Inter;
  font-size: 28px;
  border-radius: 2px;
  font-weight: 500;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-align: center; // Center align for better appearance
  color: rgba(255, 255, 255, 0.04);
  box-sizing: border-box; // Ensure proper sizing
  &:nth-child(1){
    border-radius: 0px 0px 0px 13px;
  }
  &:last-child{
    border-radius: 0px 0px 13px 0px;
  }

  &.selected {
    background-color: #32ff6c;
    color: #191a20;
  }
}