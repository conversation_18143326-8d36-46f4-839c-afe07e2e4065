<svg width="41" height="41" viewBox="0 0 41 41" fill="none" xmlns="http://www.w3.org/2000/svg">
    <foreignObject x="-8.694" y="-8.694" width="58.386" height="58.387">
        <div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(4.33px);clip-path:url(#66dn5fzxxa);height:100%;width:100%"/>
    </foreignObject>
    <g filter="url(#gotonueinb)" data-figma-bg-blur-radius="8.665">
        <circle cx="20.5" cy="20.5" r="20.065" fill="#000" fill-opacity=".25"/>
        <circle cx="20.5" cy="20.5" r="20.282" stroke="url(#r94uoan2tc)" stroke-opacity=".2" stroke-width=".433"/>
    </g>
    <path d="M14.848 13.98a1.305 1.305 0 0 1 1.955-1.13l11.3 6.518c.87.502.87 1.758 0 2.26l-11.3 6.518a1.304 1.304 0 0 1-1.955-1.13V13.98z" fill="#fff"/>
    <defs>
        <linearGradient id="r94uoan2tc" x1="35.873" y1="3.187" x2="20.501" y2="40.566" gradientUnits="userSpaceOnUse">
            <stop stop-color="#fff"/>
            <stop offset="1" stop-color="#fff" stop-opacity="0"/>
        </linearGradient>
        <clipPath id="66dn5fzxxa" transform="translate(8.694 8.694)">
            <circle cx="20.5" cy="20.5" r="20.065"/>
        </clipPath>
        <filter id="gotonueinb" x="-8.694" y="-8.694" width="58.386" height="58.387" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feMorphology radius=".633" operator="dilate" in="SourceAlpha" result="effect1_innerShadow_911_30420"/>
            <feOffset dx="-.316" dy=".316"/>
            <feGaussianBlur stdDeviation=".158"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
            <feBlend in2="shape" result="effect1_innerShadow_911_30420"/>
        </filter>
    </defs>
</svg>
