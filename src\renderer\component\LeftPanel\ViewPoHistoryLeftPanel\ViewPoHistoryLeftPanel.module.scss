.ViewPoHistoryLeftPanelContainer {
    display: flex;
    flex-direction: column;
    gap: 16px;
    height: 100%;

    .actionBtns {
        display: flex;

        .actionBtn {
            font-family: Inter;
            font-size: 12px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.4;
            letter-spacing: 0.48px;
            text-align: left;
            color: rgba(255, 255, 255, 0.3);
            width: 93px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: inset -2px 0 0 0 #000, 0 4px 4px 0 rgba(0, 0, 0, 0.25);
            background-color: rgba(255, 255, 255, 0.04);
            cursor: pointer;

        }

        .actionBtn:nth-child(2),
        .actionBtn:last-child {
            border-left: solid 1px rgba(255, 255, 255, 0.27);
        }

        .actionBtn:nth-child(1) {
            border-radius: 10px 0 0 10px;
        }

        .actionBtn:nth-child(3) {
            border-radius: 0 10px 10px 0;
        }

        .active {
            box-shadow: inset -2px 0 0 0 #000, 0 4px 4px 0 rgba(0, 0, 0, 0.67);
        }

        .actionBtn:focus {
            background-color: red($color: #000000);
            box-shadow: inset -2px 0 0 0 #000, 0 4px 4px 0 rgba(0, 0, 0, 0.67);
        }

    }

    .savedBomList {
        overflow-y: auto;
        height: 100%;

        &::-webkit-scrollbar {
            width: 5px;
            height: 6px;
        }

        .disabled.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            z-index: 999
        }

        .savedBomItem {
            width: 280px;
            min-height: 107px;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            padding: 12px;
            margin-bottom: 4px;
            gap: 4px;
            border-radius: 10px;

            &:hover {
                background-image: linear-gradient(342deg, #eaecf0 -10%, #9b9eac 100%);

                .savedBomTitle {
                    .savedBomJobTitle {
                        color: #0f0f14;
                    }

                    .savedBomTotalPrice {
                        color: #131319;
                    }

                    .pendingReview {
                        color: #131319;
                        opacity: 1;
                    }
                }

                .savedBomAddress {
                    .savedBomDetails {
                        color: #0f0f14;
                        font-weight: 300;
                    }

                    .savedBomPoNumber {
                        color: #0f0f14;
                    }

                     .savedBomFulfilledBy{
                        color: #0f0f14;
                    }

                    .pendingReviewStatus {
                        color: #131319;
                    }
                }
            }

            &:focus {
                background-image: linear-gradient(342deg, #eaecf0 -10%, #9b9eac 100%);

                .savedBomTitle {
                    .savedBomJobTitle {
                        color: #0f0f14;
                    }

                    .savedBomTotalPrice {
                        color: #131319;
                    }

                    .pendingReview {
                        color: #131319;
                        opacity: 1;
                    }
                }

                .savedBomAddress {
                    .savedBomDetails {
                        color: #0f0f14;
                        font-weight: 300;
                    }

                    .savedBomPoNumber {
                        color: #0f0f14;
                    }

                     .savedBomFulfilledBy{
                        color: #0f0f14;
                    }


                    .pendingReviewStatus {
                        color: #131319;
                    }
                }
            }

            &.active {
                background-image: linear-gradient(342deg, #eaecf0 -10%, #9b9eac 100%);

                .savedBomTitle {
                    .savedBomJobTitle {
                        color: #0f0f14;
                    }

                    .savedBomTotalPrice {
                        color: #131319;
                    }
                    

                    .pendingReview {
                        color: #131319;
                        opacity: 1;
                    }
                }

                .savedBomAddress {
                    .savedBomDetails {
                        color: #0f0f14;
                        font-weight: 300;
                    }

                    .savedBomPoNumber {
                        color: #0f0f14;
                    }

                     .savedBomFulfilledBy{
                        color: #0f0f14;
                    }


                    .pendingReviewStatus {
                        color: #131319;
                    }
                }
            }

            .savedBomTitle {
                display: flex;
                justify-content: space-between;

                .savedBomJobTitle {
                    font-family: Syncopate;
                    font-size: 14px;
                    font-weight: bold;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.4;
                    letter-spacing: 0.98px;
                    text-align: left;
                    color: #fff;
                    text-transform: uppercase;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    overflow: hidden;
                    margin-right: 10px;
                }

                .savedBomTotalPrice {
                    font-family: Inter;
                    font-size: 14px;
                    font-weight: 600;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.4;
                    letter-spacing: 0.56px;
                    text-align: right;
                    color: #fff;
                }

                .pendingReview {
                    opacity: 0.4;
                    font-family: Inter;
                    font-size: 14px;
                    font-weight: 500;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.4;
                    letter-spacing: 0.56px;
                    color: #fff;
                }
            }

            .savedBomAddress {
                display: flex;
                flex-direction: column;
                gap: 4px;

                .savedBomGrid {
                    display: flex;
                    align-items: flex-end;
                    column-gap: 10px;
                    .colLeft{
                        flex: 1;
                    }
                }


                .savedBomPoNumber {
                    font-family: Syncopate;
                    font-size: 14px;
                    font-weight: normal;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.4;
                    letter-spacing: 0.98px;
                    text-align: left;
                    color: #fff;

                }

                .savedBomFulfilledBy {
                    font-family: Inter;
                    font-size: 14px;
                    font-weight: 200;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: normal;
                    letter-spacing: 0.56px;
                    text-align: left;
                    color: #fff;
                }

                .savedBomDetails {
                    font-family: Inter;
                    font-size: 14px;
                    font-weight: 200;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: normal;
                    letter-spacing: 0.56px;
                    text-align: left;
                    color: #fff;
                    // word-break: break-word;
                }

                .pendingReviewStatus {
                    font-family: Inter;
                    font-size: 10px;
                    font-weight: normal;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: normal;
                    letter-spacing: 0.4px;
                    text-align: left;
                    color: rgba(255, 255, 255, 0.4);
                    text-transform: capitalize;
                    white-space: nowrap;
                }
            }
        }
    }

    .loading {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.noDataContainer {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;

    .noData {
        display: flex;
        align-items: center;
        justify-content: center;

        span {
            opacity: 0.5;
            font-family: Syncopate;
            font-size: 16px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.75;
            letter-spacing: 1.12px;
            text-align: center;
            color: #fff;
            padding-top: 94px;
        }
    }

    button {
        width: 100%;
        background: url(../../../assets/images/place-order-hover.svg) no-repeat;
        height: 60px;
        position: absolute;
        bottom: 0;
        left: -4px;
    }
}

.savedBomAddressDetails {
    display: flex;
    justify-content: space-between;
}