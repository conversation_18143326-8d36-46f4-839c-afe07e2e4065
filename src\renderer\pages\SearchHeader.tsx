import React from "react";
import styles from './search/home.module.scss'
import ProductSearch from "./search/headerSection/productSearch";
import Header from "./Header/Header";
import { useLocation } from "react-router-dom";
import { routes } from "../common";
import clsx from "clsx";
import SellerSearch from "./seller/SellerSearch/sellerSearch";

const SearchHeader = ({isMacDevice, handleDoubleClick}) => {
  const location = useLocation();

  return (
    <div className={styles.topSection}>
      <Header isMacDevice={isMacDevice} handleDoubleClick={handleDoubleClick} />
      {/* {
        location.pathname === routes.homePage && <ProductSearch />
      } */}
      {/* {
        location.pathname === routes.orderPage && <SellerSearch />
      } */}
      <div className='bgEllips'></div>
      <div className='bgEllips1'></div>
    </div>
  )
};

export default SearchHeader;
