import React, { useState, useEffect, useRef } from 'react';
import { Popover } from '@mui/material';
import clsx from 'clsx';
import { formatToTwoDecimalPlaces } from '@bryzos/giss-ui-library';
import styles from './PriceHistoryDropdown.module.scss';
import { ReactComponent as RedArrow } from '../../assets/images/redArrow.svg';
import { ReactComponent as GreenArrow } from '../../assets/images/geenArrow.svg';
import { CustomMenu } from 'src/renderer/pages/buyer/CustomMenu';

interface PriceHistoryItem {
  created_date?: string;
  qty?: string | number;
  qty_unit?: string;
  buyer_price_per_unit?: string | number;
  price_unit?: string;
  buyer_line_total?: string | number;
}

interface PriceChangeIndicator {
  show: boolean;
  direction: React.ReactNode;
  color: string;
  changeAmount: number;
}

interface PriceHistoryDropdownProps {
  lineHistory: string | null | undefined;
  currentPrice: number;
  dollerPerUmFormatter: (value: string | number) => string;
  index: number;
  watch: any;
  register: any;
  control: any;
  setValue: any;
  getValues: any;
  pricePerUnitChangeHandler: any;
  setIsCreatePoDirty: any;
  saveUserActivity: any;
  actualIndex: number;
  priceUnits: any;
  isStateZipValChange: any;
  setIsOrderLineChanges: any;
}

const PriceHistoryDropdown: React.FC<PriceHistoryDropdownProps> = ({
  lineHistory,
  currentPrice,
  dollerPerUmFormatter,
  index,
  watch,
  register,
  control,
  setValue,
  getValues,
  pricePerUnitChangeHandler,
  setIsCreatePoDirty,
  saveUserActivity,
  actualIndex,
  priceUnits,
  isStateZipValChange,
  setIsOrderLineChanges
}) => {
  // Internal state management
  const [showPriceHistory, setShowPriceHistory] = useState(false);
  const [priceHistoryData, setPriceHistoryData] = useState<PriceHistoryItem[]>([]);
  const [priceChangeIndicator, setPriceChangeIndicator] = useState<PriceChangeIndicator>({
    show: false,
    direction: null,
    color: '',
    changeAmount: 0
  });
  const [originalPrice, setOriginalPrice] = useState<number | null>(null);
  const anchorRef = useRef<HTMLDivElement>(null);

  // Helper function to get price value
  const getPriceValue = (historyItem: PriceHistoryItem): number => {
    if (typeof historyItem.buyer_price_per_unit === 'object' && historyItem.price_unit) {
      const priceUnit = historyItem.price_unit.toLowerCase();
      return typeof historyItem.buyer_price_per_unit === 'object' && priceUnit in historyItem.buyer_price_per_unit
        ? parseFloat(String(historyItem.buyer_price_per_unit[priceUnit])) || 0
        : 0;
    }
    return parseFloat(String(historyItem.buyer_price_per_unit)) || 0;
  };

  // Helper function to get price value for a specific unit
  const getPriceValueForUnit = (historyItem: PriceHistoryItem, targetUnit: string): number => {
    if (typeof historyItem.buyer_price_per_unit === 'object' && targetUnit) {
      const priceUnit = targetUnit.toLowerCase();
      return typeof historyItem.buyer_price_per_unit === 'object' && priceUnit in historyItem.buyer_price_per_unit
        ? parseFloat(String(historyItem.buyer_price_per_unit[priceUnit])) || 0
        : 0;
    }
    return parseFloat(String(historyItem.buyer_price_per_unit)) || 0;
  };

  // Helper function to get price color based on comparison with original price for current unit
  const getPriceColor = (historyItem: PriceHistoryItem, historyIndex: number): string => {
    if (historyIndex === 0 || originalPrice === null) {
      return ''; // No color for original price
    }
    
    const currentPriceUnit = watch(`cart_items.${index}.price_unit`)?.toLowerCase();
    const currentPriceValue = getPriceValueForUnit(historyItem, currentPriceUnit);
    const originalPriceForCurrentUnit = getPriceValueForUnit(priceHistoryData[0], currentPriceUnit);
    
    if (formatToTwoDecimalPlaces(String(currentPriceValue)) > formatToTwoDecimalPlaces(String(originalPriceForCurrentUnit))) {
      return '#ff6b6b'; // Red for higher price
    } else if (formatToTwoDecimalPlaces(String(currentPriceValue)) < formatToTwoDecimalPlaces(String(originalPriceForCurrentUnit))) {
      return '#51cf66'; // Green for lower price
    }
    return ''; // No color if same price
  };

  // Parse price history from line_history
  useEffect(() => {
    if (lineHistory) {
      try {
        const historyData = JSON.parse(lineHistory);

        if (Array.isArray(historyData)) {
          setPriceHistoryData(historyData);
          
          // Set original price (first item in history)
          if (historyData.length > 0) {
            const originalPriceItem = historyData[0];
            const originalPriceValue = getPriceValue(originalPriceItem);
            setOriginalPrice(originalPriceValue);
          }
          
          // Calculate price change indicator
          if (historyData.length > 0 && currentPrice) {
            // Get the original price (first item in history)
            const originalPriceItem = historyData[0];
            
            if (originalPriceItem && originalPriceItem.buyer_price_per_unit) {
              const currentPriceNum = parseFloat(String(currentPrice));
              const currentPriceUnit = watch(`cart_items.${index}.price_unit`)?.toLowerCase();
              const originalPriceForCurrentUnit = getPriceValueForUnit(originalPriceItem, currentPriceUnit);
              
              if (!isNaN(currentPriceNum) && !isNaN(originalPriceForCurrentUnit) && currentPriceNum !== originalPriceForCurrentUnit) {
                const priceDiff = currentPriceNum - originalPriceForCurrentUnit;
                setPriceChangeIndicator({
                  show: true,
                  direction: priceDiff > 0 ? <RedArrow/> : <GreenArrow/>,
                  color: priceDiff > 0 ? '#ff6b6b' : '#51cf66',
                  changeAmount: Math.abs(priceDiff)
                });
              } else {
                setPriceChangeIndicator({ show: false, direction: '', color: '', changeAmount: 0 });
              }
            } else {
              setPriceChangeIndicator({ show: false, direction: '', color: '', changeAmount: 0 });
            }
          } else {
            setPriceChangeIndicator({ show: false, direction: '', color: '', changeAmount: 0 });
          }
        }
      } catch (error) {
        console.error('Error parsing line_history:', error);
        setPriceHistoryData([]);
        setPriceChangeIndicator({ show: false, direction: '', color: '', changeAmount: 0 });
        setOriginalPrice(null);
      }
    } else {
      setPriceHistoryData([]);
      setPriceChangeIndicator({ show: false, direction: '', color: '', changeAmount: 0 });
      setOriginalPrice(null);
    }
  }, [lineHistory, currentPrice]);

  return (
    <>
      <div className={styles.pricePerUnitContainer}>
        {/* Price Change Indicator */}
        <div
          ref={anchorRef}
          className={clsx(styles.priceChangeIndicator, showPriceHistory && styles.priceChangeIndicatorShow)}
          onMouseEnter={() => {
            if (priceHistoryData.length > 1) {
              setShowPriceHistory(true);
            }
          }}
          onMouseLeave={() => {
            setShowPriceHistory(false);
          }}
          onClick={() => {
            if (priceHistoryData.length > 1) {
              setShowPriceHistory(!showPriceHistory);
            }
          }}
        >
      {/* Price Change Indicator */}
      {(priceChangeIndicator.show && priceHistoryData.length > 1) && (
          <span
            className={styles.priceChangeArrow}
            style={{ color: priceChangeIndicator.color }}
          >
            {priceChangeIndicator.direction}
          </span>
        )}

          <span>{(currentPrice >= 0) && dollerPerUmFormatter(currentPrice)}</span>
          
      {(watch(`cart_items.${index}.price_unit`) && watch('isEdit') && !watch(`cart_items.${index}.line_cancel_date`) && !isStateZipValChange) ?
        <span className={styles.selectUom1}>
          <CustomMenu
            name={register(`cart_items.${index}.price_unit`).name}
            control={control}
            // disabled={apiCallInProgress}
            onChange={() => {
              pricePerUnitChangeHandler(index);
              setIsCreatePoDirty(true);
              saveUserActivity();
              if(setIsOrderLineChanges) {
                setIsOrderLineChanges(true);
              }
            }}
            items={
              getValues(`cart_items.${index}.price_um`)?.map((x: any) => ({ title: (x.toLowerCase() === priceUnits.pc) ? 'PC' : x, value: x.toLowerCase() })) ?? []
            }
            className={clsx(styles.uomDrodown, 'qtyUnitDropdown')}
            MenuProps={{
              classes: {
                paper: styles.selectUomPaper,
              },
              id: `price-unit-menu-${actualIndex}` // Add index to make ID unique
            }}
            onKeyDown={(e: any) => {
              if (e.key === 'Tab' && document.activeElement?.closest(`#price-unit-menu-${actualIndex}`)) { // Update selector to match new ID
                const value = document.activeElement.getAttribute('data-value');
                setValue(`cart_items.${index}.price_unit`, value);
                pricePerUnitChangeHandler(index);
                setIsCreatePoDirty(true);
                saveUserActivity();
                if(setIsOrderLineChanges) {
                  setIsOrderLineChanges(true);
                }
              }
            }}
          />
        </span>
        :
        <span className={styles.selectUom}>
          <span className={styles.selectUomValue}>
            {watch(`cart_items.${index}.price_unit`)}
          </span>
        </span>
      }
        </div>
      
        
        {/* Price History Popover */}
        <Popover
                  open={showPriceHistory && priceHistoryData.length > 0}

          anchorEl={anchorRef.current}
          onClose={() => setShowPriceHistory(false)}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'center',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'center',
          }}
          disableRestoreFocus
          sx={{
            pointerEvents: 'none',
          }}
          classes={{
            paper: styles.priceHistoryDropdown,
          }}
          slotProps={{
            paper: {
              onMouseEnter: () => setShowPriceHistory(true),
              onMouseLeave: () => setShowPriceHistory(false),
            },
          }}
        >
          <div className={styles.priceHistoryDropdownContent}>
            {priceHistoryData.map((historyItem, historyIndex) => {
              const priceValue = getPriceValue(historyItem);
              const priceColor = getPriceColor(historyItem, historyIndex);
              
              return (
                <div key={historyIndex} className={styles.priceHistoryRow}>
                  <div className={styles.priceHistoryLabel}>
                    {historyItem.created_date || 'Original'}
                  </div>
                  <div className={styles.priceHistoryValues}>
                    <span className={styles.priceHistoryQty}>
                      {String(historyItem.qty || '')}
                    </span>
                    <span className={styles.priceHistoryUnit}>
                      {String(historyItem.qty_unit || '')}
                    </span>
                    <span 
                      className={clsx(
                        styles.priceHistoryPrice,
                        // formatToTwoDecimalPlaces(String(priceValue)) === formatToTwoDecimalPlaces(String(currentPrice)) && styles.currentPrice
                      )}
                      style={{ color: priceColor }}
                    >
                      {formatToTwoDecimalPlaces(String(priceValue))}
                    </span>
                    <span className={styles.priceHistoryUnit}>
                      {String(historyItem.price_unit || '')}
                    </span>
                    <span className={styles.priceHistoryTotal}>
                      {formatToTwoDecimalPlaces(String(historyItem.buyer_line_total || 0))}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </Popover>
      </div>

    </>
  );
};

export default PriceHistoryDropdown; 