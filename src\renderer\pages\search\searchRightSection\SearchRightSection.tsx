// @ts-nocheck
import React, { useEffect, useState } from 'react'

import styles from '../home.module.scss'
import clsx from 'clsx'
import { navigatePage, newPriceFormatter, priceFormatter } from 'src/renderer/helper'
import { routes, shareEmailTypes } from 'src/renderer/common'
import { useRightWindowStore } from '../../RightWindow/RightWindowStore'
import ShareEmailWindow from 'src/renderer/component/ShareEmailWindow/ShareEmailWindow'
import SearchHistoryWindow from 'src/renderer/component/SearchHistoryWindow/SearchHistoryWindow'
import InputWrapper from 'src/renderer/component/InputWrapper'
import CustomTextField from 'src/renderer/component/CustomTextField'
import { ClickAwayListener } from '@mui/material'
import usePostSaveSearchProducts from '../../../hooks/usePostSaveSearchProducts'
import { useBuyerSettingStore, useSearchStore } from '@bryzos/giss-ui-library'

const SearchRightSection = () => {
    const { selectedProductsData, setSelectedProductsData, shortListedSearchProductsData, setShortListedSearchProductsData, focusSingleProduct, setFocusSingleProduct, filterShortListedSearchProductsData, setFilterShortListedSearchProductsData, saveFeedbackMap, setSaveFeedbackMap, selectedPriceUnit, searchZipCode, orderSizeSliderValue } = useSearchStore();
    const { setShareEmailWindowProps, setLoadComponent, setShareEmailType, shareEmailType, setIsPriceSearchHistory, isPriceSearchHistory  } = useRightWindowStore();
    const [showSavePricingInput, setShowSavePricingInput] = useState(false);
    const [savePricingName, setSavePricingName] = useState('');
    const [isSaving, setIsSaving] = useState(false);
    
    // Use the updated mutation hook
    const { mutateAsync: saveSearchProductsMutation } = usePostSaveSearchProducts();

    useEffect(() => {
        if(selectedProductsData.length === 0){
            setShowSavePricingInput(false);
        }
    }, [selectedProductsData]);

    const handleDeleteLines = () => {
        const spreadSelectedSearchProductsData = [...shortListedSearchProductsData];
        const filterShortListedSearchProductsData = spreadSelectedSearchProductsData.filter((productData) => {
            if (!focusSingleProduct[productData.id]) return true;
            setSaveFeedbackMap({ ...saveFeedbackMap, [productData.id]: undefined });
            return false;
        });
        setShortListedSearchProductsData(filterShortListedSearchProductsData);
        setFocusSingleProduct({});
        setSelectedProductsData([]);
    }

    const handleResetData = () => {
        setShortListedSearchProductsData([]);
        setFilterShortListedSearchProductsData([]);
        setSelectedProductsData([]);
        setSaveFeedbackMap({});
        setFocusSingleProduct({});
    }

    const handleSharePrice = () => {
        console.log('Share Pricing Data ', selectedProductsData)
        setShareEmailWindowProps({isSharePrice: true});
        setLoadComponent(<ShareEmailWindow />)
        setShareEmailType(shareEmailTypes.sharePrice);
    }

    const handleSavePricing = () => {
        setShowSavePricingInput(true);
    }

    const handleViewHistory = () => {
        if(!isPriceSearchHistory){
            const historyWindowComponent = <SearchHistoryWindow />;
            setLoadComponent(historyWindowComponent);
            setIsPriceSearchHistory(true);
        }else{
            setIsPriceSearchHistory(false);
            setLoadComponent(null);
        }
    }

    const handleSavePricingInput = async () => {
        if (isSaving || savePricingName.trim().length === 0) return;
        
        setIsSaving(true);
        try {
            // Map selected products to the required format

            const productsPayload = selectedProductsData.map((product) => ({
                product_id: product.id,
                price_unit: selectedPriceUnit.toLowerCase(),
                price: newPriceFormatter(product),
            }));
            const { buyerSetting } = useBuyerSettingStore.getState();
            const defaultZipCode = buyerSetting?.price_search_zip || '63105';
            const _zipcode = searchZipCode?.length === 5 ? searchZipCode :  defaultZipCode;
            
            // Create payload according to the required structure
            const payload = {
                data: {
                    title: savePricingName.trim(),
                    zipcode: _zipcode.trim(),
                    order_size: String(orderSizeSliderValue),
                    source: "search",
                    products: productsPayload
                }
            };
            
            // Call the save search products API using the updated hook
            const response = await saveSearchProductsMutation(payload);
            
            if (response?.data?.data) {
                console.log("Pricing saved successfully");
                setShowSavePricingInput(false);
                setSavePricingName('');
                
                // Navigate to the history page - data will be fresh thanks to query invalidation
                setLoadComponent(<SearchHistoryWindow />);
                setIsPriceSearchHistory(true);
            } else {
                console.error("Failed to save pricing");
            }
        } catch (error) {
            console.error('Error saving pricing:', error);
        } finally {
            setIsSaving(false);
        }
    }

    return (
        <div className={styles.rightSection}>
            <button className={styles.createPOBTn} onClick={() => navigatePage(location.pathname, {path:routes.createPoPage})}><span className={styles.createPoBtnInnerSection}><div>CREATE</div>
                <div>PO</div></span></button>
            <div className={clsx(styles.actionMenu, (selectedProductsData.length === 0) && styles.overlay)}>
                <div className={styles.actionMenuTitle}>ACTION MENU</div>
                <button className={clsx(styles.actionBtn, (shareEmailType === shareEmailTypes.sharePrice) && styles.activeActionBtn)} onClick={handleSharePrice} disabled={(selectedProductsData.length === 0)} ><span>SHARE PRICING</span></button>
                <button className={styles.actionBtn} disabled={(selectedProductsData.length === 0)} ><span>EXPORT PRICING</span></button>
                
                <button className={clsx(showSavePricingInput && styles.positionRelative, styles.actionBtn)} disabled={(selectedProductsData.length === 0)} onClick={handleSavePricing} >
                    <span>SAVE PRICING</span>
                    {showSavePricingInput && (
                        <ClickAwayListener onClickAway={() => setShowSavePricingInput(false)}>
                            <div className={styles.savePricingInputContainer}>
                                <InputWrapper>
                                    <CustomTextField
                                        className={clsx(styles.savePricingInput)}
                                        type='text'
                                        placeholder='NAME THIS PRICE LIST'
                                        maxLength={30}
                                        onChange={(e: any) => {
                                            setSavePricingName(e.target.value);
                                        }}
                                        value={savePricingName}
                                        autoFocus={showSavePricingInput}
                                        onKeyDown={(e: any) => {
                                            if(e.key === 'Space' || e.key === ' '){
                                                e.preventDefault();
                                                e.stopPropagation();
                                                setSavePricingName(savePricingName + ' ');
                                            }
                                        }}
                                    />
                                </InputWrapper>
                                <button 
                                    className={styles.actionInputBtn} 
                                    disabled={(selectedProductsData.length === 0 || savePricingName.trim().length === 0 || isSaving)} 
                                    onClick={handleSavePricingInput}
                                >
                                    {isSaving ? 'SAVING...' : 'SAVE'}
                                </button>
                            </div>
                        </ClickAwayListener>
                    )}
                </button>
                <button className={styles.actionBtn} onClick={handleDeleteLines} disabled={(selectedProductsData.length === 0)} ><span>DELETE LINES</span></button>
                <button className={styles.actionBtn} disabled={(selectedProductsData.length === 0)} ><span>ADD LINES TO PO</span></button>
            </div>
            <button className={clsx(styles.createPOBTn, styles.MarginTop16, styles.viewHistoryBtn)} onClick={handleViewHistory}>
                <span className={styles.createPoBtnInnerSection}>
                    <div>VIEW</div> <div>HISTORY</div>
                </span>
            </button>
            <span className={styles.clearList}>
            <button disabled={filterShortListedSearchProductsData.length < 1} onClick={handleResetData}>
             CLEAR LIST
             </button>
            </span>
           
        </div>
    )
}

export default SearchRightSection