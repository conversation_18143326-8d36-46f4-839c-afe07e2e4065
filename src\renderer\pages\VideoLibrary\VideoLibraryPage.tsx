import { USE_IMAGEKIT, USE_IMGIX, useGetAllVideoLibraryTag, useGetVideoByTag, useGlobalStore, useImgixOrImageKit, useSaveVideoLibraryViews } from "@bryzos/giss-ui-library";
import { useEffect, useState } from "react";
import { useVideoStore } from "./VideoStore";
import SectionPills from "src/renderer/component/SectionPills/SectionPills";
import VideoPlayer from "src/renderer/component/videoPlayer/videoPlayer";
import ShareVideoUrl from "src/renderer/component/ShareVideoUrl/ShareVideoUrl";
import VideoSection from "./VideoSection";
import styles from "./videoLibrary.module.scss";
import { ReactComponent as CreateNewPluseIcon } from '../../assets/images/createNewPluse.svg';
import { useHoverVideoStore } from "src/renderer/component/LeftPanel/HoverVideoStore";
import VideoSearchField from "./VideoSearchField";

const VideoLibraryPage = () => {
    const getVideoByTag = useGetVideoByTag();
    const getAllVideoLibraryTag = useGetAllVideoLibraryTag();
    const imgixOrImageKit = useImgixOrImageKit();
    const [useImgixUrl, setUseImgixUrl] = useState(false);
    const [useImageKitUrl, setUseImageKitUrl] = useState(false);
    //const [transformedSelectedVideo, setTransformedSelectedVideo] = useState();
    const { panelData, setPanelData, setSelectedSection, selectedSection, selectedVideo, setSelectedVideo, setShareVideoObject, shareVideoObject, setVideoSectionPills, videoSectionPills,
        transformedSelectedVideo, setTransformedSelectedVideo, showPiP, seekTime, setSeekTime, videoSearchText, setVideoSearchText
    } = useVideoStore()
    const {setShowLoader, userData} = useGlobalStore()
    const saveVideoViewCount = useSaveVideoLibraryViews();
    const [openShare, setOpenShare] = useState(false);
    const [showVideo, setShowVideo] = useState(false);
    const [interactedVideos, setInteractedVideos] = useState<any[]>([]);
    const {isHoverVideoEnabled} = useHoverVideoStore();
    const clientName = import.meta.env.VITE_CLIENT_NAME;

    useEffect(() => {
      if (panelData && Object.keys(panelData).length > 0 && selectedVideo?._pendingSelection) {
        const videoId = selectedVideo.id;
        const videoExists = Object.values(panelData).flat().find((video: any) => video.id === videoId);
        
        if (videoExists) {
          const correctSection = Object.keys(panelData).find((key) => 
            panelData[key].find((item: any) => item.id === videoId)
          );
          
          if (correctSection) {
            setSelectedSection(correctSection);
            setSelectedVideo(videoExists);
          }
        }
        return;
      }
    
      if (panelData && Object.keys(panelData).length > 0 && selectedVideo && Object.keys(selectedVideo).length > 0 && !selectedVideo._pendingSelection) {
        const videoExists = Object.values(panelData).flat().find((video: any) => video.id === selectedVideo.id);
        
        if (videoExists) {
          const correctSection = Object.keys(panelData).find((key) => 
            panelData[key].find((item: any) => item.id === selectedVideo.id)
          );
          
          if (correctSection && correctSection !== selectedSection) {
            setSelectedSection(correctSection);
          }
          
          setSelectedVideo(videoExists);
        } else {
          if (selectedSection && panelData[selectedSection] && panelData[selectedSection].length > 0) {
            setSelectedVideo(panelData[selectedSection][0]);
          }
        }
      }
    }, [panelData, selectedVideo, selectedSection]);

    useEffect(() => {
      if (selectedVideo && Object.keys(selectedVideo).length > 0 && !selectedVideo._pendingSelection) {
        if(transformedSelectedVideo && transformedSelectedVideo.id === selectedVideo.id && 
          transformedSelectedVideo.thumbnail_s3_url_map?.electron_player === selectedVideo.thumbnail_s3_url_map?.electron_player) return;
        
        setInteractedVideos((prevInteractedVideo: string[]) => Array.from(new Set([...prevInteractedVideo, selectedVideo])));
        
        const transformedObj = {
          ...selectedVideo,
          thumbnail_s3_url_map: {
            ...selectedVideo.thumbnail_s3_url_map
          }
        };
    
        if (selectedVideo?.share_video_url) {
          transformedObj.share_video_url = selectedVideo?.share_video_url;
        }
    
        if (selectedVideo?.thumbnail_s3_url_map) {
          transformedObj.thumbnail_s3_url_map.electron_player = transformImageURL(selectedVideo?.thumbnail_s3_url_map?.electron_player);
          transformedObj.thumbnail_s3_url_map.intro_desktop = transformImageURL(selectedVideo?.thumbnail_s3_url_map?.intro_desktop);
          transformedObj.thumbnail_s3_url_map.thumbnail_app = transformImageURL(selectedVideo?.thumbnail_s3_url_map?.thumbnail_app);
        }
        transformedObj.video_s3_url = transformVideoURL(selectedVideo?.video_s3_url, selectedSection, selectedVideo?.is_large_file);
        setShowVideo(false);
        setSeekTime(0);
        setTransformedSelectedVideo(transformedObj);
      }
    }, [selectedVideo, selectedSection]);

    useEffect(() => {
      if (transformedSelectedVideo) {
if (transformedSelectedVideo?.video_s3_url || !!transformedSelectedVideo?.thumbnail_s3_url_map) {
  const payload = {
    data: {
      user_id: userData.data.id.toString(),
      video_id: transformedSelectedVideo.video_id,
    },
  };
    if (payload?.data?.user_id && payload?.data?.video_id) {
        saveVideoViewCount.mutateAsync(payload).then(() => {
            setInteractedVideos((prevInteractedVideo) =>
                prevInteractedVideo.map((video) =>
                    video.video_id === transformedSelectedVideo.video_id
                        ? { ...video, view_count: (video.view_count || 0) + 1 }
                        : video
                )
            );
        })
    }
}
      }
    }, [transformedSelectedVideo])

    useEffect(() => {
        if (shareVideoObject) {
            setOpenShare(true);
        }
    }, [shareVideoObject]);

    useEffect(() => {
        getAllVideos();
    }, [])

    const transformImageURL = (rawUrl: string) => {
        if (!rawUrl) return "";
        const s3Path = rawUrl.split(".com")[1];
        return useImgixUrl ? import.meta.env.VITE_IMGIX_PREFIX + s3Path + import.meta.env.VITE_IMGIX_SUFFIX : import.meta.env.VITE_CLOUDFRONT_PREFIX + s3Path;
    };

    const transformVideoURL = (rawUrl: string, section, isLarge) => {
        if (!rawUrl) return "";
        const s3Path = rawUrl.split(".com")[1];
        let url = useImageKitUrl
            ? import.meta.env.VITE_IMAGEKIT_PREFIX +
            s3Path
            : import.meta.env.VITE_CLOUDFRONT_PREFIX + s3Path;
        url += '?tag=' + section;
        if (isLarge && useImageKitUrl) {
            url += '&tr=orig';
        }
        return url;
    }

    const getAllVideos = async () => {
        setShowLoader(true);
        try {
            const response = (await imgixOrImageKit.mutateAsync())?.data?.data;
            if (response) {
                const imgixObj = response.find(res => res.config_key === USE_IMGIX);
                if (imgixObj) {
                    setUseImgixUrl(imgixObj.config_value);
                }
                const imgeKitObj = response.find(res => res.config_key === USE_IMAGEKIT);
                if (imgeKitObj) {
                    setUseImageKitUrl(imgeKitObj.config_value);
                }
            }
            const libraryTagsList = (await getAllVideoLibraryTag.mutateAsync()).data?.data;
            if (libraryTagsList) {
                const tagList = libraryTagsList.reduce((tags, curr_tag) => {
                    if (curr_tag.show_on_app) tags.push(curr_tag.query_param);
                    return tags;
                }, []);
                getVideoByTag.mutateAsync(tagList).then((data) => {
                  setPanelData(data?.data?.data);
                  const videoSectionPills = Object.keys(data?.data?.data).map((key) => {
                    return {
                      label: key,
                      id: key,
                      disabled: false
                    };
                  });
                  setVideoSectionPills(videoSectionPills);
                  
                  const currentState = useVideoStore.getState();
                  if (currentState.selectedVideo?._pendingSelection) {
                    const videoId = currentState.selectedVideo.id;
                    const videoExists = Object.values(data?.data?.data).flat().find((video: any) => video.id === videoId);
                    
                    if (videoExists) {
                      const correctSection = Object.keys(data?.data?.data).find((key) => 
                        data?.data?.data[key].find((item: any) => item.id === videoId)
                      );
                      
                      if (correctSection) {
                        setSelectedSection(correctSection);
                        setSelectedVideo(videoExists);
                      }
                    }
                  } else if (!currentState.selectedVideo || Object.keys(currentState.selectedVideo).length === 0) {
                    const firstSection = Object.keys(data?.data?.data)[0];
                    if (firstSection && data?.data?.data[firstSection]?.length > 0) {
                      setSelectedSection(firstSection);
                      setSelectedVideo(data.data.data[firstSection][0]);
                    }
                  }
                  
                  setShowLoader(false);
                })
            }
        }
        catch (err) {
            console.log(err);
            setShowLoader(false);
        }
    }

    const openShareVideoPopup = () => {
        setShareVideoObject({ video_id: transformedSelectedVideo?.id, share_video_url: transformedSelectedVideo?.share_video_url });
    };

    const shareVideoPopupClose = () => {
        setOpenShare(false);
        setShareVideoObject(null);
    };


    return (
        <div className={styles.videoPlayerMain}>
            <div className={styles.videoPlayerTitleMain}>
                <div className={styles.videoPlayerTitle}>THE {clientName?.toUpperCase()} VAULT</div>
                <p>Go beyond the surface - get the all-access pass to the people, the stories, and the drive that power {clientName}. From the docuseries Cracking the Code to the podcast Irregardless, go behind the scenes with the team thatâ€™s reshaping metal procurement and their guests sharing their own experiences. Real conversations. Real decisions. Real grit. See why {clientName} was built for you â€” and how itâ€™s changing the game for good.</p>
            </div>
            <VideoSearchField 
                value={videoSearchText}
                onChange={(value:string) => {
                    setVideoSearchText(value);
                 }}
            />
            {videoSectionPills &&
                <SectionPills
                    items={videoSectionPills}
                    activeId={selectedSection}
                    onChange={(value) => {
                        setSelectedSection(value);
                        setSelectedVideo(panelData[value][0]);
                    }}
                />
            }
            <div className={styles.videoMainContainer}>
          {(transformedSelectedVideo) ?
            <>
              <h2 className={styles.videoPlayerMainTitle}>{transformedSelectedVideo?.title}</h2>
              <p className={styles.videoPlayerDescription}>{transformedSelectedVideo?.description}&nbsp;</p>
              
              {(transformedSelectedVideo?.video_s3_url) ?
                <VideoPlayer
                  url={transformedSelectedVideo?.video_s3_url}
                  width={"100%"}
                  height={"300px"}
                  autoPlay={!transformedSelectedVideo?.thumbnail_s3_url_map?.electron_player}
                  playNextVideo={() => { }}
                  disableNextVideoBtn={true}
                  captionUrl={transformedSelectedVideo?.subtitle_s3_url}
                  pauseImageUrl={transformedSelectedVideo?.thumbnail_s3_url_map?.electron_player}
                  showPiPControl={true}
                  isPiPMode={showPiP}
                  onPiPClick={() => {
                    if(isHoverVideoEnabled) return false;
                    return true;
                  }}
                  seekTime={seekTime?seekTime:0}
                />
              :
                (transformedSelectedVideo?.thumbnail_s3_url_map?.electron_player) ?
                  <img src={transformedSelectedVideo?.thumbnail_s3_url_map?.electron_player} height={"300px"} width={"100%"} alt={transformedSelectedVideo?.caption}></img>
                :
                  <div style={{height: "300px", display: "flex", alignItems: "center", justifyContent: "center", backgroundColor: "#f5f5f5"}}>
                    <p>Loading video...</p>
                  </div>
              }
              
              <p className={styles.videoPlayerDescriptionBottom}>{transformedSelectedVideo?.caption}&nbsp;</p>
              <div className={styles.videoPlayerShareButton}>
                { transformedSelectedVideo?.share_video_url ?
                  <button className={styles.shareVideoButton} onClick={openShareVideoPopup}>Share Video</button>
                : null}
              </div>
            </>
          :
            <div style={{height: "300px", display: "flex", alignItems: "center", justifyContent: "center", backgroundColor: "#f9f9f9"}}>
              <p>Please select a video to play</p>
            </div>
          }
            {
                (selectedSection && transformedSelectedVideo) &&
                <VideoSection
                    section={selectedSection}
                    sectionList={panelData[selectedSection]}
                    activeId={selectedVideo?.id}
                    interactedVideos={interactedVideos}
                    onItemClick={
                        (videoObject, index) => {
                            setSelectedVideo(videoObject)
                        }
                    }
                    onPlayClick={
                        (videoObject, index) => {
                            setSelectedVideo(videoObject);
                        }
                    } />
            }
          <ShareVideoUrl openShare={openShare} shareVideoPopupClose={shareVideoPopupClose} videoData={transformedSelectedVideo} />
        </div>
        </div>
    );
};

export default VideoLibraryPage;