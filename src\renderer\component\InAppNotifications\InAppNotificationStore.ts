import { create } from "zustand";
import dayjs from "dayjs";
import isToday from "dayjs/plugin/isToday";
import isYesterday from "dayjs/plugin/isYesterday";
import weekday from "dayjs/plugin/weekday";
import { getFormattedCentralTime } from "src/renderer/helper";


dayjs.extend(isToday);
dayjs.extend(isYesterday);
dayjs.extend(weekday);

export type InAppNotif = InAppNotification & {
  sentEpoch: number; // for stable sorting
};

type State = {
  navigationURL:string;
  loading: boolean;
  error?: string;
  openedOnceAt?: number;
  items: Record<string, InAppNotif>; // by id
  order: string[]; // ids, desc by sentEpoch
  showPanel: boolean;
  markOnCloseIds: string[];
};
type Actions = {
  normalizeFromServer: (raw: any) => InAppNotification;
  notificationAdapter: (n: any) => InAppNotification;
  ingestLive: (n: InAppNotification) => void;
  onPanelOpen: () => void;
  clearByIds: (ids: string[]) => Promise<void>;
  clearAll: () => Promise<void>;
  setFromServer: (list: InAppNotification[]) => void; 
  toggleShowPanel: () => void;      
  hidePanel: () => void; 
  getReadList: () => InAppNotif[];
  getUnreadList: () => InAppNotif[];
  getUnreadCount: () => number;
  setNavigationURL: (url: string) => void;
  onPanelClose: () => string[];
};

function toNotif(n: InAppNotification): InAppNotif {
  // sent_date like "2025-08-13 09:03 AM" (assume local time string)
  const d = dayjs(n.sent_date, "YYYY-MM-DD hh:mm A", true);
  const epoch = d.isValid() ? d.valueOf() : Date.parse(n.sent_date);
  return { ...n, sentEpoch: Number.isFinite(epoch) ? epoch : Date.now() };
}

function sortIds(items: Record<string, InAppNotif>) {
  return Object.values(items)
    .sort((a, b) => b.sentEpoch - a.sentEpoch)
    .map((n) => n.id);
}

export const useInAppNotificationStore = create<State & Selectors & Actions>((set, get) => ({
  // state
  loading: false,
  items: {},
  order: [],
  showPanel: false,
  navigationURL:null,
  markOnCloseIds: [],


  // actions
  setNavigationURL(url:string){
    set({navigationURL:url});
  },
  getReadList() {
    return get().order.map(id => get().items[id]).filter(n => n?.is_read_in_app === 1) as InAppNotif[];
  },
  getUnreadList() {
    return get().order.map(id => get().items[id]).filter(n => n?.is_read_in_app === 0) as InAppNotif[];
  },
  getUnreadCount() {
    return get().order.reduce((a, id) => a + (get().items[id]?.is_read_in_app === 0 ? 1 : 0), 0);
  },
  hidePanel() {
    set({ showPanel: false });
  },
  toggleShowPanel() {
    set({ showPanel: !get().showPanel });
  },
  normalizeFromServer(raw: any): InAppNotification {
  return {
    id: raw.id ?? raw.notification_id,
      notification_title: raw.notification_title ?? raw.title,
      message: raw.message ?? raw.body,
      notification_route: raw.notification_route ?? raw.navigation ?? null,
      mobile_notification_route: raw.mobile_notification_route ?? null,
      is_read_in_app: Number(raw.is_read_in_app ?? 0) as 0 | 1,
      sent_date: raw.sent_date, // keep as-is; your toNotif() parses it
    };
  },
  setFromServer(list: any[]) {
    const items: Record<string, InAppNotif> = {};
    for (const raw of list) {
      const norm = get().normalizeFromServer(raw);
      items[norm.id] = toNotif(norm);
    }
    const order = sortIds(items);
    set({ items, order });
  },

  notificationAdapter(n) {
    const id = n.id ?? n.notification_id ?? n.notificationId;
    return {
      id,
      notification_id :n.notificationId,
      notification_title:n.title,
      message:n.body,
      notification_route:n.navigation,
      mobile_notification_route:null,
      is_read_in_app:0,
      sent_date:getFormattedCentralTime(),
    };
  },

  ingestLive(notificationObj) {
    const n = get().notificationAdapter(notificationObj);
    const items = { ...get().items, [n.id]: toNotif(n) };
    const order = sortIds(items);
    set({ items, order });
  },

  onPanelClose() {
    const ids = get().markOnCloseIds;
    if (!ids.length) { set({ showPanel: false }); return { toMarkRead: [] }; }

    const items = { ...get().items };
    for (const id of ids) {
      const n = items[id];
      if (n && n.is_read_in_app === 0) items[id] = { ...n, is_read_in_app: 1 };
    }
    set({ items, markOnCloseIds: [], showPanel: false });
    return ids;
  },

  onPanelOpen() {
    const unreadIds = get().order.filter(id => get().items[id]?.is_read_in_app === 0);
    set({ markOnCloseIds: unreadIds, openedOnceAt: Date.now(), showPanel: true });
  },

  async clearByIds(ids) {
    if (!ids.length) return;
    const prev = get().items;
    const backup = { ...prev };
    // optimistic remove
    const items: Record<string, InAppNotif> = {};
    for (const [id, n] of Object.entries(prev)) if (!ids.includes(id)) items[id] = n;
    const order = sortIds(items);
    set({ items, order });
  },

  async clearAll() {
    const ids = [...get().order];
    await get().clearByIds(ids);
  },
}));

// Small helper for relative time labels like the mock
export function formatRelativeLabel(epoch: number) {
  const d = dayjs(epoch);
  if (d.isToday()) return d.format("h:mm A");
  if (d.isYesterday()) return "Yesterday";
  const daysAgo = dayjs().startOf("day").diff(d.startOf("day"), "day");
  if (daysAgo < 7) return d.format("dddd"); // e.g., Wednesday
  return d.format("MMM D"); // fallback for older
}
