<svg width="560" height="563" viewBox="0 0 560 563" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_189_8863)">
<rect width="560" height="563" rx="20" fill="#191A20"/>
<g opacity="0.8" filter="url(#filter0_f_189_8863)">
<circle cx="280" cy="631" r="60" fill="#9786FF"/>
<circle cx="280" cy="631" r="60" fill="url(#paint0_linear_189_8863)" fill-opacity="0.8"/>
<g clip-path="url(#paint1_angular_189_8863_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.06 0.0785787 -0.056235 0.109785 280 635.873)"><foreignObject x="-945.056" y="-945.056" width="1890.11" height="1890.11"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><circle cx="280" cy="631" r="60" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:119.99998474121094,&#34;m01&#34;:-112.46994018554688,&#34;m02&#34;:276.23498535156250,&#34;m10&#34;:157.15734863281250,&#34;m11&#34;:219.56929016113281,&#34;m12&#34;:447.5097656250},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<g filter="url(#filter1_f_189_8863)">
<circle cx="100" cy="-100" r="100" fill="#9786FF"/>
<circle cx="100" cy="-100" r="100" fill="url(#paint2_linear_189_8863)" fill-opacity="0.8"/>
<g clip-path="url(#paint3_angular_189_8863_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.1 0.130964 -0.0937249 0.182974 100 -91.8782)"><foreignObject x="-939.022" y="-939.022" width="1878.04" height="1878.04"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><circle cx="100" cy="-100" r="100" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:199.99996948242188,&#34;m01&#34;:-187.44989013671875,&#34;m02&#34;:93.724960327148438,&#34;m10&#34;:261.92892456054688,&#34;m11&#34;:365.94882202148438,&#34;m12&#34;:-405.81704711914062},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
</g>
<rect x="0.5" y="0.5" width="559" height="562" rx="19.5" stroke="url(#paint4_radial_189_8863)" stroke-opacity="0.4"/>
<defs>
<filter id="filter0_f_189_8863" x="160" y="511" width="240" height="240" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="30" result="effect1_foregroundBlur_189_8863"/>
</filter>
<clipPath id="paint1_angular_189_8863_clip_path"><circle cx="280" cy="631" r="60"/></clipPath><filter id="filter1_f_189_8863" x="-100" y="-300" width="400" height="400" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_189_8863"/>
</filter>
<clipPath id="paint3_angular_189_8863_clip_path"><circle cx="100" cy="-100" r="100"/></clipPath><linearGradient id="paint0_linear_189_8863" x1="280" y1="607.244" x2="361.45" y2="651.714" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_189_8863" x1="100" y1="-139.594" x2="235.749" y2="-65.4773" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint4_radial_189_8863" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(173.133 -9.49244) rotate(113.174) scale(147.05 134.194)">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<clipPath id="clip0_189_8863">
<rect width="560" height="563" rx="20" fill="white"/>
</clipPath>
</defs>
</svg>
