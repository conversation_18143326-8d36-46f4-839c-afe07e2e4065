import React, { useState, useEffect, useRef } from 'react';
import styles from '../SellerHeaderInfo/SellerHeaderInfo.module.scss'
import { commomKeys, formatToTwoDecimalPlaces, useCreatePoStore, useGlobalStore, useOrderManagementStore, userRole } from '@bryzos/giss-ui-library';
import clsx from 'clsx';
import usePostDisputeOrder from 'src/renderer/hooks/usePostDisputeOrder';
import useDialogStore from 'src/renderer/component/DialogPopup/DialogStore';
import { disputeCounterStatus } from 'src/renderer/common';

const OrderCancellationDispute = () => {
    const [showAcceptCancellation, setShowAcceptCancellation] = useState(false);
    const setGlobalDisputeComponent = useOrderManagementStore(state => state.setGlobalDisputeComponent);
    const [restockingFee, setRestockingFee] = useState('');
    const { mutateAsync: postDisputeOrder } = usePostDisputeOrder();
    const selectedQuote = useCreatePoStore(state => state.selectedQuote);
    const { showCommonDialog, resetDialogStore } = useDialogStore();
    const { setShowLoader, userData } = useGlobalStore();
    const cancelOrderDisputeList = selectedQuote?.order_level_dispute?.cancel_order;
    const lastCounterData = selectedQuote?.order_level_dispute?.cancel_order?.[selectedQuote?.order_level_dispute?.cancel_order?.length - 1];
    const startIndex = cancelOrderDisputeList?.findIndex(
        item => parseFloat(item.restocking_fee) > 0
    );
    const isSupplier = userData?.data?.type === userRole.sellerUser;

    const refineRestockingFeeCounterList =
        startIndex !== -1
            ? cancelOrderDisputeList?.slice(startIndex)
            : [];

    const [showEditRestockingFee, setShowEditRestockingFee] = useState(false);
    const [showBuyerAcceptRestockingFee, setShowBuyerAcceptRestockingFee] = useState(false);
    const [buyerRestockingFee, setBuyerRestockingFee] = useState("");
    const [currentSubmitAction, setCurrentSubmitAction] = useState("");
    const disputeCounterScrollRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if(selectedQuote){
            setShowEditRestockingFee(false);
            setShowBuyerAcceptRestockingFee(false);
            setCurrentSubmitAction("");
        }
    }, [selectedQuote]);

    useEffect(() => {
        if (disputeCounterScrollRef.current && refineRestockingFeeCounterList?.length > 0) {
            disputeCounterScrollRef.current.scrollTop = disputeCounterScrollRef.current.scrollHeight;
        }
    }, [refineRestockingFeeCounterList]);

    const handleAcceptCancellation = () => {
        setShowAcceptCancellation(true);
    };

    const handleRejectCancellation = () => {
        // Go back to previous screen
        handleSubmit("reject");
        setShowAcceptCancellation(false);
        setGlobalDisputeComponent(null);
    };

    const handleSubmit = async (mode: string = "accept", restockingFee: string = "") => {
        try {
            setShowLoader(true);
            const payload = {
                "data": {
                    "po_number": userData?.data?.type === userRole.buyerUser ? selectedQuote?.buyer_po_number : selectedQuote?.seller_po_number,
                    "order_level": {
                        "is_order_canceled": 1,
                        "action": currentSubmitAction ? currentSubmitAction : mode,
                        "counter_id": lastCounterData?.counter_id,
                        "restocking_fee": restockingFee ? restockingFee : undefined

                    }
                }
            }
            const response = await postDisputeOrder(payload);
            if (
                typeof response?.data === "object" &&
                "error_message" in response.data
            ) {
                throw new Error(response.data.error_message);
            }
        } catch (error) {
            console.log("error @>>>>>>>", error);
            showCommonDialog(null, error?.message || commomKeys.errorContent, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
            setShowLoader(false);
        } finally {
            setShowEditRestockingFee(false);
            setShowAcceptCancellation(false);
            setCurrentSubmitAction("");
            setShowBuyerAcceptRestockingFee(false);
            setBuyerRestockingFee("");
            setRestockingFee("");
        }
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        e.target.value = e.target.value.replace(/[^\d.]/g, '');
        setRestockingFee(e.target.value);
    };

    const handleBuyerInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        e.target.value = e.target.value.replace(/[^\d.]/g, '');
        setBuyerRestockingFee(e.target.value);
    };

    const handleEditRestockingFee = () => {
        setShowEditRestockingFee(true);
    };

    const handleAcceptCounter = () => {
        handleSubmit("accept", lastCounterData?.restocking_fee);
    };

    const handleCounterClick = () => {
        setShowEditRestockingFee(true);
        setCurrentSubmitAction("counter");
    };

    const handleRejectCounter = () => {
        handleSubmit("reject");
    };

    const handleBuyerAcceptCounter = () => {
        handleSubmit("accept", lastCounterData?.restocking_fee);
    };

    const handleBuyerRejectCounter = () => {
        handleSubmit("reject");
    };

    const handleEditCounter = () => {
        setShowBuyerAcceptRestockingFee(true);
        setCurrentSubmitAction("edit_counter");
    };

    const getLastRestockingFeeByCreator = (data: any, creator: string) => {
        if (!Array.isArray(data) || !creator) return null;
    
        const match = data.slice().reverse().find(item => item.created_by === creator);
        return match ? match.restocking_fee : null;
    };

    return (
        <div className={styles.buyerCancellationContainer}>
            { 
                (userData?.data?.type === userRole.buyerUser) ? (
                    <>
                    
                        {lastCounterData?.counter_status === disputeCounterStatus.pending && lastCounterData?.restocking_fee && Number(lastCounterData?.restocking_fee) !== 0 ? (
                            <>
                                <h2>Seller Accepted Order Cancelation - Restocking Fee Applied</h2>
                                <p className={styles.buyerCancellationDescription}>This order cancelation incurred the following restocking fee from the supplier: <span>${formatToTwoDecimalPlaces(getLastRestockingFeeByCreator(refineRestockingFeeCounterList, userRole.sellerUser ))}</span></p>
                            </>
                        ): (
                            <>
                                <h2>Cancelation Request Sent to Supplier</h2>
                                <p className={styles.buyerCancellationDescription}>Your request to cancel this order has been submitted to the supplier.</p>
                            </>
                        )}
                    </>
                ) : (
                    <>
                        {
                            (lastCounterData?.counter_status === disputeCounterStatus.pending && lastCounterData?.restocking_fee && Number(lastCounterData?.restocking_fee) !== 0) ? (
                                <>
                                    <h2>Accepted Order Cancelation - Restocking Fee Applied</h2>
                                    {
                                        lastCounterData?.created_by === userRole.buyerUser ? (
                                            <div className={styles.buyerCancellationDescription}>The buyer has countered the applied restocking fee of: <span>$ {formatToTwoDecimalPlaces(lastCounterData?.restocking_fee)}</span></div>
                                        ) : (
                                            <div className={styles.buyerCancellationDescription}>The buyer was presented with your restocking fee and they must Accept, Counter or Rescind.<br></br> We will notify you of their response.</div>
                                        )
                                    }
                                </>
                            ) : (
                                <>
                                    <h2>Buyer Requested To Cancel This Order</h2>
                                    <p className={styles.buyerCancellationDescription}>If you choose to accept this order cancelation, you'll have the option to apply a restocking fee.</p>
                                </>
                            )
                        }
                    </>
                )
            }

            {

                (userData?.data?.type === userRole.buyerUser) ? (
                    <>
                    {
                        lastCounterData?.counter_status === disputeCounterStatus.pending && lastCounterData?.restocking_fee && Number(lastCounterData?.restocking_fee) !== 0 && (
                        <div className={styles.disputeRestockingFeePending}>
                                {
                                    lastCounterData?.created_by === userRole.buyerUser && (
                            <div className={clsx(styles.disputeCounterContainer1, styles.disputeContainerFirst)}>
                                <div className={styles.disputeCounterScroll} ref={disputeCounterScrollRef}>
                                    {refineRestockingFeeCounterList?.length > 0 &&
                                        refineRestockingFeeCounterList?.map((dispute: any, i: number) => {
                                            const isMe = userData?.data?.type === dispute?.created_by;
                                            const isDisputeBuyer = dispute?.created_by === userRole.buyerUser;
                                            const isCounterResolved = dispute?.counter_status === disputeCounterStatus.resolved && i === refineRestockingFeeCounterList?.length - 1;
                                            const isCounterPending = dispute?.counter_status === disputeCounterStatus.pending && i === refineRestockingFeeCounterList?.length - 1;
                                            const isCounterRejected = dispute?.counter_status === disputeCounterStatus.rejected && i === refineRestockingFeeCounterList?.length - 1;
                                            let isCounterStatus: string | undefined
                                            if (isCounterResolved) {
                                                isCounterStatus = styles.isCounterResolved;
                                            } else if (isCounterPending) {
                                                isCounterStatus = styles.isCounterPending;
                                            } else if (isCounterRejected) {
                                                isCounterStatus = styles.isCounterRejected;
                                            } else {
                                                isCounterStatus = "";
                                            }

                                            return (
                                                <div className={styles.disputeCounterData} key={dispute.counter_number + i}>
                                                    <span className={styles.disputeCounterDataFirstCol}>
                                                        <span className={styles.disputeCounterStatus}>
                                                            <span className={styles.disputeCounterStatusLbl}>
                                                                {(i === 0) ? "Original " : isMe ? "Me " : isDisputeBuyer ? "Buyer" : "Supplier"}
                                                            </span>
                                                            {(i !== 0) && `(${i})`}
                                                        </span>
                                                        <span className={styles.disputeCounterQtyUnit}>
                                                        <span className={isCounterStatus}>${formatToTwoDecimalPlaces(dispute.restocking_fee)}</span> <span className={styles.restockingFeeLbl}> restocking fee</span>
                                                    </span>
                                                    </span>
                                               
                                                </div>
                                            )
                                        })
                                    }
                                </div>
                            </div>
                                    )
                                }
                            <div>
                                {
                                    showBuyerAcceptRestockingFee ? (
                                        <div className={styles.acceptCancellationContainer}>
                                            <div className={styles.acceptCancellationInput}>
                                                <label>$</label>
                                                <input
                                                    value={buyerRestockingFee}
                                                    placeholder="200.00"
                                                    className={clsx(styles.input)}
                                                    onChange={handleBuyerInputChange}
                                                />
                                            </div>
                                            <div className={styles.buyerCancellationButtons}>
                                                <button onClick={() => { setShowBuyerAcceptRestockingFee(false); setBuyerRestockingFee(""); setCurrentSubmitAction(""); }} >Cancel Counter</button>
                                                <button onClick={() => handleSubmit("counter", buyerRestockingFee)} disabled={!buyerRestockingFee || Number(buyerRestockingFee) === 0}>{isSupplier ? "Save Counter" : "Submit"}</button>
                                            </div>
                                        </div>
                                    ) : (
                                        <>
                                            {
                                                lastCounterData?.created_by === userRole.sellerUser ? (
                                                    <div className={styles.actionButtonsContainer}>
                                                        <button className={styles.actionButtons} onClick={handleBuyerAcceptCounter} >Accept Restocking Fee, Cancel Order</button>
                                                        <button onClick={() => setShowBuyerAcceptRestockingFee(true)} className={styles.actionButtons} >Counter</button>
                                                        <button className={styles.actionButtons} onClick={handleBuyerRejectCounter} >Rescind Cancelation, No Fee</button>
                                                    </div>
                                                ) : (
                                                    <div className={styles.editRestockingFeeContainer}>
                                                        <button className={styles.editRestockingFeeBtn} onClick={() => handleEditCounter()}>Edit Counter</button>
                                                        <span className={clsx(styles.counterStatus, styles.statusText)}>Countered</span>
                                                    </div>
                                                )
                                            }
                                        </>

                                    )
                                }
                            </div>
                        </div>
                        )
                    }
                    </>
                ) : (
                    <>
                        {
                            (lastCounterData?.counter_status === disputeCounterStatus.pending && lastCounterData?.restocking_fee && Number(lastCounterData?.restocking_fee) !== 0) ? (
                                <div className={styles.disputeRestockingFeePending}>
                                    <div className={clsx(styles.disputeCounterContainer1, styles.disputeContainerFirst)}>
                                        <div className={styles.disputeCounterScroll} ref={disputeCounterScrollRef}>
                                            {refineRestockingFeeCounterList?.length > 0 &&
                                                refineRestockingFeeCounterList?.map((dispute: any, i: number) => {
                                                    const isMe = userData?.data?.type === dispute?.created_by;
                                                    const isDisputeBuyer = dispute?.created_by === userRole.buyerUser;
                                                    const isCounterResolved = dispute?.counter_status === disputeCounterStatus.resolved && i === refineRestockingFeeCounterList?.length - 1;
                                                    const isCounterPending = dispute?.counter_status === disputeCounterStatus.pending && i === refineRestockingFeeCounterList?.length - 1;
                                                    const isCounterRejected = dispute?.counter_status === disputeCounterStatus.rejected && i === refineRestockingFeeCounterList?.length - 1;
                                                    let isCounterStatus: string | undefined
                                                    if (isCounterResolved) {
                                                        isCounterStatus = styles.isCounterResolved;
                                                    } else if (isCounterPending) {
                                                        isCounterStatus = styles.isCounterPending;
                                                    } else if (isCounterRejected) {
                                                        isCounterStatus = styles.isCounterRejected;
                                                    } else {
                                                        isCounterStatus = "";
                                                    }

                                                    return (
                                                        <div className={styles.disputeCounterData} key={dispute.counter_number + i}>
                                                            <span className={styles.disputeCounterDataFirstCol}>
                                                                <span className={styles.disputeCounterStatus}>
                                                                    <span className={styles.disputeCounterStatusLbl}>
                                                                        {(i === 0) ? "Original " : isMe ? "Me " : isDisputeBuyer ? "Buyer" : "Supplier"}
                                                                    </span>
                                                                    {(i !== 0) && `(${i})`}
                                                                </span>
                                                                <span className={styles.disputeCounterQtyUnit}>
                                                                    <span className={isCounterStatus}>${formatToTwoDecimalPlaces(dispute.restocking_fee)}</span> <span className={styles.restockingFeeLbl}>restocking fee</span>
                                                                </span>
                                                            </span>
                                                        </div>
                                                    )
                                                })
                                            }
                                        </div>
                                    </div>
                                    <div>
                                        {
                                            showEditRestockingFee ? (
                                                <div className={styles.acceptCancellationContainer}>
                                                    <div className={styles.acceptCancellationInput}>
                                                        <label>Restocking Fee $</label>
                                                        <input
                                                            value={restockingFee}
                                                            placeholder="200.00"
                                                            className={clsx(styles.input)}
                                                            onChange={handleInputChange}
                                                        />
                                                    </div>
                                                    <div className={styles.buyerCancellationButtons}>
                                                        <button onClick={() => { setShowEditRestockingFee(false); setRestockingFee(""); setCurrentSubmitAction(""); }} >Cancel </button>
                                                        <button className={styles.submitBtn} onClick={() => handleSubmit("edit_counter", restockingFee)} disabled={!restockingFee || Number(restockingFee) === 0}>Submit</button>
                                                    </div>
                                                </div>
                                            ) : (
                                                <>
                                                    {
                                                        lastCounterData?.created_by === userRole.buyerUser ? (
                                                            <div className={styles.actionButtonsContainer}>
                                                                <button className={styles.actionButtons} onClick={handleAcceptCounter} >Accept</button>
                                                                <button onClick={handleCounterClick} className={styles.actionButtons} >Counter</button>
                                                                <button className={styles.actionButtons} onClick={handleRejectCounter} >Reject</button>
                                                            </div>
                                                        ) : (
                                                            <div className={styles.editRestockingFeeContainer}>
                                                                <button className={styles.editRestockingFeeBtn} onClick={handleEditRestockingFee}>Edit Restocking Fee</button>
                                                                <span className={clsx(styles.acceptedStatus, styles.statusText)}>Accepted</span>
                                                            </div>
                                                        )
                                                    }
                                                </>

                                            )
                                        }
                                    </div>
                                </div>
                            ) : (
                                <>
                                    {!showAcceptCancellation ? (
                                        <div className={styles.buyerCancellationButtons}>
                                            <button onClick={handleAcceptCancellation}>Accept Cancelation</button>
                                            <button onClick={handleRejectCancellation}>Reject Cancelation</button>
                                        </div>
                                    ) : (
                                        <div className={styles.acceptCancellationContainer}>
                                            <div className={styles.acceptCancellationInput}>
                                                <label>Restocking Fee $</label>
                                                <input
                                                    value={restockingFee}
                                                    placeholder="200.00"
                                                    className={clsx(styles.input)}
                                                    onChange={handleInputChange}
                                                />
                                            </div>
                                            <div className={styles.buyerCancellationButtons}>
                                                <button onClick={() => handleSubmit("accept")} >Skip</button>
                                                <button onClick={() => handleSubmit("counter", restockingFee)} disabled={!restockingFee || Number(restockingFee) === 0}>Submit</button>
                                                <button onClick={() => setShowAcceptCancellation(false)} >Back</button>
                                            </div>
                                        </div>
                                    )}
                                </>
                            )

                        }
                    </>
                )
            }

        </div>
    );
};

export default OrderCancellationDispute;