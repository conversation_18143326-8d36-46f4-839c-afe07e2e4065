import { useMutation, useQueryClient } from '@tanstack/react-query';
import { axios } from '@bryzos/giss-ui-library';
import { reactQueryKeys } from 'src/renderer/common';
import useDialogStore from 'src/renderer/component/DialogPopup/DialogStore';

/**
 * Parameters for updating an ACH subscription
 */
interface UpdateAchSubscriptionParams {
  data: {
    subscription_id: string;
    update_type: 'payment_details' | 'user_count' | 'payment_method' | 'all';
    user_count?: number;
    payment_details?: {
      account_name?: string;
      account_type?: string;
      bank_name?: string;
      routing_number?: string;
      account_number?: string;
    };
    payment_method_id?: string;
    setup_intent_id?: string;
    verification_status?: boolean;
  };
}

/**
 * Response from updating an ACH subscription
 */
interface UpdateAchSubscriptionResponse {
  data: {
    subscription_id: string;
    status: string;
    message?: string;
    client_secret?: string;
    requires_verification?: boolean;
    setup_intent_id?: string;
    error_message?: {
      reason: string;
      email?: string[];
      message?: string;
    };
  };
}

/**
 * Hook for updating an existing subscription using ACH payment method
 *
 * This hook can be used to:
 * 1. Update payment details (account name, type, bank name)
 * 2. Update user count
 * 3. Update payment method (requires new setup intent and verification)
 * 4. Update all subscription details
 *
 * @returns A mutation function for updating ACH subscriptions
 */
const useUpdateAchSubscription = () => {
  const queryClient = useQueryClient();
  const { showCommonDialog, resetDialogStore } = useDialogStore();

  return useMutation<UpdateAchSubscriptionResponse, Error, UpdateAchSubscriptionParams>(
    async (params) => {
      const response = await axios.post<UpdateAchSubscriptionResponse>(
        import.meta.env.VITE_API_SERVICE + '/subscription/update-payment-details',
        params
      );

      // Check if the response contains an error message
      if (response.data?.data?.error_message) {
        const errorData = response.data.data.error_message;

        // Handle different types of errors
        if (errorData.reason === 'invalid_domain' && errorData.email) {
          // This is a specific error that might need special handling in the component
          // We'll return the response and let the component handle it
          return response.data;
        } else {
          // For general errors, show a dialog
          const errorMessage = errorData.message || 'Something went wrong';
          showCommonDialog(null, errorMessage, null, resetDialogStore, [
            { name: 'OK', action: resetDialogStore }
          ]);

          // Still return the response for the component to handle
          return response.data;
        }
      }

      return response.data;
    },
    {
      // Invalidate relevant queries when the mutation is successful
      onSuccess: (data) => {
        // Only invalidate queries if there's no error
        if (!data?.data?.error_message) {
          // Invalidate user subscription data to trigger a refetch
          queryClient.invalidateQueries([reactQueryKeys.getUserSubscription]);
        }
      }
    }
  );
};

export default useUpdateAchSubscription;
