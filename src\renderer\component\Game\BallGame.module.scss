.gameWrapper {
  position: relative;
  width: 100%;
  height: 880px;
  display: flex;
  flex-direction: column;
}

.game {
  width: 100%;
  height: 100%;
  background: transparent;
  position: relative;
  margin: 0;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ball {
  width: 30px;
  height: 30px;
//   background: white;
//   border-radius: 50%;
  position: absolute;
  background-image: url(../../assets/images/circle.png);
  background-size: contain;  // Add this to ensure image fits
  background-repeat: no-repeat;  // Add this to prevent repeating
  background-position: center;  // Add this to center the image
}

.paddle {
  width: 100px;
  height: 84px;
//   background: white;
  position: absolute;
  bottom: 40px;
  background-image: url(../../assets/images/onlywhiteLogo.svg);
  background-size: contain;  // Add this to ensure image fits
  background-repeat: no-repeat;  // Add this to prevent repeating
  background-position: center;  // Add this to center the image
  rotate: 90deg;
}

.menu {
  color: white;
  text-align: center;
  padding-top: 20%;

  button {
    padding: 10px 20px;
    margin-top: 20px;
    cursor: pointer;
  }
}

.scoreContainer {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 40px;
  left: 41px;
  color: white;
  font-family: Inter;
  font-size: 21px;
  line-height: 1.15;
  letter-spacing: 0.98px;
  gap: 10px;
}

.hiddenScoreContainer {
  /* Use a fixed height to maintain layout stability */
  height: 50px;
  /* Keep space in layout but don't show contents */
  visibility: hidden;
  /* Ensure it doesn't affect other elements */
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

.lastblackBox {
  height: 70px;
  width: 100%;
  background-color: black;
  position: absolute;
  bottom: 0;
}

.initialPage {
    font-family: Syncopate;
    font-size: 20px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 2.55;
    letter-spacing: 0.8px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .textTop {
        color: #32ff6c;
        padding-bottom: 68px;
        padding-top: 80px;
    }
    .imgCenter {
        width: 185.9px;
        height: 186px;
        position: relative;
        svg {
            width: 250.9px;
            height: 229px;
            position: absolute;
            left: -66px;
            top: -7px;
        }
    }
    .textBottom {
        color: #fff;
        padding-top: 124px;
        padding-bottom: 48px;
    }
    button {
        width: 278px;
        height: 50px;
        border-radius: 10px;
        background-color: #222329;
        font-family: Syncopate;
        font-size: 18px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: -0.72px;
        text-align: center;
        color: rgba(255, 255, 255, 0.4);
        z-index: 100;
        &:hover {
          background-color: #32ff6c;
          color: #1f2026;
        }
    }
}

.initialPage1 {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute; // Added this
}

.screen1, .screen2, .screen3, .screen4 {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%); // This centers the element perfectly
}

.screen1 {
  opacity: 1;
}

.screen2 {
  opacity: 1;
  transform: translate(-50%, -45%); // Adjust vertical position slightly
}

.screen3 {
  opacity: 1;
  transform: translate(-50%, -40%);
}

.screen4 {
  opacity: 1;
  transform: translate(-50%, -50%); // Just keep it centered
}

@keyframes float {
  0%, 100% {
    transform: translate(-50%, -50%); // Center position
  }
  50% {
    transform: translate(-50%, -45%); // Reduced movement
  }
}

.gameContainer {
  position: absolute;
  inset: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
  background: transparent;
  .bryzosBall {
    position: absolute;
    left: 50%;
    right: 0;
    top: 0;
    height: 50%;
    // animation: rotateAnimation 2s ease-in-out forwards;
    transform-origin: center;
    svg{
      position: relative;
      top: 264px;
      right: 170px;
      // animation: positionBallAnimation 2s ease-in-out forwards;
    }
  }
  .BryzosB {
    position: absolute;
    left: 0;
    top: 50%;
    height: 50%;
    right: 50%;
    bottom: 0;
    
    transform-origin: center;
    .initialIcon{
      svg{
        // display: none;
        position: relative;
        top: -176px;
        left: 235px;
        // animation: positionBigBAnimation 2s ease-in-out forwards;
      }
    }
    .gameIcon{
      position: absolute;
      left: 296px;
      bottom: 418px;
      img{
        opacity: 0;
        transform: rotate(-90deg);
        width: 200px;
        height: 200px;

      }
    }
    
  }
}

.rotateAnimation {
  animation: rotateAnimation 2s ease-in-out forwards;
  svg {
    animation: positionBallAnimation 2s ease-in-out forwards;
    @keyframes positionBallAnimation {
      0% {
        transform: scale(1);
        right: 150px;
        top: 264px;
      }
      100% {
        transform: scale(0.5);
        right: 110px;
        top: 320px;
      }
    }
  }
}

.rotateAnimation2 {
  animation: rotateAnimation 2s ease-in-out forwards;
  svg{
    animation: positionBigBAnimation 2s ease-in-out forwards;
    @keyframes positionBigBAnimation {
      0% {
        transform: scale(1);
        top: -140px;
        left: 256px;
        opacity: 1;
      }
      50% {
        opacity: 0;
      }
      100% {
        transform: scale(0.5);
        top: -110px;
        left: 180px;
        height: 275px;
        opacity: 0;
      }
    } 
  }
  .gameIcon{
    animation: positionGameIcon 2s ease-in-out forwards;
    @keyframes positionGameIcon {
      0% {
        left: 296px;
        bottom: 418px;
      }
      100% {
        left: 220px;
        bottom: 320px;
      }
    } 
    img{
      animation: positionBigBAnimation2 2s ease-in-out forwards;
      @keyframes positionBigBAnimation2 {
        0% {
          transform: scale(1) rotate(-90deg);
          opacity: 0;
        }
        100% {
          transform: scale(0.5) rotate(-90deg);
          opacity: 1;
        }
      } 
    }
  }
}
@keyframes rotateAnimation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(90deg);
  }
}

.centerOverLay{
  position: absolute;
  top: 255px;
  left: -100px;
  width: 200px;
  height: 200px;
  z-index: 100;
  cursor: pointer;
}
.bomUploadCrossIcon {
  position: absolute;
  right: 41px;
  top: 40px;
  cursor: pointer;
  z-index: 2;
}