// FloatingDock.tsx
import React, { useCallback, useEffect, useRef, useState } from "react";
import { createPortal } from "react-dom";

type Pos = { x: number; y: number };

export interface FloatingDockProps {
  isOpen: boolean;
  onClose?: () => void;
  title?: string;
  initialPosition?: Pos;            // defaults to bottom-right
  allowClickThrough?: boolean;      // true = rest of the app remains interactive
  snapToViewport?: boolean;         // keep panel inside viewport on drag
  children: React.ReactNode;
  style?: React.CSSProperties;      // optional visual overrides
  className?: string;

  // NEW (backwards-compatible: all default to false)
  disableDrag?: boolean;            // stops dragging if true
  hideTitleBar?: boolean;           // hides the title bar entirely
  hideCloseButton?: boolean;        // hides only the ✕ button (title bar stays)
}

export default function FloatingDock({
  isOpen,
  onClose,
  title = "Panel",
  initialPosition,
  allowClickThrough = true,
  snapToViewport = true,
  children,
  style,
  className,
  disableDrag = false,
  hideTitleBar = false,
  hideCloseButton = false,
}: FloatingDockProps) {
  if (typeof document === "undefined") return null;

  // default to bottom-right 24px offset
  const defaultPos: Pos = initialPosition ?? {
    x: Math.max(24, (window.innerWidth || 0) - 360 - 24),
    y: Math.max(24, (window.innerHeight || 0) - 240 - 24),
  };

  const [pos, setPos] = useState<Pos>(defaultPos);
  const dragStart = useRef<{ pointerId: number; origin: Pos; mouse: Pos } | null>(null);
  const panelRef = useRef<HTMLDivElement | null>(null);

  // Ensure inside viewport (useful on resize or after dragging)
  const clampToViewport = useCallback(
    (p: Pos): Pos => {
      if (!snapToViewport || !panelRef.current) return p;
      const rect = panelRef.current.getBoundingClientRect();
      const w = window.innerWidth;
      const h = window.innerHeight;
      const maxX = Math.max(0, w - rect.width);
      const maxY = Math.max(0, h - rect.height);
      return { x: Math.min(Math.max(0, p.x), maxX), y: Math.min(Math.max(0, p.y), maxY) };
    },
    [snapToViewport]
  );

  useEffect(() => {
    if (!isOpen) return;
    // Re-clamp on resize
    const onResize = () => setPos((p) => clampToViewport(p));
    window.addEventListener("resize", onResize);
    return () => window.removeEventListener("resize", onResize);
  }, [isOpen, clampToViewport]);

  // ESC to close (still works even if title bar is hidden)
  useEffect(() => {
    if (!isOpen || !onClose) return;
    const onKey = (e: KeyboardEvent) => e.key === "Escape" && onClose();
    window.addEventListener("keydown", onKey);
    return () => window.removeEventListener("keydown", onKey);
  }, [isOpen, onClose]);

  const onPointerDownTitle = (e: React.PointerEvent<HTMLDivElement>) => {
    if (disableDrag || hideTitleBar) return;
    if (!panelRef.current) return;
    panelRef.current.setPointerCapture(e.pointerId);
    dragStart.current = {
      pointerId: e.pointerId,
      origin: pos,
      mouse: { x: e.clientX, y: e.clientY },
    };
  };

  const onPointerMove = (e: React.PointerEvent<HTMLDivElement>) => {
    if (!dragStart.current) return;
    const dx = e.clientX - dragStart.current.mouse.x;
    const dy = e.clientY - dragStart.current.mouse.y;
    const next = { x: dragStart.current.origin.x + dx, y: dragStart.current.origin.y + dy };
    setPos(snapToViewport ? clampToViewport(next) : next);
  };

  const onPointerUp = () => {
    if (!dragStart.current) return;
    try {
      panelRef.current?.releasePointerCapture(dragStart.current.pointerId);
    } catch { /* no-op */ }
    dragStart.current = null;
  };

  if (!isOpen) return null;

  return createPortal(
    <div
      // Fullscreen layer that does NOT block clicks to the app (thanks to pointer-events)
      style={{
        position: "fixed",
        inset: 0,
        pointerEvents: allowClickThrough ? "none" : "auto",
        zIndex: 60_000, // above typical app UI
      }}
      aria-hidden={false}
    >
      <div
        ref={panelRef}
        role="dialog"
        aria-modal="false"
        aria-label={title}
        // The panel itself should accept pointer events even if the backdrop is click-through
        style={{
          position: "fixed",
          left: pos.x,
          top: pos.y,
          width: 360,
          maxWidth: "92vw",
          maxHeight: "85vh",
          display: "flex",
          flexDirection: "column",
          borderRadius: 16,
          boxShadow: "0 10px 30px rgba(0,0,0,0.15)",
          overflow: "hidden",
          pointerEvents: "auto",
          ...style,
        }}
        className={className ?? "hoverVideoPanel"}
        onPointerMove={onPointerMove}
        onPointerUp={onPointerUp}
      >
        {!hideTitleBar && (
          <div
            // Title bar doubles as drag handle (unless disabled)
            onPointerDown={onPointerDownTitle}
            style={{
              cursor: disableDrag ? "default" : "grab",
              userSelect: "none",
              padding: "20px",
              fontSize: 14,
              fontWeight: 600,
              background: "linear-gradient(180deg, rgba(0,0,0,0.04), rgba(0,0,0,0.02))",
              display: "flex",
              alignItems: "center",
              gap: 8,
              textTransform: "uppercase",
              textAlign: "center",
              zIndex: 61000,
              boxShadow: "0 0.34375rem 0.275rem -0.1875rem rgba(0, 0, 0, 0.91)",
            }}
          >
            <span style={{ flex: 1 }}>{title}</span>
            {onClose && !hideCloseButton && (
              <button
                onClick={onClose}
                aria-label="Close"
                style={{
                  border: 0,
                  background: "transparent",
                  padding: 6,
                  borderRadius: 8,
                  cursor: "pointer",
                }}
              >
                ✕
              </button>
            )}
          </div>
        )}

        {children}
      </div>
    </div>,
    document.body
  );
}
