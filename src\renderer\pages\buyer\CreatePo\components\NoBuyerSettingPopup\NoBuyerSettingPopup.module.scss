.modal.modal { 
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin: 0;
    width: 100%;
    max-width: 504px;
    min-width: 504px;
    min-height: 418px;
    border-radius: 50px;
    box-shadow: none;
    background-color: #0f0f14;
    padding: 64px 52px 64px 52px;
    .closeIcon {
        position: absolute;
        top: 36px;
        right: 36px;
        cursor: pointer;
    }
    .buttonContainer {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        button {
            border-radius: 50px;
            background-color: #2b2c32;
            border: none;
            cursor: pointer;
            width: 90%;
            height: 69px;
            font-family: Syncopate;
            font-size: 18px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: -0.72px;
            text-align: center;
            color: #fff;
            text-transform: uppercase;
        }
    }
    h1 {
        font-family: Syncopate;
        font-size: 24px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: -0.96px;
        text-align: center;
        color: #fff;
        text-transform: uppercase;
    }
    p {
        font-family: Inter;
        font-size: 16px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: normal;
        text-align: center;
        color: #c3c4ca;
        margin-bottom: 40px;
        margin-top: 24px;
    }
}
