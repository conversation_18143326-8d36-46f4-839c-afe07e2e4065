// @ts-nocheck
import { useQuery } from "@tanstack/react-query";
import { reactQueryKeys } from "../common";
import axios from "axios";
import { useBuyerSettingStore, useGlobalStore } from "@bryzos/giss-ui-library";

const useGetBuyingPreference = () => {
  const { setShowLoader } = useGlobalStore();
  const { setBuyerSettingInfo } = useBuyerSettingStore();

  return useQuery(
    [reactQueryKeys.getBuyingPreference],
    async () => {
      setShowLoader(true);
      try {
        const response = await axios.get(
          `${import.meta.env.VITE_API_SERVICE}/user/buyingPreference`
        );
        if (response.data?.data) {
          if (
            typeof response.data.data === "object" &&
            "error_message" in response.data.data
          ) {
            setShowLoader(false)
            throw new Error(response.data.data.error_message);
          } else {
            setBuyerSettingInfo(response.data.data);
            return response.data.data;
          }
        } else {
          setShowLoader(false)
          return null;
        }
      } catch (error) {
        setShowLoader(false)
        throw new Error(error?.message ?? error);
      }
    },
    {
      staleTime: 0,
      cacheTime: 0,
      refetchOnMount: true,
      refetchOnWindowFocus: false,
      retry: false,
      enabled: navigator.onLine
    }
  );
};

export default useGetBuyingPreference;
