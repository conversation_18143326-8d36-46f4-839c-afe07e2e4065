// DeleteCellRenderer.tsx
import React from "react";
import type { ICellRendererParams } from "ag-grid-community";

type DeleteCellRendererProps = ICellRendererParams & {
    onDeleteRow: (row: any) => void;
  };

export default function DeleteCellRenderer(props: DeleteCellRendererProps) {
    const handleDelete = () => {
        // Remove from grid view
        props.api.applyTransaction({ remove: [props.node.data] });
        // Remove from source data in parent
        props.onDeleteRow(props.node.data);
      };

  return (
    <button
      type="button"
      onClick={handleDelete}
      aria-label="Delete row"
      style={{
        padding: "4px 10px",
        background: "rgb(254, 226, 226)",
        cursor: "pointer",
        color: "#ffff",
        width: "100px",
        height: "30px",
        borderRadius: "9px",
        fontSize: "14px",
        backgroundImage: "linear-gradient(128deg, #e71c1c -23%, #ed4f4f 116%)",
      }}
    >
      Delete
    </button>
  );
}
