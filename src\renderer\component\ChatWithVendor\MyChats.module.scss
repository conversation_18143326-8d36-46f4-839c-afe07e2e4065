.claimOrderRightWindow {
    width: 322px;
    padding: 20px;
    border-radius: 20px;
    background-origin: border-box;
    position: relative;
    overflow: hidden;
    background: url(../../assets/images/Create-PO-Order-Ledger.svg) no-repeat transparent;

    &.orderUpdatedSuccesWindow {
        margin-top: 24px;
        padding: 32px 20px 20px 20px;

        .closeIcon {
            position: absolute;
            top: 8px;
            right: 8px;
            z-index: 9;
            cursor: pointer;
        }
    }

    &.fulfillmentMainWindow{
        margin-top: 24px;
    }

    .summarySection {
        background: url(../../assets/images/MaterialTotal.svg) no-repeat transparent;
        padding: 10px;
        background-size: cover;
    }
    &.chatWithVendor{
        height: 456px;
        margin-top: 24px;
    }

    .summaryRow {
        display: flex;
        justify-content: space-between;
        margin-bottom: 6px;

        &:last-child {
            margin-bottom: 0px;
        }

        .summaryRowLbl {
            font-family: Inter;
            font-size: 18px;
            font-weight: normal;
            line-height: 1;
            letter-spacing: normal;
            text-align: left;
            color: #fff;
        }

        .summaryRowNum {
            font-family: Inter;
            font-size: 18px;
            line-height: 1;
            text-align: right;
            color: #fff;
        }

        &.muted {

            .summaryRowLbl,
            .summaryRowNum {
                color: rgba(255, 255, 255, 0.4);
            }

        }

        &.total {
            padding: 1rem 0;
            font-weight: 600;
            font-size: 1.125rem;
        }
    }

    .totalPurchase {
        width: 100%;
        padding: 6px 10px 10px;
        background-image: linear-gradient(354deg, #000 142%, #191a20 4%);
        border-radius: 0px 0px 13px 13px;
        margin-top: -1px;

        .totalPurchaseLbl {
            font-family: Inter;
            font-size: 18px;
            font-weight: normal;
            line-height: 1;
            letter-spacing: normal;
            text-align: left;
            color: #fff;
        }

        .totalPurchaseNum {
            font-family: Inter;
            font-size: 18px;
            font-weight: normal;
            line-height: 1;
            letter-spacing: normal;
            text-align: right;
            color: #fff;
        }
    }


    .claimOrderNote {
        margin: 16px 0px 50px 0px;
        font-family: Inter;
        font-size: 12px;
        font-weight: 300;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.6;
        letter-spacing: -0.36px;
        text-align: center;
        color: rgba(255, 255, 255, 0.4);
    }


    .btnSection {
        display: flex;
        flex-direction: column;
        row-gap: 12px;

        button {
            width: 100%;
            height: 50px;
            flex-grow: 0;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            padding: 8px 0 8px;
            border-radius: 10px;
            background-color: #222329;
            font-family: Syncopate;
            font-size: 18px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: -0.72px;
            text-align: center;
            color: rgba(255, 255, 255, 0.4);
            margin-bottom: 12px;
            transition: all 0.1s;

            &:last-child {
                margin-bottom: 0px;
            }

            &[disabled] {
                background-color: #222329;
                color: rgba(255, 255, 255, 0.1);
            }

            &:hover {
                background-color: #fff;
                color: #0f0f14;
            }

        }
    }

}

.orderUpdatedSuccesfullMain {
    .orderUpdatedSuccesBg {
        margin-bottom: 12px;
        padding: 12px 25px 12px 26px;
        border-radius: 16px;
        background-color: #3da95d;


        .title {
            font-family: Syncopate;
            font-size: 16px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.4;
            letter-spacing: normal;
            text-align: center;
            color: #fff;
            display: flex;
            margin-bottom: 8px;
        }

        .title1 {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-family: Inter;
            font-size: 12px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: normal;
            text-align: center;
            color: rgba(255, 255, 255, 0.8);

            span {
                color: #fff;
            }
        }
    }

    .orderUpdatedSuccesBg1 {
        display: flex;
        width: 100%;
        padding: 8px 7px;
        border-radius: 16px;
        border: solid 1px rgba(255, 255, 255, 0.15);
        background-color: rgba(255, 255, 255, 0.04);
        font-family: Inter;
        font-size: 12px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: normal;
        text-align: center;
        color: #dbdcde;
        margin-bottom: 32px;
    }

    .btnSectionOrder {
        width: 100%;

        .btn1 {
            width: 100%;
            height: 50px;
            align-self: stretch;
            flex-grow: 0;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            padding: 8px 0 0;
            border-radius: 10px;
            background-color: #222329;
            font-family: Syncopate;
            font-size: 14px;
            font-weight: bold;
            line-height: 1.3;
            letter-spacing: -0.56px;
            text-align: center;
            color: #fff;
            text-transform: uppercase;
            margin-bottom: 12px;

            &:hover {
                background-color: #fff;
                color: #0f0f14;

                span {
                    &:last-child {
                        color: #0f0f14;
                    }
                }
            }

            span {
                &:last-child {
                    font-family: Inter;
                    font-size: 12px;
                    font-weight: normal;
                    line-height: 1.3;
                    letter-spacing: normal;
                    text-align: center;
                    color: rgba(255, 255, 255, 0.4);
                    text-transform: none;
                }
            }
        }

        .btn2 {
            width: 100%;
            height: 50px;
            align-self: stretch;
            flex-grow: 0;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            padding: 8px 0 0;
            border-radius: 10px;
            background-color: #222329;
            font-family: Syncopate;
            font-size: 14px;
            font-weight: bold;
            line-height: 1.3;
            letter-spacing: -0.56px;
            text-align: center;
            color: #fff;
            text-transform: uppercase;

            &:hover {
                background-color: #fff;
                color: #0f0f14;
            }
        }
    }
}

.fulfillmentMain {
    .fulfillmentNote {
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: normal;
        text-align: left;
        color: #fff;
        margin-bottom: 20px;
        display: flex;
    }
    .fulfillmentNote1{
        margin-bottom: 55px;
    }


}

.btmSection {
    display: flex;
    align-items: center;
    flex-direction: column;
    .proceedAddLine {
        font-family: Inter;
        font-size: 20px;
        font-weight: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: center;
        color: #fff;
        display: flex;
        justify-content: center;
        padding: 0px 29px;
    }

    .btnProceed {
        width: 100%;
        height: 50px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        border-radius: 10px;
        box-shadow: 0 -4px 4px 0 rgba(0, 0, 0, 0.8);
        background: linear-gradient(236.5deg, rgba(255, 119, 89, 0.1) 0%, rgba(255, 83, 45, 0.1) 100%);
        position: relative;
        z-index: 9;
        margin: 24px 0px 16px 0px;

        span {
            background-image: linear-gradient(316deg, #ff7759 130%, #ff532d -17%);
            font-family: Syncopate;
            font-size: 18.5px;
            font-weight: bold;
            font-stretch: normal;
            line-height: 1.3;
            letter-spacing: -0.74px;
            text-align: left;
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            text-transform: uppercase;
        }
    }

    .btnCancel {
        font-family: Syncopate;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.61;
        letter-spacing: 0.56px;
        text-align: center;
        color: #8c8b99;
        display: flex;
        justify-content: center;
    }
}