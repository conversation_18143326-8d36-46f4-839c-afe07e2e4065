.reminderPopup {
  .dialogContent {
    max-width: 761px;
    width: 100%;
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding: 56px 26px;
    object-fit: contain;
    border-radius: 50px;
    background-color: #222329;
    position: relative;

    .textLsines {
      margin-bottom: 15px;
      margin-left: 20px;
    }

    .closeIcon {
      position: absolute;
      top: 24px;
      right: 24px;
      cursor: pointer;
      opacity: 0.5;
      &:hover{
        opacity: unset;
      }
    }

    .reminderPopupTitle {
      .titleText {
        font-family: Syncopate;
        font-size: 24px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: -0.96px;
        text-align: center;
        color: #fff;
        margin-bottom: 16px;
      }

      .titleSmallText {
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: 0.56px;
        text-align: left;
        color: #9b9eac;
      }
    }
    .reminderPopupNote{
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: 0.56px;
      text-align: left;
      color: #9b9eac;
      margin-top: 24px;
      margin-bottom: 24px;
    }

    .FormInputGroup {
      display: flex;
      margin-bottom: 8px;

      .lblInput {
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.6;
        text-align: left;
        color: #fff;
        height: 34px;
        display: flex;
        flex: 0 0 152px;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        padding: 6px 4px 6px 10px;
        border: solid 0.5px #000;
        background-color: rgba(0, 0, 0, 0.25);
        border-radius: 4px 0px 0px 4px;
        border-right: 0px;
      }

      .lblAdress {
        flex: 0 0 152px;
        height: 68px;
        align-items: baseline;
      }

      .inputSection {
        height: 34px;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: flex-start;
        padding: 6px 6px 6px 10px;
        border: solid 0.5px #000;
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 0px 4px 4px 0px;
        flex: 1 auto;

        &.comanyName.comanyName{
          font-family: Inter;
          font-size: 14px;
          font-weight: normal;
          text-align: left;
          color: #fff;
          padding: 0px;
          .comanyNameInput1.comanyNameInput1{
              padding: 6px 6px 6px 10px;
            }
        }

        &.companyState {
          flex: 0 0 96px
        }

        &.phoneNo {
          width: 128px;
        }

        &.bdrRadius0 {
          border-radius: 0px;
        }

        &.bdrTopRightRadius0 {
          border-top-right-radius: 0px;
        }

        &.bdrBtmRightRadius0 {
          border-bottom-right-radius: 0px;
        }

        &.bdrRight0 {
          border-right: 0px;
        }

        &.bdrBtm0 {
          border-bottom: 0px;
        }

        input {
          font-family: Inter;
          font-size: 14px;
          font-weight: normal;
          text-align: left;
          color: #fff;
          border: 0px;
          background-color: transparent;
          padding: 0px;
          width: 100%;

          &:focus {
            outline: none;
            box-shadow: none;
          }

          &::placeholder {
            color: rgba(255, 255, 255, 0.5);
            font-weight: normal;
          }
        }
      }

      &.FormInputGroupError {
        .lblInput {
          border: solid 0.5px #f00;
          background-color: #f00;
          cursor: pointer;
          white-space: nowrap;
        }

        .borderOfError {
          border: solid 0.5px #f00;
        }
      }

      .cityInput {
        flex: 0 0 128px;
      }

      .zipCodeInput {
        flex: 0 0 96px;
        padding: 6px 3px 6px 6px;

      }

    }

    .disclaimer {
      padding: 16px 24px;
      border-radius: 4px;
      background-color: #d9d9d9;
      font-family: Inter;
      font-size: 20px;
      font-weight: 600;
      line-height: 1.6;
      color: #000;
      text-align: center;
      position: relative;
      margin-bottom: 24px;
    }

    .noteText {
      margin-top: 10px;
    }

    .settingBtnMain {
      display: flex;
      justify-content: center;
      margin-top: 20px;

      .saveSettingsBtn {
        transition: all 0.1s;
        font-family: Inter;
        font-size: 16px;
        line-height: 1.6;
        text-align: center;
        color: #70ff00;
        background-color: transparent;
        border: 0;
        opacity: 0.7;

        &:disabled {
          cursor: not-allowed;
          color: #fff !important;
          opacity: 0.5 !important;
        }

        &:hover {
          color: #70ff00;
          opacity: unset;
        }
      }
    }
  }
}

.Dropdownpaper.Dropdownpaper {
  padding: 3px 4px 8px 8px;
  -webkit-backdrop-filter: blur(30px);
  backdrop-filter: blur(30px);
  box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
  background-color: rgba(255, 255, 255, 0.3);
  margin-top: 7px;
  overflow: hidden;
  width: 96px;
  border-radius: 0px 0px 4px 4px;
  margin-left: -3px;
  background: url(../../../assets/images/DropDownBG.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;
  ul {
      overflow: auto;
      max-height: 230px;
      padding-right: 4px;
      padding-top: 0px;
      padding-bottom: 0px;

      &::-webkit-scrollbar {
          width: 6px;
          height: 6px;
      }

      li {
          font-family: Inter;
          font-size: 14px;
          font-weight: normal;
          line-height: 1.4;
          text-align: left;
          color: #fff;
          border-radius: 2px;
          padding: 3px 5px;
          margin-bottom: 2px;

          &:hover {
              background-color: #fff;
              color: #000;
          }

          &[aria-selected="true"] {
              background-color: #fff;
              color: #000;
              border-radius: 2px;
          }
      }
  }
}

.containerPopup{
  width: 100%;
}

.successPopup{
  .dialogContent {
    max-width: 300px;
    width: 100%;
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding: 45px 24px 40px 24px;
    object-fit: contain;
    border-radius: 10px;
    -webkit-backdrop-filter: blur(24px);
    backdrop-filter: blur(24px);
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
    background-color: rgba(0, 0, 0, 0.72);
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: center;
    color: #fff;
  }
  .closeIcon {
    position: absolute;
    top: 10px;
    right: 12px;
    cursor: pointer;
    opacity: 0.5;

    &:hover {
      opacity: unset;
    }

    svg {
      height: 20px;
      width: 20px;
      color: white;

      path {
        fill: #fff
      }
    }
  }
  .successPopupMain{
    width: 100%;
    text-align: center;
  }
  .successText{
    font-family: Inter;
    font-size: 18px;
    line-height: 1.6;
    text-align: center;
    color: #70ff00;
  }
  .btnsuccess{
    margin-top: 20px;
    margin-left: auto;
    margin-right: auto;
    width: 85px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    padding: 8px 24px;
    border-radius: 4px;
    border: solid 0.5px #fff;
    background-color: transparent;
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: center;
    color: #fff;
    transition: all 0.1s;
    &:hover{
      background-color: #70ff00;
      border: solid 0.5px #70ff00;
      color: #000;
    }
  }
}

.errorMessage {
  padding: 4px 20px;
  border-radius: 50px;
  border: solid 1px #ff5d47;
  background-color: #ffefed;
  font-family: Inter;
  font-size: 12px;
  line-height: 1.2;
  text-align: left;
  color: #ff5d47;
  display: flex;
  align-items: center;
  gap: 4px;
  position: absolute;
  left: 160px;
  top: 10px;
  svg{
    width: 20px;
    height: 20px;
  }
}

.autocompleteDescPanel {
  border-radius: 4px;
}

.autocompleteDescInnerPanel.autocompleteDescInnerPanel {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  -webkit-backdrop-filter: blur(24px);
  backdrop-filter: blur(24px);
  background-color: #ffffff4c;
  box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
  padding-right: 4px;
  background: url(../../../assets/images/DropDownBG.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;
  border-radius: 0px 0px 4px 4px;
  margin-top: 1px;
}

.listAutoComletePanel.listAutoComletePanel {
  width: 100%;
  max-height: 300px;
  padding: 6px 4px 6px 10px;
  margin-top: 4px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  li {
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.4;
    text-align: left;
    color: #fff;
    box-shadow: none;
    padding: 6px 8px;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 4px;
    border-radius: 2px;

    &:hover {
      background-color: #fff;
      color: #000;
    }

    &[aria-selected="true"] {
      background-color: #fff !important;
      color: #000;
    }
  }
}

.companyInput {
  width: 100%;
  height: 100%;
  padding: 6px 6px 6px 10px;
  color: #fff;
  resize: none;
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  line-height: 1.4;
  text-align: left;

  &::placeholder {
    color: #bbb;
  }

  &:focus-within {
    border: solid 1px #70ff00;
    outline: none;
    box-shadow: none;
    border-radius: 0px 2px 2px 0px;
  }
}

.formGroupInput {
  display: flex;
  height: 56px;
  display: flex;
  justify-content: flex-start;
  border-bottom: solid 1px rgba(255, 255, 255, 0.07);

  &.bdrBtm0 {
    border-bottom: 0px;
  }

  label {
    font-family: Syncopate;
    font-size: 18px;
    font-weight: bold;
    line-height: 1.3;
    letter-spacing: -0.72px;
    text-align: left;
    color: #71737f;
    text-transform: uppercase;
  }

  .focusLbl {
    color: #fff;
  }

  .col1 {
    flex: 1;
    display: flex;
    align-items: center;
    position: relative;

    &:nth-child(2) {
      flex: 0 50%;
    }
  }

  .inputWrapper {
    display: flex;
    align-items: center;
    width: 100%;
  }

  .inputCreateAccount {
    width: 100%;
    height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding: 6px 16px;
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.04);
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: 0.72px;
    text-align: left;
    color: #fff;
    transition: background 0.1s;

    &.arBryzosCom {
      color: rgba(255, 255, 255, 0.4);

      &:focus {
        background: rgba(255, 255, 255, 0.04);
        color: rgba(255, 255, 255, 0.4);
      }
    }

    &.sendInvoiceEmailInput {
      text-overflow: ellipsis;
    }

    &.error {
      background: url(../../../assets/images/Create-Account/error-input.svg) no-repeat;
      background-size: cover;
      box-shadow: none;

      &:focus {
        background-color: green;
        background: url(../../../assets/images/Create-Account/error-input.svg) no-repeat;
        background-size: cover;
        color: #fff;
      }
    }

    &:focus {
      outline: none;
      background: url(../../../assets/images/Create-Account/input-active.svg) no-repeat;
      background-position: bottom right;
      background-size: cover;
    }

    &:disabled {
      cursor: not-allowed;
    }
  }
}
.companyHQAddressContainer{
  height: 160px;
  
  .customAddressContainer {
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 4px;
    padding: 8px;
    border-radius: 12px;
    box-shadow: inset 5px 5px 7.9px -2px #000;
    background-color: rgba(255, 255, 255, 0.04);

    input {
      width: 100%;
      font-size: 14px;
      height: 32px;
      border-radius: 8px;
      padding: 7px 12px;
      &::placeholder{
          font-family: Syncopate;
          font-size: 14px;
          font-weight: normal;
          font-stretch: normal;
          font-style: normal;
          line-height: normal;
          letter-spacing: 0.56px;
          text-align: left;
          color: #616575;
          text-transform: uppercase;
      }
    }

    .zipInputContainer {
      display: flex;
      gap: 4px;

      .col1 {
        flex: 0 0 1;
      }

      .col2{
        flex: 0 0 77px;
        input{
          width: 100%;
        }
      }
       .yourLocationAdressState{
        height: 32px;
      }
      .col3 {
       flex: 0 0 104px;
      }
    }
  }
}
.locationAddressContainer {
  height: 100%;
  .addressDisplayContainer{
    // height: 100%;
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.04);
    width: 100%;
    .placeHolderDiv {
      padding: 13px 16px;
      font-family: Inter;
      font-size: 12px;
      font-weight: 300;
      font-stretch: normal;
      font-style: normal;
      line-height: 1;
      letter-spacing: -0.48px;
      text-align: left;
      color: #71737f;
      text-transform: uppercase;
      width: 100%;
      height: 100%;
      display: block;
    }
    .valueDiv {
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1;
      letter-spacing: 0.56px;
      text-align: left;
      color: #fff;
      display: flex;
      flex-direction: column;
      row-gap: 4px;
      padding:8px;
      height: 100%;
      &.placeHolderDiv{
        height: 120px;
        padding: 15px 16px;
        color: #616575;
        font-family: Syncopate;
        text-transform: uppercase;
      }
      .addressInputs{
        width: 100%;
        height: 32px;
        align-self: stretch;
        flex-grow: 0;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        padding: 7px 12px;
      }
      .lastAddressFiled {
        display: flex;
        column-gap: 4px;
        .addressInputsCol1,.addressInputsCol2,.addressInputsCol3 {
          height: 32px;
          align-self: stretch;
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: center;
          padding: 7px 12px;
        }
          .addressInputsCol1 {
            flex: 1;
          }

          .addressInputsCol2{
            flex: 0  77px;
          }
          .addressInputsCol3 {
          flex: 0  104px;
          }
      }
    }
  }
  .customAddressContainer {
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 4px;
    padding: 8px;
    border-radius: 12px;
    box-shadow: inset 5px 5px 7.9px -2px #000;
    background-color: rgba(255, 255, 255, 0.04);

    input {
      width: 100%;
      font-size: 14px;
      height: 32px;
      border-radius: 8px;
      padding: 7px 12px;
      &::placeholder{
          font-family: Syncopate;
          font-size: 14px;
          font-weight: normal;
          font-stretch: normal;
          font-style: normal;
          line-height: normal;
          letter-spacing: 0.56px;
          text-align: left;
          color: #616575;
          text-transform: uppercase;
      }
    }

    .zipInputContainer {
      display: flex;
      gap: 4px;

      .col1 {
        flex: 0 0 1;
      }

      .col2{
        flex: 0 0 77px;
        input{
          width: 100%;
        }
      }
       .yourLocationAdressState{
        height: 32px;
      }
      .col3 {
       flex: 0 0 104px;
      }
    }
  }
}
.saveButton {
  border-radius: 500px;
  
  width: 140px;
  height: 40px;
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: center;
  background-color: #16b9ff;
  color: #0f0f14;
  &:disabled{
    background-color: rgba(255, 255, 255, 0.04);
    color: rgba(255, 255, 255, 0.3);
  }
}
.fadeLoaderOpen{
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}
.fadeLoaderMessage {
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  line-height: 1;
  letter-spacing: 0.035rem;
  text-align: left;
  color: #2abcfb;
  transition: background 0.1s;
  caret-color: #fff;
  margin-left: 20px;
}
.fadeLoaderMessageSaved {
  color: #50ff50;
  opacity: 0;
  transition: opacity 1.5s ease-out;
}
.saveButtonContainer {
  display: flex;
  align-items: center;
  justify-content: right;
  gap: 20px
}
