// @ts-nocheck
import { getChannelWindow, useGlobalStore } from "@bryzos/giss-ui-library";
import { useMutation } from "@tanstack/react-query";
import axios from "axios";

const useGetAllProducts = () => {
  
  return useMutation(async () => {
    let url = `${import.meta.env.VITE_API_SERVICE}/reference-data/v2/products`;
    const response = await axios.get(url);
    return response.data;
  },
  {
    retry: false,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    staleTime: 0,
    onError(error) {
      const store = useGlobalStore.getState();
      if (error.code === 'ERR_NETWORK_CHANGED' || error.code === "ERR_NETWORK" || error.code === 'ECONNABORTED' || error.message.includes('Network')) {
        store.setApiFailureDueToNoInternet(true);
      }else{
        return error
      }
    }
  }
  );
};

export default useGetAllProducts;
