.closeButton {
  position: absolute;
  top: 28px;
  right: 30px;

  background: none;
  border: none;
  cursor: pointer;
}

.header {
  text-align: center;
  margin-bottom: 32px;
}

.title {
  font-family: Syncopate;
  font-size: 24px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.3;
  letter-spacing: -0.96px;
  text-align: center;
  color: #fff;
  text-transform: uppercase;
  margin-bottom: 12px;
}

.poNumber {
  font-family: Syncopate;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-align: center;
  color: #fff;
  margin: 0;
  text-transform: uppercase;

  span {
    font-weight: bold;

  }
}

.emailSection {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.emailIcon {
  display: flex;
}

.emailText {
  font-family: Syncopate;
 font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.3;
  letter-spacing: 0.98px;
  text-align: left;
  color: #fff;
}

.emailAddress {
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.3;
  letter-spacing: 0.56px;
  text-align: left;
  color: #fff;
  margin-top: 1.7px;
}

.statusSection {
  margin-bottom: 32px;
}

.statusText,
.instructionText {
  font-family: Inter;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.3;
  letter-spacing: normal;
  text-align: left;
  color: #dbdcde;
}

.instructionText {
  margin-top: 8px;
}

.poEmail {
  color: #16b9ff;
  text-decoration: none;
}

.accessBox {
  padding: 24px 20px 24px 20px;
  border-radius: 16px;
  background-color: rgba(255, 255, 255, 0.08);
  margin-bottom: 24px;
}

.accessText {
  font-family: Inter;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.3;
  letter-spacing: -0.16px;
  text-align: left;
  color: #c3c4ca;
  margin-bottom: 16px;
}

.orderManagementLink {
  color: #4a9eff;
  cursor: pointer;
  font-weight: 600;

  &:hover {
    text-decoration: underline;
  }
}

.actionsGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.actionColumn {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.actionItem {
  display: flex;
  align-items: center;
  gap: 12px;
  font-family: Inter;
  font-size: 14px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.3;
  letter-spacing: normal;
  text-align: left;
  color: #fff;
  transition: all 0.2s ease;
}

.sellerActionItem {
  margin-top: 12px;
}

.arrowIcon {
  display: flex;
}

.feedbackSection {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.feedbackText {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  flex: 1;
}

.feedbackButtons {
  display: flex;
  gap: 12px;
}

.feedbackButton {
  background: none;
  border: 2px solid #666;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #4a9eff;
    transform: scale(1.1);
  }

  &.selected {
    border-color: #4a9eff;
    background-color: rgba(74, 158, 255, 0.2);
  }

  &.thumbsUp:hover {
    border-color: #4caf50;
  }

  &.thumbsDown:hover {
    border-color: #f44336;
  }
}

.createAnotherButton {
  width: 100%;
  border-radius: 12px;
  background-image: linear-gradient(119deg, #1c40e7 -12%, #16b9ff 109%);
  color: #fff;
  border: none;
  border-radius: 12px;
  padding: 18px 24px;
  font-family: Syncopate;
  font-size: 20px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.3;
  letter-spacing: -0.8px;
  text-align: center;
  color: #fff;
  text-transform: uppercase;
  cursor: pointer;
  transition: all 0.2s ease;

}


.purchaseRating {
  width: 100%;
  flex-direction: row;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0px;
  gap: 16px;
  background-size: cover;
  margin-bottom: 40px;

  p {
    font-family: Syncopate;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.3;
    letter-spacing: -0.14px;
    text-align: left;
    color: #c3c4ca;
    text-transform: uppercase;
  }

  .GiveUsthumbsUp {
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: italic;
    line-height: 1.3;
    letter-spacing: normal;
    text-align: center;
    color: #c3c4ca;
    text-transform: none;
  }

  .purchaseRatingBtn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
}

.thumbsImages {
  display: flex;
  .img2 {
    display: none;
  }

  &:not(.thumbsImagesActive):hover {
    .img1 {
      display: none;
    }

    .img2 {
      display: block;
    }
  }

  &.thumbsImagesActive{
    .img1 {
      display: none;
    }

    .img2 {
      display: block;
    }
  }
}