.container{
    position: absolute;
    inset: 0;
    border-radius: 16px;
    z-index: 100000;
    flex-direction: column;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-image: linear-gradient(to bottom, #0f0f14, #0f0f14), linear-gradient(179deg, #fff -12%, #1a1b21 28%);
    width: 100%;
    .noInternetBody{
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .no_internet_btn{
        display: flex;
        min-width: 220px;
        justify-content: end;
        span{
            padding: 10px 10px 10px 0;
            cursor: pointer;
        }
    }
    .main_content{
      width: 560px;
      height: 563px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-image: url('../../assets/images/noInternetBackground.svg');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      padding: 60px 15px 40px 15px;
        .content {
          font-family: Inter;
          font-size: 18px;
          font-weight: normal;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.3;
          letter-spacing: normal;
          text-align: center;
          color: rgba(255, 255, 255, 0.8);
            // margin: 0 0 4vw 0;
            // color: #fff;
            // opacity: 0.7;
          }
      
          .header {
            color: #fff;
            font-family: Syncopate;
            font-size: 24px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.4;
            letter-spacing: -0.96px;
            text-align: left;
            color: #fff;
            margin-bottom: 8px;
          }
          .try_again_btn {
            gap: 8px;
            padding: 14px 12px;
            border-radius: 10px;
            background-color: #fff;
            border: none;
            outline: none;
            font-family: Syncopate;
            font-size: 18px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: -0.72px;
            text-align: center;
            color: #0f0f14;
            cursor: pointer;
            margin-top: 40px;
            width: 220px;
          }
    }
}
.flex_center{
    display: flex;
    align-items: center;
}

.noInternetIcon{
  width: 104px;
  height: 104px;
  flex-grow: 0;
  object-fit: contain;
  margin-bottom: 24px;
}

.bgEllips {
  position: absolute;
  top: 0px;
  left: 0px;
  opacity: 0.56;
  -webkit-filter: blur(100px);
  filter: blur(100px);
  background-image: conic-gradient(from 0.25turn, #fff, rgba(255, 255, 255, 0) 0.19turn, #fff 0.4turn, rgba(255, 255, 255, 0) 0.55turn, #fff 0.76turn, rgba(255, 255, 255, 0) 0.97turn, #fff), linear-gradient(147deg, #e7ecef 75%, rgba(231, 236, 239, 0) 200%), linear-gradient(to bottom, #9786ff, #9786ff);
  width: 231.9px;
  height: 54.6px;
}
.noInternetHeader {
  height: 120px;
  width: 100%;
  display: flex;
  justify-content: left;
  align-items: center;
  padding-left: 32px;
  background-color: #0f0f14;
  position: relative;
  overflow: hidden;
  border-radius: 16px 16px 16px 16px;
  background-image: url(../../assets/images/Header-No-Internet.svg);
  background-size: cover;
  background-position: center;
}
.bryzosName {
  font-family: Syncopate;
      font-size: 28px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1;
      letter-spacing: -1.12px;
      text-align: left;
      color: #fff;
      padding-bottom: 58px;
}