import React from 'react';
import styles from './StateSelector.module.scss';
import clsx from 'clsx';

interface StateSelectorProps {
  allStates: Array<{ id: number | string; code: string }>;
  selectedStates: Array<number | string>;
  onChange: (selectedStates: Array<number | string>) => void;
}

const StateSelector: React.FC<StateSelectorProps> = ({
  allStates,
  selectedStates,
  onChange,
}) => {
  const handleStateToggle = (stateId: number | string) => {
    const newSelection = selectedStates.includes(stateId)
      ? selectedStates.filter((id) => id !== stateId)
      : [...selectedStates, stateId];
    onChange(newSelection);
  };

  return (
    <div className={styles.container}>
      {allStates.map((state) => (
        <button
          type="button"
          key={state.id}
          className={clsx(
            styles.stateItem,
            selectedStates.includes(state.id) && styles.selected
          )}
          onClick={() => handleStateToggle(state.id)}
        >
          {state.code}
        </button>
      ))}
    </div>
  );
};

export default StateSelector;
