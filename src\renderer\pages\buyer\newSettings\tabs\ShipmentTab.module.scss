.shipmentTabContentContainer {
  padding: 32px 0px 24px 0px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 5px;
    height: 6px;
  }

  .shipmentTabContentHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0px 26px 20px 26px;

    h2 {
      font-family: Syncopate;
      font-size: 18px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.2;
      letter-spacing: -0.72px;
      color: #fff;
    }

    button {
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: normal;
      color: rgba(255, 255, 255, 0.85);
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }

  .shipmentTabContentBody {
    padding: 0px 26px;
    position: relative;

    .shipmentTabContent {
      display: flex;
      align-items: center;
      padding: 8px 0;
      border-bottom: solid 1px rgba(255, 255, 255, 0.07);

      .shipmentTabContentTitle {
        width: 50%;
        font-family: Syncopate;
        font-size: 18px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: -0.72px;
        text-align: left;
        color: #71737f;
        text-transform: uppercase;
      }

      .focusLbl {
        color: #fff;
      }

      .shipmentTabContentValue {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 5px;
      }

      .locationAddressContainer {
        height: 100%;

        .addressDisplayContainer {
          height: 100%;
          border-radius: 12px;
          background-color: rgba(255, 255, 255, 0.04);
          width: 100%;

          .placeHolderDiv {
            padding: 13px 16px;
            font-family: Inter;
            font-size: 12px;
            font-weight: 300;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: -0.48px;
            text-align: left;
            color: #71737f;
            text-transform: uppercase;
            width: 100%;
            height: 100%;
            display: block;
          }

          .valueDiv {
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: 0.56px;
            text-align: left;
            color: #fff;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 23px 15px;
            height: 100%;

            .lastAddressFiled {
              display: flex;
              gap: 50px;

              .addressInputsCol1 {
                flex: 1;
              }
            }
          }

          .placeHolderDiv {
            height: 120px;
            padding: 15px 16px;
            color: #616575;
            font-family: Syncopate;
            text-transform: uppercase;
          }
        }
      }

    }
    .deliveryApptRequiredContainer {
      opacity: 0.2;
      pointer-events: none;
    }
  }
}
.inputCreateAccount {
  width: 100%;
  height: 40px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  padding: 6px 16px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.04);
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: 0.56px;
  text-align: left;
  color: #fff;
  transition: background 0.1s;

  &.arBryzosCom {
    color: rgba(255, 255, 255, 0.4);

    &:focus {
      background: rgba(255, 255, 255, 0.04);
      color: rgba(255, 255, 255, 0.4);
    }
  }

  &.sendInvoiceEmailInput {
    text-overflow: ellipsis;
  }

  // &.error {
  //   background: url(../../../../assets/images/Create-Account/error-input.svg)
  //     no-repeat;
  //   background-size: cover;
  //   box-shadow: none;

  //   &:focus {
  //     background-color: green;
  //     background: url(../../../../assets/images/Create-Account/error-input.svg)
  //       no-repeat;
  //     background-size: cover;
  //     color: #fff;
  //   }
  // }

  &:focus {
    outline: none;
    color: #459fff;
  }

  &:disabled {
    cursor: not-allowed;
  }
}
.stripePaymentGrid {
  column-gap: 12px;
  flex: 1;
  display: flex;
  align-items: center;
  position: relative;
}
.stripeElement {
  width: 100%;
  height: 40px;
  flex-grow: 0;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  padding: 0 12px 0 16px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.04);

  &>div {
    width: 100%;
  }

  /* Extra styling to ensure the iframe is visible */
  iframe {
    opacity: 1 !important;
    height: 24px !important;
    min-height: 24px !important;
    width: 100% !important;
  }
}
.companyHQAddressContainer {
  height: 160px;


  .customAddressContainer {
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 4px;
    padding: 8px;
    border-radius: 12px;
    box-shadow: inset 5px 5px 7.9px -2px #000;
    background-color: rgba(255, 255, 255, 0.04);

    input {
      width: 100%;
      font-size: 14px;
      height: 32px;
      border-radius: 8px;
      padding: 7px 12px;

      &::placeholder {
        font-family: Syncopate;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: 0.56px;
        text-align: left;
        color: #616575;
        text-transform: uppercase;
      }
    }

    .zipInputContainer {
      display: flex;
      gap: 4px;

      .col1 {
        flex: 1;
      }

      .col2 {
        flex: 0 0 77px;

        input {
          width: 100%;
        }
      }

      .col3 {
        flex: 0 0 104px;
      }
    }
  }
}
.receivingHoursInput {
  height: 100px;
  padding: 8px 0px;

  .lblInput {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    border-right: solid 1px #000;

    .lblReceivingHours {
      font-size: 12px;
      padding-left: 6px;
    }
  }

  .inputSectionRecevingHours {
    width: 100%;
    height: 100%;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    align-items: center;

    label {
      cursor: pointer;

      &:hover {
        color: #fff;
      }
    }

    input[type='checkbox'] {
      display: none;
    }

    .daylbl1 {
      font-family: Syncopate;
      font-size: 12px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1;
      letter-spacing: normal;
      text-align: center;
      color: #c3c4ca;
      text-transform: uppercase;
      margin-bottom: 4px;
    }
  }
}
.footerContainer {
  display: flex;
  padding: 32px 26px 0px;

  button {
    padding: 8px 42px 8px 41px;
    border-radius: 500px;
    background-color: rgba(255, 255, 255, 0.04);
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: center;
  }
  .saveBtnContainer {
    margin-left: auto;
  }
  .saveBtn {
    color: rgba(255, 255, 255, 0.5);
    margin-left: 20px;

    &:disabled {
      color: rgba(255, 255, 255, 0.5);
    }

    &:hover {
      color: #fff;

      &:disabled {
        color: rgba(255, 255, 255, 0.5);
      }
    }

    &:focus {
      color: #fff;
      outline: 0.5px solid #fff;
    }
  }
  .fadeLoaderMessage {
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    line-height: 1;
    letter-spacing: 0.035rem;
    text-align: left;
    color: #2abcfb;
    transition: background 0.1s;
    caret-color: #fff;
  }
  .saveBtnFadeLoaderMessage {
    color: #32ff6c;
    opacity: 0;
    transition: opacity 1.5s ease-out;
  }
  .saveBtnResaleCert {
    background-color: #2abcfb;
    color: #000;
    margin-left: 20px;

    &:disabled {
      color: rgba(255, 255, 255, 0.5);
      background-color: rgba(255, 255, 255, 0.04);
    }

    &:hover {
      color: #000;

      &:disabled {
        color: rgba(255, 255, 255, 0.5);
      }
    }

    &:focus {
      color: #000;
      outline: 0.5px solid #fff;
    }
  }

  .deleteBtn {
    color: #ff4848;
  }
}
.deleteDialogContainer {
  width: 504px;
  height: 411px;
  background-color: #0f0f14;
  border-radius: 50px;
  padding: 64px 64px 48px 64px;

  .deleteDialogTitle {
    font-family: Syncopate;
    font-size: 24px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.3;
    letter-spacing: -0.96px;
    text-align: center;
    color: #fff;
    text-transform: uppercase;
    padding-bottom: 48px;
  }

  .deleteBtnSection {
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
    justify-content: center;

    button {
      width: 352px;
      height: 69px;
      border-radius: 50px;
      font-family: Syncopate;
      font-size: 18px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: -0.72px;
      text-align: center;
      text-transform: uppercase;
    }

    .submitYesBtn {
      background-color: #32ff6c;
      color: #000;

      &:hover {
        background-color: #2b2c32;
        color: #32ff6c;
      }
    }

    .submitNoBtn {
      background-color: #2b2c32;
      color: #fff;

      &:hover {
        color: #32ff6c;
      }
    }
  }
}
.Dropdownpaper.Dropdownpaper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  gap: 8px;
  padding: 4px;
  border-radius: 6.2px;
  -webkit-backdrop-filter: blur(15.4px);
  backdrop-filter: blur(15.4px);
  background-color: rgba(113, 115, 127, 0.7);
  padding: 0px;
  margin-top: 4px;

  .muiMenuList {
    padding: 4px;

    li {
      padding: 4px 16px;
      border-radius: 8px;
      font-family: Inter;
      font-size: 10.8px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: normal;
      text-align: left;
      color: rgba(255, 255, 255, 0.7);
      background-color: transparent;

      &:hover {
        border-radius: 4.6px;
        background-color: rgba(255, 255, 255, 0.4);
        color: #000;
        font-weight: bold;
      }
    }
  }

  &.Dropdownpaper1 {
    width: auto;
    overflow: hidden;
    padding-right: 5px;
    padding-top: 4px;
    padding-bottom: 4px;

    ul {
      max-height: 220px;
      overflow: auto;

      &::-webkit-scrollbar {
        width: 1.5px;
      }

      li {
        font-size: 14px;
        margin-right: 4px;
      }
    }
  }

  &.resaleCertdropdown {
    width: auto;
    overflow: hidden;
    padding-right: 5px;
    padding-top: 4px;
    padding-bottom: 4px;

    ul {
      max-height: 220px;
      overflow: auto;

      &::-webkit-scrollbar {
        width: 1.5px;
      }

      li {
        font-size: 14px;
        margin-right: 4px;
      }
    }
  }

  &.receivingHoursTop {
    padding-right: 3px;
    margin-top: 3px;
    border-radius: 6px 6px 6px 6px;

    ul {
      &::-webkit-scrollbar {
        width: 1.5px;
      }

      li {
        font-size: 12px;
        margin-right: 0px;
        padding: 4.6px 7px;
        border-radius: 4px;
      }
    }
  }

  &.receivingHoursBottom {
    padding-right: 3px;
    margin-top: 2px;
    border-radius: 6px 6px 6px 6px;

    ul {
      &::-webkit-scrollbar {
        width: 1.5px;
      }

      li {
        font-size: 12px;
        margin-right: 0px;
        padding: 4.6px 7px;
        border-radius: 4px;
      }
    }
  }
}
.dropDownBG.dropDownBG {
  width: 110px;
  z-index: 999;
  padding: 4px;
  border-radius: 8px;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  background-color: #9c9da5;
  margin-top: 10px;

  ul {
    padding: 0px;

    li {
      font-family: Inter;
      font-size: 14px;
      font-weight: 500;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: normal;
      text-align: center;
      color: #191a20;
      margin-bottom: 2px;

      &[aria-selected="true"] {
        border-radius: 6px;
        background-color: #e0e0e0;
      }

      &:hover {
        border-radius: 6px;
        background-color: #e0e0e0;
      }
    }
  }
}
.sameAsCompanyAddressButton {
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: left;
  color: #bff0b2;
}
.inputMain {
  position: relative;
  border-radius: 12px;

  &:focus-within {
     box-shadow: inset 3px 3px 7.4px 0 #000;
     input{
      background-color: transparent;
    }

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      border-radius: inherit;
      padding: 1.7px;
      background: linear-gradient(to bottom right, #1a1b20 61%, #fff 294%);
      -webkit-mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      mask-composite: exclude;
      background-clip: border-box;
      z-index: -1;
      pointer-events: none;
      left: 0;
      top: 0
    }
  }
}
.addresInputMain {
  position: relative;
  // z-index: 0;
  border-radius: 8px;

  &:focus-within {
    box-shadow: inset 3px 3px 7.4px 0 #000;
    input{
      background-color: transparent;
    }
    &::before {
      content: '';
      position: absolute;
      inset: 0;
      border-radius: inherit;
      padding: 1px;
      background: linear-gradient(to bottom right, #1a1b20 61%, #fff 294%);
      -webkit-mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      mask-composite: exclude;
      background-clip: border-box;
      z-index: -1;
      pointer-events: none;
      left: 0px;
      top: -0px;
    }
  }

}
.selectDropdown.selectDropdown {
  width: 100%;

  :global(.MuiSelect-select) {
    width: 100%;
    height: 40px;
    flex-grow: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    gap: 20px;
    padding: 0 0 0 16px;
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.04);
    border: 1px solid transparent;
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: 0.56px;
    text-align: center;
    color: #fff;

    // span {
    //     color: #616575;
    // }
  }

  &.selectState {
    :global(.MuiSelect-select) {
      height: 40px;
      font-size: 14px;
    }
  }

  :global(.MuiSelect-icon) {
    path {
      fill: rgba(255, 255, 255, 0.6);
      stroke-opacity: unset;
    }

    transform: none;
    top: unset;
    width: 19px;
    height: 19px;
    right:12px;
  }

  &:global(.Mui-focused) {
    :global(.MuiSelect-select) {
      // border: solid 1px #71737f;
      background-color: rgba(255, 255, 255, 0.04);
    }
  }

  :global(fieldset) {
    border-color: transparent !important;
  }
}
.uploadFileInput {
  display: none;
}
.uploadCert {
  font-family: Syncopate;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-align: center;
  color: #9b9eac;
  display: flex;
  align-items: center;
  gap: 5px;
  text-transform: uppercase;
  svg {
    path{
      fill: #9b9eac;
    }
  }
  &:hover {
    color: #fff;
    svg {
      path{
        fill: #fff;
      }
    }
  }
  &:focus {
    outline: none;
    color: #fff;
  }
}
.resaleCertFileContainer {
  display: flex;
  justify-content: space-between;
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-align: center;
  color: #fff;
  position: relative;
  width: 100%;
  align-items: center;
  a {
    position: absolute;
    text-decoration: none;
    left: -60px;
    font-family: Syncopate;
    color: #c3c4ca;
    text-transform: uppercase;

    &:hover {
      color: #fff;
    }
    &:focus {
      outline: none;
      color: #fff;
    }
  }
  .resaleCertFileName {
    padding-left: 16px;
  }
}
.sameAsCompanyAddressLabel {
  font-family: Inter;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: left;
  color: #71737f;
  text-transform: capitalize;
  margin-top: 12px;
}
.checkingThisbox {
  display: flex;
  align-items: flex-start;
  column-gap: 21.1px;

  .containerChk {
      display: inline-block;
      position: relative;
      cursor: pointer;
      padding-left: 12px;
      text-align: left;

      &:focus-within{
        .checkmark{
          border: 1px solid #fff;
          border-radius: 6px;
        }
      }

      input {
          position: absolute;
          opacity: 0;
          cursor: pointer;
          height: 0;
          width: 0;
      }

      .checkmark {
          position: absolute;
          top: 0px;
          left: 0;
          z-index: 1;
          width: 24px;
          height: 24px;
          background: url(../../../../assets/images/Create-Account/uncheckmark.svg) no-repeat;
          background-size: contain;
        

      }

      .checkmark:after {
          content: "";
          position: absolute;
          display: none;
      }

      input:checked~.checkmark {
          background: url(../../../../assets/images/Create-Account/checkmark.svg) no-repeat;
          background-size: contain;
      }

      input:checked~.checkmark:after {
          display: block;
      }

      input:disabled~.checkmark {
          opacity: 0.5;
          cursor: not-allowed;
      }

      input:disabled~.checkmark {
          background: url(../../../../assets/images/Create-Account/uncheckmark.svg) no-repeat;
          background-size: contain;
          opacity: 0.5;
      }

  }

  .lblChk {
      font-family: Inter;
      font-size: 16px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: normal;
      text-align: left;
      color: #dbdcde;
      transition: opacity 0.2s ease;
  }

  // Disabled state styling
  .containerChk input:disabled+.checkmark+.lblChk,
  .containerChk input:disabled~.lblChk {
      opacity: 0.5;
      color: rgba(219, 220, 222, 0.5);
  }
}
.fadeLoaderOpen {
  pointer-events: none;
  opacity: 0.5;
  cursor: not-allowed;
}