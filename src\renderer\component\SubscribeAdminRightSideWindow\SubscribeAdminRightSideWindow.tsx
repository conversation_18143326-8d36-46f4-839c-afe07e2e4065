import React from 'react'
import styles from './SubscribeAdminRightSideWindow.module.scss';

const SubscribeAdminRightSideWindow = () => {

    return (
        <div className={styles.RightWindowcontainer}>
            <div className={styles.paymentDetailsContainer}>
                <div className={styles.title}>
                    <span>PAYMENT <br /> DETAILS</span>
                </div>
                <div className={styles.paymentDetails}>
                    <div className={styles.paymentDetailsItem}>
                        <span className={styles.paymentDetailsItemTitle}>Next Debit:</span>
                        <span className={styles.paymentDetailsItemValue}>Sep 1, 2025</span>
                    </div>
                    <div className={styles.paymentDetailsItem}>
                        <span className={styles.paymentDetailsItemTitle}>Amount:</span>
                        <span className={styles.paymentDetailsItemValue}>$200.00</span>
                    </div>
                    <div className={styles.paymentDetailsItem}>
                        <span className={styles.paymentDetailsItemTitle}>For:</span>
                        <span className={styles.paymentDetailsItemValue}>4 Users</span>
                    </div>
                    <div className={styles.paymentDetailsItem}>
                        <span className={styles.paymentDetailsItemTitle}>Method:</span>
                        <span className={styles.paymentDetailsItemValue}>ACH Debit</span>
                    </div>
                    <div className={styles.paymentDetailsItem}>
                        <span className={styles.paymentDetailsItemTitle}>Ending In:</span>
                        <span className={styles.paymentDetailsItemValue}>xxxx 1234</span>
                    </div>
                </div>
                <button className={styles.paymentDetailsCancelButton}>
                    CANCEL SUBSCRIPTION
                </button>
                <button className={styles.paymentDetailsEditButton}>
                    <span>EDIT <br /> PAYMENT DETAILS</span>
                </button>
            </div>
            <div className={styles.adminControlContainer}>
                <div className={styles.title}>
                    <span>ADMIN <br /> CONTROLS</span>
                </div>
                <div className={styles.adminControl}>
                    <button className={styles.adminControlButton}>
                        BUY LICENSES
                    </button>
                    <button className={styles.adminControlButton}>
                        ADD USERS
                    </button>
                    <button className={styles.adminControlButton}>
                        REMOVE USERS
                    </button>
                    <button className={styles.adminControlButton}>
                        CREATE NEW GROUP
                    </button>
                    <button className={styles.adminControlButton}>
                        EDIT EXISTING GROUP
                    </button>
                    <button className={styles.adminControlButton}>
                        ASSIGN NEW ADMIN
                    </button>
                    <button className={styles.adminControlButton}>
                        UPLOAD USERS BY CSV
                    </button>
                </div>
            </div>
        </div>
    )
}

export default SubscribeAdminRightSideWindow
