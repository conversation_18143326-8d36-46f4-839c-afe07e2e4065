.newSetting {
    height: 100%;
    min-height: 800px;
    display: flex;
    flex-direction: column;
    width: 100%;
    // max-width: 741px;
    padding: 16px 16px 12px;
    background-color: #191a20;
    // margin: 0 auto;

}

.newSettingContent {
    display: flex;
    flex-direction: column;
    flex: 1;
    position: relative;
}

.saveButtonContainer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .settingTitle {
        font-family: Syncopate;
        font-size: 18px;
        font-weight: bold;
        line-height: 1.3;
        letter-spacing: -0.72px;
        text-align: right;
        color: #fff;
        text-transform: uppercase;
        margin-left: auto;
        height: 36px;
        display: flex;
        align-items: center;
    }
}
.fadeLoaderOpen{
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}
.fadeLoaderMessage {
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    line-height: 1;
    letter-spacing: 0.035rem;
    text-align: left;
    color: #2abcfb;
    transition: background 0.1s;
    caret-color: #fff;
    margin-left: 20px;
}
.fadeLoaderMessageSaved {
    color: #50ff50;
    opacity: 0;
    transition: opacity 1.5s ease-out;
}

.tabContentContainer{
    height: calc(100vh - 15.1875rem);
    max-height: calc(100vh - 15.1875rem);
}

.AppInfo{
    width: 100%;
    font-family: Inter;
    font-size: 12px;
    font-weight: normal;
    line-height: 1;
    letter-spacing: 0.035rem;
    text-align: center;
    color: #fff;
}