.orderContent {
    padding: 15px;
    margin: 0px auto;
    max-width: 800px;
    width: 100%;
    text-align: center;
    position: relative;
    background: url(../../assets/images/AppBG.svg) no-repeat center;

    .orderSearch {
        display: flex;
        width: 100%;
        height: 79px;
        align-items: center;
        padding-right: 16px;

        input {
            border: 0;
            padding: 0px 16px;
            font-family: Inter;
            font-size: 20px;
            line-height: 1.4;
            text-align: left;
            color: #fff;
            background-color: transparent;

            &::placeholder {
                color: rgba(255, 255, 255, 0.5);
            }

            &:focus-visible {
                outline: transparent;
            }
        }

        .btnOfOrderSearch {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 5px;

            button {
                font-family: Inter;
                font-size: 12px;
                font-weight: 300;
                line-height: 1.4;
                text-align: left;
                color: rgba(255, 255, 255);
                width: 80px;
                height: 21px;
                padding: 2px 0;
                border-radius: 2px;
                border: solid 0.5px rgba(255, 255, 255);
                background-color: transparent;
                text-align: center;
                opacity: 0.5;

                &:hover {
                    background-color: #70ff00;
                    opacity: unset;
                    color: #000;
                    border: 0.5px solid #000;
                    font-weight: normal;
                }

                &:focus {
                    border: 0.5px solid #70ff00;
                    opacity: unset;
                }
            }

            .activeBtn {
                background-color: #70ff00;
                opacity: unset;
                color: #000;
                border: 0.5px solid #000;
                font-weight: normal;
            }
        }
    }

    .orderHeading {
        height: 68px;
        display: flex;
        flex-direction: column;
        width: 100%;
        padding: 16px 12px;
        gap: 2px;
        box-shadow: 0 -16px 15.1px -11px rgba(0, 0, 0, 0.6);

        .bigHeading {
            font-family: Inter;
            font-size: 16px;
            font-weight: 600;
            line-height: 1.4;
            text-align: left;
            color: #fff;
            display: flex;
            gap: 4px;

            .aTag {
                font-family: Inter;
                font-size: 14px;
                font-weight: 300;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.4;
                letter-spacing: normal;
                text-align: left;
                color: rgba(255, 255, 255);
                padding: 1px 6px;
                border-radius: 2.8px;
                border: solid 0.5px rgba(255, 255, 255);
                background-color: rgba(0, 0, 0, 0.5);
                opacity: 0.5;
            }
        }

        .smallHeading {
            font-family: Inter;
            font-size: 14px;
            font-weight: 300;
            line-height: 1.4;
            text-align: left;
            color: #fff;
        }
    }

    .orderAcceptNote {
        text-align: left;
        font-family: Inter;
        font-size: 16px;
        font-weight: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: 0.48px;
        color: #fff;
        margin-bottom: 20px;
        padding: 0px 8px;

        p {
            margin-bottom: 8px;

            &:last-child {
                font-family: Inter;
                font-size: 14px;
                letter-spacing: 0.42px;
                color: rgba(255, 255, 255, 0.6);
                margin-bottom: 0px;
            }
        }
    }

    .listOfOrder {
        padding-right: 4px;

        ul {
            list-style: none;
            overflow-y: auto;
            height: 670px;

            &::-webkit-scrollbar {
                width: 8px;
                height: 6px;
            }

            .liOfOrder {
                width: 100%;
                padding: 16px 20px 29px 8px;
                border-radius: 16px;
                margin-bottom: 6px;


                &:hover {
                    background-color: rgba(255, 255, 255, 0.07);
                }

                .availOrderMain {
                    display: flex;
                    column-gap: 12px;
                    width: 100%;

                    .iconBox {
                        position: relative;
                        top: -3px;
                        display: flex;
                    }

                    .availOrderRightCol {
                        display: flex;
                        flex-direction: column;
                        row-gap: 6px;
                        flex: 1;

                        .availOrderTitle {
                            font-family: Syncopate;
                            font-size: 14px;
                            font-weight: bold;
                            font-stretch: normal;
                            font-style: normal;
                            line-height: 1.4;
                            letter-spacing: 0.98px;
                            text-align: left;
                            color: #fff;
                            text-transform: uppercase;
                        }

                        .availOrderDetails {
                            display: flex;
                            justify-content: space-between;
                        }

                        .colLeft {
                            font-family: Inter;
                            font-size: 14px;
                            font-weight: 300;
                            line-height: normal;
                            letter-spacing: 0.56px;
                            text-align: left;
                            display: flex;
                            flex-direction: column;
                            row-gap: 16px;
                            flex: 1;

                            .deliverTo {
                                display: flex;
                                column-gap: 15px;

                                span {
                                    &:first-child {
                                        color: rgba(255, 255, 255, 0.6);
                                        width: 100px;
                                        display: inline-flex;
                                    }

                                    &:last-child {
                                        color: #fff;
                                    }

                                }
                            }

                            &:first-child {
                                .deliverTo {
                                    span {
                                        &:first-child {
                                            width: 85px;
                                        }

                                    }
                                }
                            }


                        }

                        .acceptOrderBtnSection {
                            .btnOfAHText {
                                display: flex;
                                flex-direction: column;
                                row-gap: 8px;
                            }

                            .accpetRejectBtn {
                                width: 24px;
                                height: 24px;
                                padding: 3px 5px 1px;
                                object-fit: contain;
                                border-radius: 3px;
                                border: solid 0.5px rgba(255, 255, 255, 0.2);
                                font-family: Syncopate;
                                font-size: 16px;
                                font-weight: normal;
                                font-stretch: normal;
                                font-style: normal;
                                line-height: 1.28;
                                letter-spacing: -0.32px;
                                text-align: center;
                                color: rgba(255, 255, 255, 0.35);
                                display: flex;
                                align-items: center;
                                justify-content: center;
                            }

                            .availcliam {
                                font-family: Inter;
                                font-size: 14px;
                                font-weight: 300;
                                font-stretch: normal;
                                font-style: normal;
                                line-height: normal;
                                letter-spacing: 0.56px;
                                text-align: right;
                                display: flex;
                                flex-direction: column;
                                row-gap: 8px;

                                .claimTitle {
                                    color: rgba(255, 255, 255, 0.6);
                                }

                                .claimTime {
                                    color: #fff;
                                }
                            }

                        }

                    }


                }

            }
        }
    }

    .btnSection {
        border-top: 1px solid #000;
        padding-left: 20px;
        padding-top: 10px;
        margin-top: 20px;

        div {
            &:nth-child(1) {
                text-align: left;
            }
        }

        .termsAndPatent {
            position: relative;

            .TermsandConditions {
                font-family: Inter;
                font-size: 12px;
                font-weight: 300;
                line-height: 1.6;
                text-align: center;
                color: #fff;
                cursor: pointer;
            }

            .patentPendingText {
                font-family: Inter;
                font-size: 12px;
                font-weight: 300;
                line-height: 1.6;
                text-align: center;
                color: #fff;


            }
        }

        .backBtn {
            font-family: Inter;
            font-size: 18px;
            font-weight: normal;
            line-height: 1.6;
            color: #fff;
            border: 0px;
            outline: none;
            background-color: transparent;
            text-align: left;
            cursor: pointer;
            padding-left: 10px;

            &:hover,
            &:focus {
                color: #b3b3b3;
            }
        }
    }
}

.ErrorDialog {
    .dialogContent {
        max-width: 330px;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding: 30px 34px 30px 34px;
        object-fit: contain;
        border-radius: 10px;
        -webkit-backdrop-filter: blur(24px);
        backdrop-filter: blur(24px);
        box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
        background-color: rgba(0, 0, 0, 0.72);
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.6;
        text-align: center;
        color: #fff;

        p {
            margin-bottom: 20px;
        }

        .submitBtn {
            height: 40px;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            padding: 10px 24px;
            border-radius: 4px;
            border: solid 0.5px #fff;
            background-color: transparent;
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.4;
            text-align: center;
            color: #fff;
            margin-top: 20px;
            transition: all 0.1s;

            &:hover {
                background-color: #70ff00;
                border: solid 0.5px #70ff00;
                color: #000;
            }
        }

    }
}

.selectedOrder {
    background-color: rgba(255, 255, 255, 0.07);
}