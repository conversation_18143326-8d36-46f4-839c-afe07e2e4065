import React, { useState } from 'react';
import { RgbaColorPicker } from 'react-colorful';
import './CustomColumnCreator.scss';
import { Select, MenuItem, FormControl, InputLabel } from '@mui/material';
import clsx from 'clsx';

const CustomColumnCreator = ({ onSave, onCancel }) => {
  // State for form fields
  const [columnName, setColumnName] = useState('');
  const [abbreviation, setAbbreviation] = useState('');
  const [dataType, setDataType] = useState('');
  const [isShowDataTypeDropDownOpen, setIsShowDataTypeDropDownOpen] = useState(false);

  // State for styling options using RGBA format
  const [borderColor, setBorderColor] = useState({ r: 215, g: 0, b: 0, a: 0.76 });
  const [fillColor, setFillColor] = useState({ r: 235, g: 148, b: 148, a: 0.47 });
  const [textColor, setTextColor] = useState({ r: 0, g: 0, b: 0, a: 1 });

  // Convert RGBA object to string
  const rgbaToString = (rgba) => {
    return `rgba(${rgba.r}, ${rgba.g}, ${rgba.b}, ${rgba.a})`;
  };

  // Convert RGBA to hex for compatibility with existing code
  const rgbaToHex = (rgba) => {
    const toHex = (value) => {
      const hex = Math.round(value).toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    };
    
    return `#${toHex(rgba.r)}${toHex(rgba.g)}${toHex(rgba.b)}`;
  };

  // Handle save button click
  const handleSave = () => {
    if(!columnName.trim() || !abbreviation.trim()){
      return;
    }
    // // Create custom box type object
    const customBoxType = {
      id: columnName.toLowerCase().replace(/\s+/g, '_'),
      label: columnName,
      abbreviation: abbreviation,
      dataType: dataType,
      color: rgbaToHex(borderColor), // Convert to hex for compatibility
      fillColor: rgbaToString(fillColor), // Use rgba string with opacity
      textColor: rgbaToHex(textColor), // Convert to hex for compatibility
      bgColor: '#c3c4ca',
      hoverBg: rgbaToString(fillColor),
      selectedBg: rgbaToHex(borderColor),
    };

    // // Call the onSave callback with the custom box type
    onSave(customBoxType);
  };

  // Handle data type selection
  const handleDataTypeSelect = (type) => {
    setDataType(type);
    // setShowDataTypeDropdown(false);
  };

  return (
    <div className="custom-column-creator">
      <div className='custom-col-top-section'>
      <span className='custome-col-1'>
        <div className="custom-column-header">CUSTOM COLUMN</div>

        <div className="custom-column-form">
          <div className="form-group input-box">
            <input
              type="text"
              value={columnName}
              onChange={(e) => setColumnName(e.target.value)}
              placeholder="COLUMN NAME"
            />
          </div>

          <div className="form-group input-box">
            <input
              type="text"
              value={abbreviation}
              onChange={(e) => setAbbreviation(e.target.value)}
              placeholder="ABBREVIATION"
            />
          </div>

          <div className="form-group">
            <div className={clsx("dropdown-container" , isShowDataTypeDropDownOpen && "custom-dropdown-open")}>


           
                <FormControl fullWidth variant="outlined" size="small">
                  <Select
                    value={dataType}
                    onChange={(e) => handleDataTypeSelect(e.target.value)}
                    displayEmpty
                    className="dataTypeDropdown"
                    onOpen={() => setIsShowDataTypeDropDownOpen(true)}
                    onClose={() => setIsShowDataTypeDropDownOpen(false)}
                    MenuProps={{
                      disablePortal: true,

                      classes: {
                        paper: 'dropdownPaper',
                        list: 'muiMenuList',
                      },

                    }}
                    renderValue={(selected) => {
                      if (!selected) {
                        return <span style={{ color: '#888' }}>Data Type</span>; // Placeholder style
                      }
                      return selected;
                    }}
                  >
                    <MenuItem value="" disabled style={{ display: 'none' }}>
                      Data Type
                    </MenuItem>
                    <MenuItem value="TEXT">TEXT</MenuItem>
                    <MenuItem value="NUMBER">NUMBER</MenuItem>
                    <MenuItem value="ALPHANUMERIC">ALPHANUMERIC</MenuItem>
                    <MenuItem value="DATE">DATE</MenuItem>
                  </Select>
                </FormControl>
 </div>
          </div>
        </div>

      </span>
      <div className="styling-content">
        <div className="styling-section color-picker-col">
          <div className="section-header">BORDER</div>
          <div className="color-picker">
            <RgbaColorPicker color={borderColor} onChange={setBorderColor} />
          </div>
        </div>

        <div className="styling-section color-picker-col">
          <div className="section-header">FILL</div>
          <div className="color-picker">
            <RgbaColorPicker color={fillColor} onChange={setFillColor} />
          </div>
        </div>

        <div className="styling-section color-picker-col">
          <div className="section-header">TEXT</div>
          <div className="color-picker">
            <RgbaColorPicker color={textColor} onChange={setTextColor} />
          </div>
        </div>

        <div className="styling-section">
          <div className="section-header">PREVIEW</div>
          <div className="preview-container">
            <div className="preview-field">
              <div 
                className="preview-header"
                style={{
                  backgroundColor: rgbaToString(borderColor),
                  color: rgbaToString(textColor)
                }}
              >
                {abbreviation || 'ABC'}
              </div>
              <div 
                className="preview-box large"
                style={{
                  borderColor: rgbaToString(borderColor),
                  backgroundColor: rgbaToString(fillColor)
                }}
              >
              </div>
            </div>
            <div className="preview-field">
              <div 
                className="preview-header"
                style={{
                  backgroundColor: rgbaToString(borderColor),
                  color: rgbaToString(textColor)
                }}
              >
                {abbreviation || 'ABC'}
              </div>
              <div 
                className="preview-box medium"
                style={{
                  borderColor: rgbaToString(borderColor),
                  backgroundColor: rgbaToString(fillColor)
                }}
              >
              </div>
            </div>
            <div className="preview-field">
              <div 
                className="preview-header small-header"
                style={{
                  backgroundColor: rgbaToString(borderColor),
                  color: rgbaToString(textColor)
                }}
              >
                {abbreviation || 'ABC'}
              </div>
              <div 
                className="preview-box small"
                style={{
                  borderColor: rgbaToString(borderColor),
                  backgroundColor: rgbaToString(fillColor)
                }}
              >
              </div>
            </div>
          </div>
        </div>
      </div>
 </div>
      <div className="action-buttons">
        <button className="cancel-button" onClick={onCancel}>CANCEL</button>
        <button className="save-button" onClick={handleSave} disabled={!columnName.trim() || !abbreviation.trim() || !dataType.trim()}>SAVE</button>
      </div> 
    </div>
  );
};

export default CustomColumnCreator;
