import { Dialog, Tooltip } from '@mui/material';
import React, { useEffect, useMemo, useRef, useState, useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router';
import styles from '../../../renderer/pages/buyer/CreatePo/CreatePo.module.scss';
import clsx from 'clsx';
import {  useFieldArray, useForm } from 'react-hook-form';
import { useDebouncedValue } from '@mantine/hooks';
import dayjs from 'dayjs';
import useGetUserPartData from 'src/renderer/hooks/useGetUserPartData';
import { useQueryClient } from '@tanstack/react-query';
import { v4 as uuidv4 } from 'uuid';
import { useGlobalStore, useSaveUserActivity, getFloatRemainder, getValUsingUnitKey, useBuyerSettingStore, useCreatePoStore, uploadBomConst, getSocketConnection, useBuyerCheckOutNode, orderIncrementPrefix, priceUnits, newPricingPrefix, getChannelWindow, commomKeys, downloadFilesUsingFetch, dateTimeFormat, useGetDeliveryDate, mobileDiaglogConst, orderConfirmationConst, useOrderManagementStore, userRole, formatToTwoDecimalPlaces} from '@bryzos/giss-ui-library';
import { useRightWindowStore } from '../RightWindow/RightWindowStore';
import { routes, bomLineStatusCountObjDefault, localStorageKeys, navigationConfirmMessages, disputeCounterStatus } from 'src/renderer/common';
import useDialogStore from 'src/renderer/component/DialogPopup/DialogStore';
import UploadBomSummary from 'src/renderer/component/UploadBomSummary/UploadBomSummary';
import BomProcessingWindow from 'src/renderer/component/BomProcessingWindow/BomProcessingWindow';
import useGetAvailableCreditLimit from 'src/renderer/hooks/useGetAvailableCreditLimit';
import useSaveBomHeaderDetails from 'src/renderer/hooks/useSaveBomHeaderDetails';
import usePostDraftPo from 'src/renderer/hooks/usePostDraftPo';
import { useBomPdfExtractorStore } from 'src/renderer/pages/buyer/BomPdfExtractor/BomPdfExtractorStore';
import CreatePoHeaderInfo from 'src/renderer/pages/buyer/CreatePoHeaderInfo/CreatePoHeaderInfo';
import { useGenericForm } from 'src/renderer/hooks/useGenericForm';
import { bomReviewSchema } from 'src/renderer/models/bomReview.model';
// import { useBomReviewStore } from '../BomReview/BomReviewStore';
import Scroller, { ScrollerRef } from 'src/renderer/component/Scroller';
import CreatePoTable from 'src/renderer/pages/buyer/CreatePo/components/CreatePoTable';
import OrderSummary from 'src/renderer/component/OrderSummary/OrderSummary';
import { clearLocal, downloadCertificate, formatAndUpdateDraftPoData, getLocal, newFormatCartItems, saveDraftData, setLocal } from 'src/renderer/helper';
import UploadReviewWindow from 'src/renderer/component/UploadReviewWindow/UploadReviewWindow';
import ConfirmPoHeaderDetails from 'src/renderer/pages/buyer/CreatePo/components/ConfirmPoHeaderDetails';
import OrderManagementHeaderInfo from './components/OrderManagementHeaderInfo/OrderManagementHeaderInfo';
import { ReactComponent as DropDownArrowIcon } from '../../assets/images/StateIconDropDpown.svg';
import { ReactComponent as DeleteIcon } from '../../assets/images/delete-outlined.svg';
import { ReactComponent as ExitIcon } from '../../assets/images/Order-Management/exit-mode.svg';
import { ReactComponent as DuplicateIcon } from '../../assets/images/Order-Management/Duplicate.svg';
import { ReactComponent as EditIcon } from '../../assets/images/Order-Management/Pencil.svg';
import { ReactComponent as CloseIcon } from '../../assets/images/Order-Management/exit-mode.svg';
import Calendar from 'src/renderer/component/Calendar/Calendar';
import InputWrapper from 'src/renderer/component/InputWrapper';
import CustomTextField from 'src/renderer/component/CustomTextField';
import usePostBuyerCancelOrder from 'src/renderer/hooks/usePostBuyerCancelOrder';
import usePostRemovePoHistoryLine from 'src/renderer/hooks/usePostRemovePoHistoryLine';
import { AgGridReact } from 'ag-grid-react';
import "ag-grid-community/styles/ag-theme-alpine.css";
import ClaimOrderRightWindow from 'src/renderer/component/ClaimOrderRightWindow/ClaimOrderRightWindow';
import NewOrderConfirmation from '../buyer/orderConfirmation/NewOrderConfirmation';
import SellerHeaderInfo from './components/SellerHeaderInfo/SellerHeaderInfo';
import usePostRemoveOrder from 'src/renderer/hooks/usePostRemoveOrder';
import usePostEditUnclaimedOrder from 'src/renderer/hooks/usePostEditUnclaimedOrder';
import DynamicHeader from './components/DynamicHeader';
import axios from 'axios';
import usePostSellerCancelOrder from 'src/renderer/hooks/usePostSellerCancelOrder';
import { useSearchStore } from 'src/renderer/store/SearchStore';
import ItemAttentionNav from './components/ItemAttentionNav';
import usePostDisputeOrder from 'src/renderer/hooks/usePostDisputeOrder';
import OrderCancellationDispute from './components/OrderCancellationDispute/OrderCancellationDispute';
import StateZipDispute from './components/StateZipDispute/StateZipDispute';
import useGetDraftLines from 'src/renderer/hooks/useGetDraftLines';
import useOrderItemClick from 'src/renderer/hooks/useOrderItemClick';
import { useLeftPanelStore } from 'src/renderer/component/LeftPanel/LeftPanelStore';

const DuplicatePoDialog = ({
    onViewDraft,
    onUndo,
    onClose,
    setOpenErrorDialog,
    setErrorMessage
}: {
    onViewDraft: (data: { jobPoNumber: string; delivery_date: string }) => void;
    onUndo: () => void;
    onClose: () => void;
    setOpenErrorDialog: (open: boolean) => void;
    setErrorMessage: (message: string) => void;
}) => {
    const deliveryAllowedDates = useBuyerSettingStore((state: any) => state.deliveryAllowedDates);
    const disableDeliveryDate = useBuyerSettingStore((state: any) => state.disableDeliveryDate);
    const deliveryDateMap = useBuyerSettingStore((state: any) => state.deliveryDateMap);
    const {
        register,
        handleSubmit,
        watch,
        setValue,
        formState: { errors, isValid }
    } = useForm({
        mode: 'onChange',
        defaultValues: {
            jobPoNumber: '',
            delivery_date: ''
        }
    });

    const [isCalendarOpen, setIsCalendarOpen] = useState(false);
    const watchedValues = watch();

    const onSubmit = (data: { jobPoNumber: string; delivery_date: string }) => {
        onViewDraft(data);
    };

    const isViewDraftDisabled = !isValid || !watchedValues.jobPoNumber.trim() || !watchedValues.delivery_date.trim();

    const handleDateSelect = (date: string) => {
        const selectedDate = dayjs(date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit);
        setValue('delivery_date_offset', deliveryDateMap?.[selectedDate]?.days_to_add);
    };

    const handleOpenCalendarBtn = () => {
        if (!disableDeliveryDate) {
            setIsCalendarOpen(true);
        } else {
            if (disableDeliveryDate) {
                setOpenErrorDialog(true)
                setErrorMessage(mobileDiaglogConst.receivingHrsEmpty)
            }
            setIsCalendarOpen(false)
        }
    };

    return (
        <div className={styles.duplicatePoDialogContainer}>
            <div className={styles.header}>
                <div className={styles.title}>ORDER DUPLICATED SUCCESSFULLY</div>
                <button className={styles.closeIcon} onClick={onClose}><ExitIcon /></button>
            </div>
            <div className={styles.formInputGroupDuplicate}>
                <InputWrapper>
                    <CustomTextField
                        className={clsx(styles.inputfiled, styles.pOInput)}
                        type='text'
                        register={register("jobPoNumber")}
                        placeholder='JOB / PO#'
                        onBlur={(e) => {
                            e.target.value = e.target.value.trim();
                            register("jobPoNumber").onBlur(e);
                        }}
                        maxLength={20}
                        autoFocus
                    />
                </InputWrapper>

                <Calendar
                    allowedDates={deliveryAllowedDates}
                    value={watch('delivery_date')}
                    setValue={setValue}
                    isCalendarOpen={isCalendarOpen}
                    setIsCalendarOpen={setIsCalendarOpen}
                    disableDeliveryDate={disableDeliveryDate}
                    handleOpenCalendar={handleOpenCalendarBtn}
                    saveUserActivity={() => { }}
                    onDateSelect={handleDateSelect}
                    saveBomHeaderDetails={() => { }}
                />

                <div className={styles.btnSectionPopup}>
                <button
                    type="submit"
                    disabled={isViewDraftDisabled}
                    onClick={handleSubmit(onSubmit)}
                >
                    VIEW DUPLICATE PO DRAFT
                </button>
                <button
                    type="button"
                    onClick={onUndo}
                >
                    UNDO DUPLICATION
                </button>
            </div>
            </div>
            
        </div>
    )
}

const OrderManagementComponent = ( {mainWrapperRef}: {mainWrapperRef: React.RefObject<HTMLDivElement>} ) => {
    const selectedQuote = useCreatePoStore(state => state.selectedQuote);
    const hasInitiallySelected = useRef(false);

    return (
        <OrderManagementComponentView mainWrapperRef={mainWrapperRef} key={selectedQuote?.id} hasInitiallySelected={hasInitiallySelected} />
    )
}

const OrderManagementComponentView = ( {mainWrapperRef, hasInitiallySelected}: {mainWrapperRef: React.RefObject<HTMLDivElement>, hasInitiallySelected: React.RefObject<boolean>} ) => {
    const navigate = useNavigate();
    const location = useLocation();
    const { setShowLoader, setCreatePoSessionId, setBackNavigation, referenceData, productData, productMapping, backNavigation, isCreatePoDirty, userData } = useGlobalStore();
    const { bomProductMappingSocketData, setBomProductMappingSocketData, isCreatePOModule, setIsCreatePOModule, setCreatePoData, createPoData, bomProductMappingDataFromSavedBom, createPoDataFromSavedBom, setCreatePoDataFromSavedBom, setBomProductMappingDataFromSavedBom, setBomDataIdToRefresh, bomSummaryViewFilter, setBomSummaryViewFilter, setUploadBomInitialData, uploadBomInitialData } = useCreatePoStore();
    const setQuoteList = useCreatePoStore(state => state.setQuoteList);
    const quoteList = useCreatePoStore(state => state.quoteList);
    const setSelectedQuote = useCreatePoStore(state => state.setSelectedQuote);
    const selectedQuote = useCreatePoStore(state => state.selectedQuote);

    const channelWindow =  getChannelWindow();
    const { bomData } = useBomPdfExtractorStore();
    const showCommonDialog = useDialogStore((state) => state.showCommonDialog);
    const resetDialogStore = useDialogStore((state) => state.resetDialogStore);
    const setIsEditingPo = useOrderManagementStore(state => state.setIsEditingPo);
    const isEditingPo = useOrderManagementStore(state => state.isEditingPo);
    const cancelOrderSocketData = useOrderManagementStore(state => state.cancelOrderSocketData);
    const setCancelOrderSocketData = useOrderManagementStore(state => state.setCancelOrderSocketData);
    const setSelectedCancelLines = useOrderManagementStore(state => state.setSelectedCancelLines);
    const selectedCancelLines = useOrderManagementStore(state => state.selectedCancelLines);
    const orderManageMentInitialData = useOrderManagementStore(state => state.orderManageMentInitialData);
    const orderManagementData = useOrderManagementStore(state => state.orderManagementData);
    const setOrderManageMentInitialData = useOrderManagementStore(state => state.setOrderManageMentInitialData);
    const { setLoadComponent, setShowVideo, bomProcessingWindowProps, setShowBomProcessing, setBOMLineStatusCountObj,  setScrollToBomLine, props, setProps } = useRightWindowStore();

    const [products, setProducts] = useState([]);

    const [openErrorDialog, setOpenErrorDialog] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');
    const { data: userPartData } = useGetUserPartData();
    const mutateSaveBomHeaderDetails = useSaveBomHeaderDetails();
    // const setScrollPosition = useBomReviewStore((state) => state.setScrollPosition);
    // const setViewIndex = useBomReviewStore((state) => state.setViewIndex);
    const scrollerRef = useRef<ScrollerRef>(null);
    
    const [sessionId, setSessionId] = useState('');
    const [searchStringData, setSearchString] = useState('');
    // const getBuyingPreference = ueGetBuyingPreference();
    const { buyerSetting } = useBuyerSettingStore();
    // const saveProductSearch = useSaveProductSearchAnaytic();
    const [openDeliveryToDialog, setOpenDeliveryToDialog] = useState(false);
    const [isCalendarOpen, setIsCalendarOpen] = useState(false);
    const createPoContainerRef = useRef(null);
    
    // Dynamic Header Component State
    const globalDisputeComponent = useOrderManagementStore(state => state.globalDisputeComponent);
    const setGlobalDisputeComponent = useOrderManagementStore(state => state.setGlobalDisputeComponent);
    const globalDisputeProps = useOrderManagementStore(state => state.globalDisputeProps);
    const setGlobalDisputeProps = useOrderManagementStore(state => state.setGlobalDisputeProps);
    const purchasingList = useCreatePoStore((state: any) => state.purchasingList);
    const setIsCreatePoDirty = useCreatePoStore((state: any) => state.setIsCreatePoDirty);
    const headerContainerRef = useRef<HTMLDivElement>(null);
    // const [isCartValid, setIsCartValid] = useState(true);
    // const [isAllCartDataLoaded, setIsAllCartDataLoaded] = useState(false);
    const [pricingBrackets, setPricingBrackets] = useState([]);
    const [disableBidBuyNow, setDisableBidBuyNow] = useState(false);
    const addPoLineTableRef = useRef(null);
    const [hidePoLineScroll, setHidePoLineScroll] = useState(true);
    const [maxScrollHeight, setMaxScrollHeight] = useState(800);
    const formInputGroupRef = useRef(null);
    const [bomUploadResult, setBomUploadResult] = useState([]);
    const getAvailableCreditLimit = useGetAvailableCreditLimit();
    const [isSavedBom, setIsSavedBom] = useState(location.pathname === routes.savedBom);
    const [cameFromSavedBom, setCameFromSavedBom] = useState(false);
    const [isHeaderDetailsConfirmed, setIsHeaderDetailsConfirmed] = useState(false);
    const [currentBomData, setCurrentBomData] = useState(null);
    const [showConfirmPoHeaderDialog, setShowConfirmPoHeaderDialog] = useState(false);
    const [openDuplicatePoDialog, setOpenDuplicatePoDialog] = useState(false);
    // Track the cart items length separately
    const socket = getSocketConnection();
    const HeaderDetailsConfirmedRef = useRef(null);

    const createPoHeaderInfoRef = useRef<any>(null);
    const createPoTableRef = useRef<any>(null);

    const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    const { initializePoHeaderForm, watch: poHeaderFormWatch, getHeaderFormData, setValue: setPoHeaderFormValue, handleUndoForHeader} = createPoHeaderInfoRef.current ?? {};
    const {getInitialData, handleScrollerDrag, disableFormValidation, getCartItems, getExportPoData, watch: createPoTableWatch, updateLocalStoreQuote, setValue: setCreatePoTableValue, handleUndoForLine, handleScrollToBomLine, handleSaveDuplicatePo, createPoResultCopy, handleExitEditModeTableData, disableConvertToPoButton, handlePriceIntegration, handleScrollToDisputeLine} = createPoTableRef.current ?? {}; 
    const [focusJobPoInput, setFocusJobPoInput] = useState(false);
    const [isProgrammaticScroll, setIsProgrammaticScroll] = useState(false);
    const { mutate: logUserActivity } = useSaveUserActivity();
    const [undoStack, setUndoStack] = useState([]);
    const downloadContainerRef = useRef(null);
    const [isDownloadDropdownOpen, setIsDownloadDropdownOpen] = useState(false);
    const cancelContainerRef = useRef(null);
    const [isCancelDropdownOpen, setIsCancelDropdownOpen] = useState(false);
    const [openCancelLineWhenUnclaimedDialog, setOpenCancelLineWhenUnclaimedDialog] = useState(false);
    const [repriceLinesData, setRepriceLinesData] = useState([]);
    const [isOrderLineChanges, setIsOrderLineChanges] = useState(false);
    const [isStateZipValChange, setIsStateZipValChange] = useState(false);
    const locationStateRef = useRef(location.state);
    const isSeller = userData?.data?.type === userRole.sellerUser;
    const [orderConfirmationData, setOrderConfirmationData] = useState<any>(null);
    const [showOrderConfirmationPopup, setShowOrderConfirmationPopup] = useState(false);
    
    const { mutateAsync: cancelOrderMutation } = usePostBuyerCancelOrder();
    const { mutateAsync: removePoHistoryLine } = usePostRemovePoHistoryLine();
    const { mutateAsync: removeOrder } = usePostRemoveOrder();
    const { mutateAsync: editUnclaimedOrder } = usePostEditUnclaimedOrder();
    const postDraftPo = usePostDraftPo();
    const setIsNavigatingToOtherPage = useOrderManagementStore(state => state.setIsNavigatingToOtherPage);
    const isNavigatingToOtherPage = useOrderManagementStore(state => state.isNavigatingToOtherPage);
    const isBuyer = userData?.data?.type === userRole.buyerUser;
    const [cancelOrderOnSellerBtnDisabled, setCancelOrderOnSellerBtnDisabled] = useState(false);
    const { mutateAsync: cancelOrderOnSeller } = usePostSellerCancelOrder();
    const { handleItemClick } = useOrderItemClick({ isSearchMode: false });

    // Item Attention Navigation state and dummy data
    const [originalDisputeItems, setOriginalDisputeItems] = useState([]);
    const [attentionItems , setAttentionItems] = useState([]);
    const [currentFocusedItemIndex, setCurrentFocusedItemIndex] = useState(0);
    const [currentFocusedItem, setCurrentFocusedItem] = useState(null);
    const [finalDisputePayload, setFinalDisputePayload] = useState(null);
    const [isDisputeThereForCurrentUser, setIsDisputeThereForCurrentUser] = useState(false);
    const [isDisputeThere, setIsDisputeThere] = useState(false);
    const [totalDisputeCount, setTotalDisputeCount] = useState(0);

    const { mutateAsync: disputeOrderMutation } = usePostDisputeOrder();
    const { mutateAsync: getDraftLines } = useGetDraftLines();
    const isOrderCanceled = Boolean(selectedQuote?.cancel_date);
    const isOrderClosed = Boolean(selectedQuote?.is_closed_order)
    const isCancelOrderDisputeThere = selectedQuote?.order_level_dispute?.cancel_order?.length > 0;
    const createPOContainerWrapperInnerRef = useRef(null);
    const clientName = import.meta.env.VITE_CLIENT_NAME;

    useEffect(() => {
        setIsSavedBom(location.pathname === routes.savedBom);
        // setShowLoader(true)
        return (() => {
        })
    }, [])


    useEffect(() => {
      if (location.state?.isFromCheckout) {
        locationStateRef.current = location.state;
    }

    const storedState = locationStateRef.current;

        if(storedState?.isFromCheckout && !showOrderConfirmationPopup){
            setOrderConfirmationData({
                poNumber: storedState.poNumber,
                jobNumber: storedState.jobNumber,
            });
            setTimeout(() => {
                setShowOrderConfirmationPopup(true);
            }, 1000);
        }
    }, [location.state?.isFromCheckout])

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (downloadContainerRef.current && !downloadContainerRef.current.contains(event.target as Node)) {
                setIsDownloadDropdownOpen(false);
            }
            if(cancelContainerRef.current && !cancelContainerRef.current.contains(event.target as Node)) {
                setIsCancelDropdownOpen(false);
            }
        };

        if (isDownloadDropdownOpen || isCancelDropdownOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isDownloadDropdownOpen, isCancelDropdownOpen]);


    useEffect(() => {
        console.log("check is navigating to other page @>>>>>>>", isNavigatingToOtherPage);
        if (isNavigatingToOtherPage) {
            handleOrderCancellationAndPurchasingTransfer();
        }
    }, [isNavigatingToOtherPage])

    const handleOrderCancellationAndPurchasingTransfer = async () => {
        try {
            const localOrderManagement = getLocal(localStorageKeys.poPurchasing, null);
            if (localOrderManagement) {
                const payload = {
                    po_number: localOrderManagement?.buyer_po_number,
                    type: orderConfirmationConst.buyerCancel
                }
                const response = await cancelOrderMutation(payload);
                if (response?.data?.error_message) {
                    console.log("error_message", response?.data?.error_message);
                    return;
                }
                await saveDraftData(postDraftPo, localStorageKeys.poPurchasing);
                setIsNavigatingToOtherPage(false);

            }
        } catch (error) {
            console.error("error_message", error);
        }
    }

    
    // Replace the existing handleLeavePageAction function
    const handleLeavePageAction = () => {
        setLoadComponent(null);
        setProps(null);
        const { bomDataIdToRefresh } = useCreatePoStore.getState();
        if (bomDataIdToRefresh) {
            socket?.emit('getBomDataById', { bom_upload_id: bomDataIdToRefresh });
        }

        saveBomHeaderDetails();
        setBomUploadResult([]);
        if (bomProcessingWindowProps?.isProcessing !== true) {
            setBomProductMappingSocketData(null);//do this conditionally
            setIsCreatePOModule(true);
            setCreatePoDataFromSavedBom(null);
            setBomProductMappingDataFromSavedBom(null);
        }
        setScrollToBomLine(null)
        setBomDataIdToRefresh(null);
    }
    

    //*IMP*
    // const disableReviewCompleteButton = true || !checkAtLeastOneApproved || isValidBomForm
// console.log("disableReviewCompleteButton >>>>>>.>> ", disableReviewCompleteButton,  !orderInfoIsFilled, !checkAtLeastOneApproved, isValidBomForm)


    // useEffect(() => {
    //     if(currentBomData?.id && getHeaderFormData){
    //         setProps({...props, bomId: currentBomData.id, getHeaderFormData});
    //     }
    // }, [
    //     currentBomData?.id,
    //     getHeaderFormData
    // ]);

    // useEffect(() => {
    //     const component = <UploadBomSummary />;
    //     setLoadComponent(component);
    // }, []);

    useEffect(() => {
        const component = isSeller ? <ClaimOrderRightWindow /> : <UploadReviewWindow key={location.pathname}/>;
        setLoadComponent(component);
    }, []);

    useEffect(()=>{
        if(!selectedQuote){
            setProps(null);
        }
    },[selectedQuote])

    useEffect(() => {
        if (bomProcessingWindowProps?.isProcessing === true) {
            setShowVideo(false);
            setShowBomProcessing(true);
        } else {
            setShowVideo(true);
            setShowBomProcessing(false);
        }
        // reset();
        // poHeaderForm.reset();
        // setIsAllCartDataLoaded(false);
        setBomUploadResult([])

        if (productData && referenceData) {
            setProducts(productData)
            setPricingBrackets(referenceData?.ref_weight_price_brackets);
        }
        const sessionId = uuidv4();
        setSessionId(sessionId);
        setCreatePoSessionId(sessionId)

        return (() => {
            setLoadComponent(null)
            setShowBomProcessing(false);
            if (bomProcessingWindowProps?.isProcessing === true) {
                setLoadComponent(
                    <div >
                        <BomProcessingWindow
                        // isCompleted={processingCompleted}
                        // gameScoreData={getGameScore}
                        />
                    </div>
                );
            }
            setBackNavigation(-1)
        })
    }, []);

    useEffect(() => {
        // console.log("-------initialData----------", initialData);
        if(initializePoHeaderForm){
            setTimeout(()=>{
                _init();
            }, 1000);
        }
    }, [initializePoHeaderForm]);

    const _init = () => {
        const _intialData = getInitialData();
        // findItemAttention(_intialData);
        initializePoHeaderForm(_intialData);
        // if (isCreatePOModule) {
        //     const payload = {
        //         "data": {
        //             "session_id": sessionId
        //         }
        //     }
        //     logUserActivity.mutateAsync({ url: import.meta.env.VITE_API_SERVICE + '/user/create_po_open_close', payload }).catch(err => console.error(err));
        // }
    }

    const findItemAttention = (data: any) => {
        const disputes = [];
        
        // Check order level disputes
        if (data?.order_level_dispute) {
            const { deliver_by, deliver_to } = data.order_level_dispute;
            
            if (deliver_by?.length > 0) {
                setIsDisputeThere(true);
                const lastDispute = deliver_by[deliver_by.length - 1];
                if (((lastDispute?.created_by && lastDispute.created_by !== userData?.data?.type) || lastDispute?.counter_status === disputeCounterStatus.original) && lastDispute?.counter_status !== disputeCounterStatus.resolved) {
                    disputes.push({
                        id: 'deliver_by',
                        type: 'order_level'
                    });
                }
            }
            
            if (deliver_to?.length > 0) {
                setIsDisputeThere(true);
                const lastDispute = deliver_to[deliver_to.length - 1];
                if (((lastDispute?.created_by && lastDispute.created_by !== userData?.data?.type) || lastDispute?.counter_status === disputeCounterStatus.original) && lastDispute?.counter_status !== disputeCounterStatus.resolved) {
                    disputes.push({
                        id: 'deliver_to',
                        type: 'order_level'
                    });
                }
            }
        }
        
        // Check line item disputes
        data?.cart_items?.forEach((item, index) => {
            if (item?.line_dispute_counter?.length > 0) {
                setIsDisputeThere(true);
                const lastDispute = item.line_dispute_counter[item.line_dispute_counter.length - 1];
                if (((lastDispute?.created_by && lastDispute.created_by !== userData?.data?.type) || lastDispute?.counter_status === disputeCounterStatus.original) && lastDispute?.counter_status !== disputeCounterStatus.resolved) {
                    disputes.push({
                        id: index,
                        type: 'line_item'
                    });
                }
            }
        });

        if(disputes.length > 0){
            setIsDisputeThereForCurrentUser(true);
        }else{
            setIsDisputeThereForCurrentUser(false);
        }
        setAttentionItems(disputes);
        setOriginalDisputeItems(disputes);
        setCurrentFocusedItem(disputes[0]);
        setTotalDisputeCount(data?.dispute_count || disputes.length);
        setCurrentFocusedItemIndex(0);
    };

    const handleAttentionNext = () => {
        setCurrentFocusedItem(attentionItems[currentFocusedItemIndex + 1]);
        navigateToDispute(attentionItems[currentFocusedItemIndex + 1]);
        setCurrentFocusedItemIndex(currentFocusedItemIndex + 1);
        console.log('Moving to next attention item');
        // Add your logic here for handling next item
    };

    const handleAttentionPrev = () => {
        setCurrentFocusedItem(attentionItems[currentFocusedItemIndex - 1]);
        navigateToDispute(attentionItems[currentFocusedItemIndex - 1]);
        setCurrentFocusedItemIndex(currentFocusedItemIndex - 1);
        console.log('Moving to previous attention item');
        // Add your logic here for handling previous item
    };

    const removeFromAttentionItems = (itemId: string | number, itemType: 'order_level' | 'line_item') => {
        setAttentionItems(prevItems => {
            const filteredItems = prevItems.filter((item : any) => 
                !(item?.id === itemId && item?.type === itemType)
            );
            
            // Always reset to first item or null if empty
            if (filteredItems.length === 0) {
                setCurrentFocusedItem(null);
                setCurrentFocusedItemIndex(0);
            } else {
                // Stay at current index if possible, otherwise go to last available
                const newIndex = currentFocusedItemIndex >= filteredItems.length 
                ? filteredItems.length - 1 
                : currentFocusedItemIndex;
                setCurrentFocusedItemIndex(newIndex);
                setCurrentFocusedItem(filteredItems[newIndex]);
            }
            
            setTotalDisputeCount(filteredItems.length);
            return filteredItems;
        });
    };

    const addToAttentionItems = (item: any) => {
        setAttentionItems((prev: any) => {
          if (!originalDisputeItems.some(orgItem => orgItem.id === item.id)) return prev;
          if (prev.some(p => p.id === item.id)) return prev; // no duplicates
      
          const priority: Record<string, number> = { deliver_by: 0, deliver_to: 1 };
      
          const updated = [...prev, item].sort((a, b) => {
            const pa = priority[a?.id] ?? 2;
            const pb = priority[b?.id] ?? 2;
            return pa !== pb ? pa - pb : Number(a.id) - Number(b.id);
          });
      
          setTotalDisputeCount(updated.length);
          return updated;
        });
      };
      

    const navigateToDispute = (disputeItem: any) => {
        if (disputeItem?.type === 'order_level') {
            // Scroll to header section
            scrollToTop();
        } else if (disputeItem?.type === 'line_item') {
            handleScrollToDisputeLine?.(disputeItem?.id);
        }
    };
    
    const setSellerCancellationHeader = useCallback(() => {
        setGlobalDisputeComponent(OrderCancellationDispute);
        setGlobalDisputeProps({});
    }, []);

    const clearDynamicHeader = useCallback(() => {
        setGlobalDisputeComponent(null);
        setGlobalDisputeProps({});
    }, []);

    // useEffect(() => {
    //     if (currentBomData?.items?.length > 0) {
    //         const cartItem = [];
    //         const statusCountObj = { ...bomLineStatusCountObjDefault };
    //         for (let i = 0; i < currentBomData.items.length; i++) {
    //             const productObj = {
    //                 lineStatusIndex: i,
    //                 bom_line_id: currentBomData.items[i].id || i,
    //                 lineStatus: currentBomData.items[i].status,
    //                 originalStatus: currentBomData.items[i].original_line_status || currentBomData.items[i].status,
    //                 confidence: currentBomData.items[i].confidence,
    //                 product_tag: currentBomData.items[i].product_tag,
    //                 description: currentBomData.items[i].description,
    //                 specification: currentBomData.items[i].specification,
    //                 search_string: currentBomData.items[i].search_string,
    //                 matched_products: currentBomData.items[i].matched_products,
    //                 selected_products: currentBomData.items[i].selected_products,
    //                 current_page: currentBomData.items[i].current_page,
    //                 total_pages: currentBomData.items[i].total_pages,
    //                 product_index: currentBomData.items[i].product_index,
    //                 grade: currentBomData.items[i].grade,
    //                 qty: currentBomData.items[i].qty?.replace(/[\$,]/g, '') || currentBomData.items[i].qty,
    //                 qty_unit: currentBomData.items[i].qty_unit?.toLowerCase(),
    //                 length: currentBomData.items[i].length,
    //                 weight_per_quantity: currentBomData.items[i].weight_per_quantity,
    //                 matched_product_count: currentBomData.items[i].matched_product_count,
    //                 last_updated_product: currentBomData.items[i]?.last_updated_product ?? 0,
    //                 domesticMaterialOnly: currentBomData.items[i]?.domestic_material_only || false
    //             }
    //             if (productObj.lineStatus === uploadBomConst.lineItemStatus.approved) {
    //                 statusCountObj[uploadBomConst.lineItemStatus.approved]++;
    //             } else if (productObj.lineStatus === uploadBomConst.lineItemStatus.pending) {
    //                 statusCountObj[uploadBomConst.lineItemStatus.pending]++;
    //             } else if (productObj.lineStatus === uploadBomConst.lineItemStatus.skipped) {
    //                 statusCountObj[uploadBomConst.lineItemStatus.skipped]++;
    //             } else if (productObj.lineStatus === uploadBomConst.lineItemStatus.deleted) {
    //                 statusCountObj[uploadBomConst.lineItemStatus.deleted]++;
    //             }
    //             cartItem[i] = productObj;
    //         }
    //         setBomUploadResult(cartItem);
    //         setBOMLineStatusCountObj(statusCountObj);
    //     }
    // }, [currentBomData])

    useEffect(() => {
        if (location.pathname !== routes.savedBom) {
            if (bomProductMappingSocketData) {
                setCurrentBomData(bomProductMappingSocketData);
            } else {
                setCurrentBomData(null);
            }
        }
    }, [bomProductMappingSocketData])

    useEffect(() => {
        if (location.pathname !== routes.savedBom) {
            if (bomData) {
                setCurrentBomData(bomData);
            } else {
                setCurrentBomData(null);
            }
        }
    }, [bomData])


    useEffect(() => {
        setProducts(productData);
    }, [productData]);




    useEffect(() => {
        const handleScrollOutsideDiv = (event) => {
            setIsCalendarOpen(false)
        };
        createPoContainerRef?.current?.addEventListener('scroll', handleScrollOutsideDiv);
        return () => {
            createPoContainerRef?.current?.removeEventListener('scroll', handleScrollOutsideDiv);
        };
    }, []);

    useEffect(() => {
       if(selectedQuote){
            setIsDisputeThere(false);
            if(selectedQuote?.order_level_dispute?.state_zip_dispute ){
                if(userData?.data?.type === userRole.sellerUser){
                    setGlobalDisputeComponent(StateZipDispute);
                }else{
                    setGlobalDisputeComponent(null);
                }
                setIsStateZipValChange(true);
            }
            else{
                setIsStateZipValChange(false);
               const orderLevelDispute = selectedQuote?.order_level_dispute;
               const cancel_order = orderLevelDispute?.cancel_order;
               if(cancel_order?.length > 0){
                const lastDispute = cancel_order[cancel_order.length - 1];
                if(lastDispute?.counter_status === disputeCounterStatus.pending){
                    setSellerCancellationHeader();
                }
               }
               else{
                setGlobalDisputeComponent(null);
               }
            }
       }else{
        setGlobalDisputeComponent(null);
       }
    }, [selectedQuote]);

    useEffect(()=>{
        if(cancelOrderSocketData && selectedQuote?.buyer_po_number === cancelOrderSocketData){
            setOrderManageMentInitialData(null);
            setCancelOrderSocketData(null, isBuyer);
        }
    },[cancelOrderSocketData, selectedQuote])


    const scrollPoHeaderToBottom = useCallback(() => {
        const container = createPoContainerRef.current;
        if (container) {
            container.scrollTo({ top: 244, behavior: 'smooth' });
        }
    }, []);


    // useEffect(() => {
    //     if (deboucedSearchString && isCreatePOModule) {
    //         handleCreatePOSearch(searchStringData, null, lineSessionId)
    //     }
    // }, [deboucedSearchString])

    const updateQuoteHeaderDetails = () => {
        if(updateLocalStoreQuote?.()){
            updateLocalStoreQuote();
        }
    }

    const saveBomHeaderDetails = () => {
        updateQuoteHeaderDetails();
        if (location.pathname === routes.bomUploadReview || location.pathname === routes.savedBom) {
            if (currentBomData?.id) {
                setBomDataIdToRefresh(currentBomData?.id);
            }
            try {
                const headerFormData = getHeaderFormData();
                const formattedHeaderFormData = {
                    "bom_name": headerFormData.buyer_internal_po,
                    "bom_type": headerFormData.order_type,
                    "delivery_date": headerFormData.delivery_date,
                    ...headerFormData
                }
                
                const payload = {
                    "data": {
                        "bom_upload_id": currentBomData?.id ?? '',
                        ...formattedHeaderFormData
                    }
                }
                if (currentBomData?.id && payload.data.bom_name && payload.data.bom_type && payload.data.delivery_date && payload.data.shipping_details.line1 && payload.data.shipping_details.city && payload.data.shipping_details.zip && payload.data.shipping_details.state_id) {
                    saveModifiedBomHeader();
                    mutateSaveBomHeaderDetails.mutateAsync(payload)
                    let _updatedBomInitialData = {
                        ...uploadBomInitialData,
                        ...headerFormData
                    }
                    setUploadBomInitialData(_updatedBomInitialData)
                }
            } catch (error) {
                console.error(error)
            }
        }
    }

    const saveModifiedBomHeader = () => {
        try {
            if (currentBomData?.id) {
                const data = {
                    [currentBomData?.id]: "HEADER"
                }
                const _lastModifiedBom = localStorage.getItem(localStorageKeys.lastModifiedBom);
                if (_lastModifiedBom) {
                    const _lastModifiedBomData = JSON.parse(_lastModifiedBom);
                    _lastModifiedBomData[currentBomData?.id] = "HEADER";
                    localStorage.setItem(localStorageKeys.lastModifiedBom, JSON.stringify(_lastModifiedBomData));
                } else {
                    localStorage.setItem(localStorageKeys.lastModifiedBom, JSON.stringify(data));
                }
            }
        } catch (e) {
            console.warn('Could not store value in localStorage', e);
        }
    }

    // Validates that cart items have required fields if they have a description
    const validateCart = () => {
        const cartItems = getValues('cart_items');
        if (!cartItems?.length) return true;

        // Only validate items that have a description
        const itemsWithDescription = cartItems.filter(item => item?.descriptionObj?.UI_Description && item?.lineStatus !== "SKIPPED" || item?.lineStatus === "APPROVED");
        if (!itemsWithDescription.length) return false;

        // Check required fields are present and valid
        return itemsWithDescription.every(item => {
            const quantity = Number(item.qty);
            return quantity > 0 &&
                Boolean(item.qty_unit) &&
                Boolean(item.price_unit);
        });
    };

    // Monitor cart changes and update validation state
    // useEffect(() => {
    //     const subscription = watch((_, { name }) => {
    //         if (name?.startsWith('cart_items')) {
    //             setIsCartValid(validateCart());
    //         }
    //     });
    //     return () => subscription.unsubscribe();
    // }, [watch]);

    

    const scrollToTop = useCallback(() => {
        if (scrollerRef.current) {
            scrollerRef.current.updateScrollPosition(0);
        }
        const container = createPoContainerRef.current;
        if (container) {
            container.scrollTo({ top: 0, behavior: 'smooth' });
        }
    }, []);

    const handleMainScroll = () => {
        // Skip if the scroll is from dragging the thumb
        // if (isDraggingScrollState) return;

        if (!createPoContainerRef.current) return;

        const { scrollTop, scrollHeight, clientHeight } = createPoContainerRef.current;
        const isAtBottom = Math.floor(Math.abs(scrollHeight - scrollTop - clientHeight)) <= 10; // 5px threshold
        // if (!isDraggingScrollState) setScrollPosition(scrollTop);
        if(scrollerRef.current && !isAtBottom && !isProgrammaticScroll) scrollerRef.current.updateScrollPosition(scrollTop);
        const header = headerContainerRef.current;
        // Handle header opacity
        if (header) {
            let opacity = 0;
            if (scrollTop > 52) {
                opacity = Math.min(scrollTop / 152, 1);
            }
            (header as HTMLElement).style.opacity = opacity.toString();
        }
        // Enable/disable the table scroll based on main scroll position
        if (addPoLineTableRef.current) {
            setHidePoLineScroll(!isAtBottom);
        }
    }

    // Replace the handleNavigateAway function
    const handleNavigateAway = (targetRoute) => {
        const { bomDataIdToRefresh } = useCreatePoStore.getState();
        if (bomDataIdToRefresh) {
            socket?.emit('getBomDataById', { bom_upload_id: bomDataIdToRefresh });
        }
        if (location.pathname === routes.savedBom) {
            setCreatePoDataFromSavedBom(null);
            setBomProductMappingDataFromSavedBom(null);
            setBomProductMappingSocketData(null);
        } else {
            const isDirty = isCreatePoDirty || !isCreatePOModule;
            const message = isCreatePoDirty
                ? navigationConfirmMessages.unsavedChanges
                :
                navigationConfirmMessages.confirmLeave;
            navigateWithConfirmation(isDirty, targetRoute, undefined, message);
        }
        setBomDataIdToRefresh(null);
    };
    
    const navigateWithConfirmation = (shouldConfirm: boolean = false, to: string, options?: any, message?: string ) => {
        const targetRoute = to ? to : location.pathname;
      if (shouldConfirm) {
        handleConfirmNavigation(targetRoute, options, message);
        return;
      }
      // If no confirmation needed, just navigate
      navigate(targetRoute, options);
    };
    // Add the navigation interception
    const handleConfirmNavigation = (to, options, message) => {
        showCommonDialog(
            null, 
            message, 
            null, 
            resetDialogStore, 
            [
                {
                    name: 'Yes', 
                    action: () => handleDialogYesBtn(to, options)
                }, 
                {
                    name: 'No', 
                    action: resetDialogStore
                }
            ]
        );
    };

    const handleDialogYesBtn = (to, options) => {
        handleLeavePageAction();
        setIsCreatePoDirty(false)
        navigate(to, options);
        resetDialogStore();
    }
    // useEffect(() => {
    //     if(!hidePoLineScroll){
    //         setTimeout(() => {
    //             if(scrollerRef.current) scrollerRef.current.updateScrollPosition((addPoLineTableRef.current?.scrollTop??0) + 204);
    //         }, 400);
    //     }
    // }, [hidePoLineScroll])

    const selectMostRecentOrder = (cancelledOrderId?: string | null, shouldSetInitiallySelected: boolean = false) => {
      if (orderManagementData?.length > 0) {
          const sortedOrders = [...orderManagementData].sort((a, b) => {
              const aCreatedDate = userData?.data?.type === userRole.buyerUser ? dayjs.utc(a.created_date) : dayjs.utc(a.claimed_date);
              const bCreatedDate = userData?.data?.type === userRole.buyerUser ? dayjs.utc(b.created_date) : dayjs.utc(b.claimed_date);
              return bCreatedDate.diff(aCreatedDate);
          });

          const remainingOrders = cancelledOrderId ? sortedOrders.filter(order => order.id !== cancelledOrderId) : sortedOrders;

          if (remainingOrders?.length > 0) {
              setTimeout(() => {
                  handleItemClick(remainingOrders[0]);
                  if (shouldSetInitiallySelected) {
                    hasInitiallySelected.current = true;
                }

                  setTimeout(() => {
                      // scroll to the selected order in the left panel
                      const { setClickThisId } = useLeftPanelStore.getState();
                      setClickThisId(remainingOrders[0].id);
                  }, 300);
              }, 500);
          }
      }
  };

    
    const saveCreatePOUserActivity = (sessionId: string, checkOutPoNumber: string | undefined, checkOutError: string | undefined) => {
        const payload = {
            "data": {
                "session_id": sessionId,
                "po_number": checkOutPoNumber ?? null,
                "response": checkOutError ?? null,
                "internal_po_number": poHeaderFormWatch("buyer_internal_po") ? poHeaderFormWatch("buyer_internal_po") : null,
                "shipping_details": poHeaderFormWatch("shipping_details") ? poHeaderFormWatch("shipping_details") : null,
                "delivery_date": poHeaderFormWatch("delivery_date") ? poHeaderFormWatch("delivery_date") : null,
                "payment_method": createPoTableWatch("payment_method") ? createPoTableWatch("payment_method") : null,
            }
        }
        logUserActivity({ url: `${import.meta.env.VITE_API_SERVICE}/user/create_po_open_close`, payload });
    }
    const saveUserActivity = (checkOutPoNumber, checkOutError) => {
        if(isCreatePOModule){
            saveCreatePOUserActivity(sessionId, checkOutPoNumber, checkOutError);
        }
    }

    const handleShowConfirmPoHeader = () => {
        // setShowConfirmPoHeaderDialog(true);
        setIsHeaderDetailsConfirmed(false);
    }

    useEffect(() => {
      if (location.state?.isFromCheckout) {
        locationStateRef.current = location.state;
    }

    if (location?.state?.reset !== undefined) {
      locationStateRef.current = location.state;
    }
    
    const storedState = locationStateRef.current;
      if (orderManagementData?.length > 0 && location.pathname !== routes.searchResult && !hasInitiallySelected.current && !storedState?.isFromCheckout && storedState?.reset !== false) {
         selectMostRecentOrder(null, true);
      }
      else{
        hasInitiallySelected.current = true;
      }
    }, [orderManagementData, location.state?.isFromCheckout, location.pathname, location?.state?.reset]);

    if(!selectedQuote && orderManagementData?.length === 0){
        return (
            <>
                <div className={styles.orderManagementNoDataContainer}>
                    <div className={styles.noOrderDetailsContainer}>
                        <p>NO DETAILS TO DISPLAY</p>
                        <p>SELECT AN ORDER FROM THE LEFT</p>
                    </div>
                </div>
            </>
        )
    }

    const handleStoreUndoStack = (undoData: any) => {
        let nameParts = undoData?.id?.split('.') || [];
        if(undoData?.multipleData){
            nameParts = Object.keys(undoData.multipleData)[0].split('.');
        }
        const currentValue = nameParts.reduce((obj, key) => obj?.[key], orderManageMentInitialData);
        if(undoStack.length === 0 ){
            if(undoData.currentValue !== currentValue){
                setUndoStack([...undoStack, undoData]);
            }
        }else{
            setUndoStack([...undoStack, undoData]);
        }
    }

    const handleUndo = () => {
        const lastElement = undoStack[undoStack.length - 1];
        if(lastElement.from === "header"){
            setTimeout(() => {
                if (addPoLineTableRef.current) {
                    addPoLineTableRef.current.scrollTop = 0;
                }
            }, 200)
            
            handleScrollToBomLine(0);
            setTimeout(() => {
                scrollToTop();
            }, 600)
            setTimeout(() => {
                handleUndoForHeader(lastElement);
            }, 700)
        }else{
            handleUndoForLine(lastElement);
        }
        const newUndoStack = undoStack.slice(0, -1);
        setUndoStack(newUndoStack);
    }

    const handleDownload = (url: string, name: string) => {
        console.log("handleDownload @>>>>>>>", url)
        if(url){
            downloadFilesUsingFetch(url, name, 'application/pdf');
        }
    }

    const handleDuplicatePo = () => {
        handleSaveDuplicatePo();
        setOpenDuplicatePoDialog(true);
    }

    const handleViewDuplicatePoDraft = (formData: { jobPoNumber: string; delivery_date: string }) => {
        // Navigate to the duplicate PO draft or open it in a new window
        const localDuplicatedPoData = getLocal(localStorageKeys.poPurchasing, null);
        if(localDuplicatedPoData){
            setLocal(localStorageKeys.poPurchasing, {...localDuplicatedPoData, buyer_internal_po: formData.jobPoNumber, delivery_date: formData.delivery_date});
        }
        handleCloseDuplicatePoDialog();
        // Add your navigation logic here
    }

    const handleUndoDuplication = () => {
        clearLocal(localStorageKeys.poPurchasing);
        setOpenDuplicatePoDialog(false);
    }

    const handleCloseDuplicatePoDialog = async () => {
        try{
            console.log("handleCloseDuplicatePoDialog @>>>>>>>")
            setOpenDuplicatePoDialog(false);
            setShowLoader(true);
            // setSelectedQuote(null);
            // setCreatePoData(null);
            setOrderManageMentInitialData(null);
            let localDuplicatedPoData = getLocal(localStorageKeys.poPurchasing, null);
            const itemId = await saveDraftData(postDraftPo, localStorageKeys.poPurchasing);
            console.log("response @>>>>>>>", itemId);
            if(itemId && localDuplicatedPoData){
                console.log("localDuplicatedPoData @>>>>>>>", localDuplicatedPoData);
                localDuplicatedPoData.id = itemId;
                localDuplicatedPoData.cameFromDuplicatePo = true;
                await handleLoadQuoteData(itemId, localDuplicatedPoData);
                navigate(routes.createPoPage, {state: { from: 'duplicatePo' }});
            }
        } catch(error){
            setShowLoader(false);
            console.error("error @>>>>>>>", error);
            clearLocal(localStorageKeys.poPurchasing);
            showCommonDialog(null, error?.message, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
        }
    }
    
    const handleLoadQuoteData = async (itemId: string, itemData: any) => {
        setShowLoader(true);
        const fetchItemDataUsingId =  purchasingList.find((item: any) => item.id === itemId);
        console.log("fetchItemDataUsingId @>>>>>>>", fetchItemDataUsingId, itemData);
        const item = fetchItemDataUsingId ? fetchItemDataUsingId : itemData;
        setIsCreatePoDirty(false);
        const draftLines = await getDraftLines(itemId);
        formatAndUpdateDraftPoData(item, draftLines?.data);
        setShowLoader(false);
    }

    const handleCancelSelectedLines = async () => {
        console.log("handleCancelSelectedLines @>>>>>>>")
        if (selectedQuote?.seller_name) {
            showCommonDialog(null, "Are you sure you want to CANCEL selected lines?", null, resetDialogStore, [
                {
                    name: <span>Yes <span> CANCEL LINES </span></span>,
                    action: handleRemoveLineBtnClick
                },
                {
                    name: <span>No <span> TAKE ME BACK </span></span>,
                    action: resetDialogStore
                }
            ]);
        } else {
            const linesData = structuredClone(orderManageMentInitialData?.cart_items || []);
            const selectedCancelLinesIdSet = new Set(selectedCancelLines);
            const filteredItems = structuredClone(linesData).filter(item => !selectedCancelLinesIdSet.has(item.id) && !item?.line_cancel_date);
            
            await handlePriceIntegration(undefined, filteredItems, undefined, false);

            const filterMap = new Map(filteredItems.map(item => [item.id, item]));
            const repriceLinesData = linesData?.map((item, index) => {
                const filteredItem = filterMap.get(item.id);
                const currentQty = item.qty || 0;
                const currentPrice = item.buyer_price_per_unit || 0;
                const currentExt = item?.line_cancel_date ? 0 : item.buyer_line_total || 0;
                
                // Calculate reprice values based on cancellation
                let qty = currentQty;
                let repriceUnit = currentPrice;
                let repriceExt = currentExt;
                
                // If this line is selected for cancellation, set qty to 0
                if (selectedCancelLines.includes(item.id) || item?.line_cancel_date) {
                    qty = 0;
                    repriceUnit = 0;
                    repriceExt = 0;
                } else {
                    repriceUnit = filteredItem?.buyer_price_per_unit || 0;
                    repriceExt = filteredItem?.buyer_line_total || 0;
                }
                const variance = repriceExt - currentExt;
                
                return {
                    id: item.id,
                    description: item.description || '',
                    qty,
                    qty_unit: item.qty_unit?.toUpperCase() || 'PC',
                    current_price: parseFloat(currentPrice),
                    price_unit: item.price_unit?.toUpperCase() || 'PC',
                    current_ext: parseFloat(currentExt),
                    reprice_unit: parseFloat(repriceUnit),
                    reprice_ext: parseFloat(repriceExt),
                    variance: variance,
                    isLineCancelled: !!(selectedCancelLines.includes(item.id) || item?.line_cancel_date)
                };
            }) || [];
            setRepriceLinesData(repriceLinesData);
            setOpenCancelLineWhenUnclaimedDialog(true);
        }
    }

    const handleCancelEntireOrder = () => {
        console.log("handleCancelEntireOrder @>>>>>>>")
        showCommonDialog(null, "Are you sure you want to Cancel this entire order?", null, resetDialogStore, [
            {
                name: <span><span>Yes</span> <span> CANCEL ORDER </span></span>,
                action: cancelOrder
            },
            { 
                name: <span><span>No</span> <span> TAKE ME BACK </span></span>,
                action: resetDialogStore
            }
        ]);
    }

    
    const cancelOrder = async () => {
        try {
            setShowLoader(true);
            resetDialogStore();
            if(selectedQuote?.seller_name){
                const payload = {
                    data: {
                        po_number: selectedQuote?.buyer_po_number,
                        order_level: {
                            is_order_canceled: 1
                        }
                    }
                }
                console.log("cancel order payload when order is claimed @>>>>>>>", payload);
                const response = await disputeOrderMutation(payload);
                if(response?.data?.error_message){
                    console.log("error_message", response?.data?.error_message);
                    showCommonDialog(null, response?.data?.error_message, null, resetDialogStore, [
                        {
                            name: commomKeys.errorBtnTitle,
                            action: resetDialogStore
                        }
                    ]);
                    return;
                }
                setShowLoader(false);
            } else {
                const payload = {
                    po_number: selectedQuote?.buyer_po_number,
                    type: orderConfirmationConst.buyerCancel
                }
                console.log("cancel order payload when order is not claimed @>>>>>>>", payload);
                const response = await cancelOrderMutation(payload);
                if(response.error_message){
                    console.log("error_message", response.error_message);
                    showCommonDialog(null, response.error_message, null, resetDialogStore, [
                        {
                            name: commomKeys.errorBtnTitle,
                            action: resetDialogStore
                        }
                    ]);
                    return;
                }
                selectMostRecentOrder(selectedQuote?.id);
                if(orderManagementData?.length === 1){
                    setSelectedQuote(null);
                    setOrderManageMentInitialData(null);
                }
            }
            setSelectedCancelLines([]);

        } catch (error) {
            console.log("error_message", error);
            showCommonDialog(null, commomKeys.error, null, resetDialogStore, [
                {
                    name: commomKeys.errorBtnTitle,
                    action: resetDialogStore
                }
            ]);
            setShowLoader(false);
        } finally {
            if(orderManagementData?.length === 1){
                setShowLoader(false);
            }
        }
    }

    const handleRemoveLineBtnClick = async () => {
        const nonCancelledIds = selectedQuote?.cart_items?.filter((item: any) => !item.line_cancel_date).map((item: any) => item.id) || [];
        const shouldSelectMostRecent = nonCancelledIds.length === selectedCancelLines?.length && 
          nonCancelledIds.every((id: any) => selectedCancelLines.includes(id));
        try {
            setShowLoader(true);
            resetDialogStore();
            if(selectedQuote?.seller_name){
                const payload = {
                    data: {
                    po_number: selectedQuote?.buyer_po_number,
                    order_line_level: selectedCancelLines.map((line) => {
                        const data = selectedQuote?.cart_items?.find((item) => item.id === line);
                        return {
                        po_line: data.po_line,
                        qty: 0,
                        qty_unit: data.qty_unit,
                        action: "cancel_line"
                    }})
                }}
                console.log("cancel line payload when order is claimed @>>>>>>>", payload);
                const response = await disputeOrderMutation(payload);
                if(response?.data?.error_message){
                    console.log("error_message", response?.data?.error_message);
                    showCommonDialog(null, response?.data?.error_message, null, resetDialogStore, [
                        {
                            name: commomKeys.errorBtnTitle,
                            action: resetDialogStore
                        }
                    ]);
                    return;
                }
            } else {
                const filterSet = new Set(selectedCancelLines);
                const list: any = selectedQuote?.cart_items || [];
                const payload = { 
                    "po_number": selectedQuote?.buyer_po_number, 
                    "type": orderConfirmationConst.buyerCancel, 
                    "purchase_order_line_id": list.reduce((acc, item) => {
                        if (filterSet.has(item.id)) {
                          acc[item.po_line.toString()] = item.id;
                        }
                        return acc;
                      }, {})
                }
                console.log("cancel line payload when order is not claimed @>>>>>>>", payload);
    
                const response = await cancelOrderMutation(payload);
                console.log("response", response);
                if (response.error_message) {
                    console.log("error", response.error_message);
                    showCommonDialog(null, response.error_message, null, resetDialogStore, [
                        {
                            name: commomKeys.errorBtnTitle,
                            action: resetDialogStore
                        }
                    ]);
                    return;
                }
                if (shouldSelectMostRecent){
                    if(orderManagementData?.length === 1){
                        setSelectedQuote(null);
                        setOrderManageMentInitialData(null);
                    }
                    selectMostRecentOrder(selectedQuote?.id);
                }
                setOpenCancelLineWhenUnclaimedDialog(false);
            }
            setSelectedCancelLines([]);
        }
        catch (error: any) {
            console.log("error", error);
            showCommonDialog(null, commomKeys.errorContent, null, resetDialogStore, [
                {
                    name: commomKeys.errorBtnTitle,
                    action: resetDialogStore
                }
            ]);
            setShowLoader(false);
        } finally {
            if(!shouldSelectMostRecent){
                setShowLoader(false);
            }
        }
    }
    const handleRequestChanges = async (requestType: string) => {
        try {
            console.log("handleRequestChanges @>>>>>>>", undoStack);
            const undoNewSet = new Set(undoStack.map(item => item?.uniqueId?.toString()));
            console.log("undoNewSet @>>>>>>>", undoNewSet, undoStack, createPoResultCopy, orderManageMentInitialData);
            
            // Initialize base payload
            let payload: any = {
                data: {
                    po_number: orderManageMentInitialData?.buyer_po_number
                }
            };
    
            if(selectedQuote?.seller_company_name){
    
                // Check and add PO name changes
                if (undoNewSet.has("buyer_internal_po")) {
                    payload.data.po_name = poHeaderFormWatch?.("buyer_internal_po");
                }
        
                // Add order_level only if there are delivery or shipping changes
                const hasDeliveryChanges = undoNewSet.has("delivery_date");
                const hasShippingChanges = undoNewSet.has("shipping_details") && (
                    poHeaderFormWatch?.("shipping_details.line1")?.trim() !== orderManageMentInitialData?.shipping_details?.line1?.trim() ||
                    poHeaderFormWatch?.("shipping_details.line2")?.trim() !== orderManageMentInitialData?.shipping_details?.line2?.trim() ||
                    poHeaderFormWatch?.("shipping_details.city")?.trim() !== orderManageMentInitialData?.shipping_details?.city?.trim() ||
                    poHeaderFormWatch?.("shipping_details.state_id") !== orderManageMentInitialData?.shipping_details?.state_id ||
                    poHeaderFormWatch?.("shipping_details.zip")?.trim() !== orderManageMentInitialData?.shipping_details?.zip?.trim()
                );
                const hasStateZipChanges = undoNewSet.has("shipping_details") && (
                    poHeaderFormWatch?.("shipping_details.state_id") !== orderManageMentInitialData?.shipping_details?.state_id ||
                    poHeaderFormWatch?.("shipping_details.zip") !== orderManageMentInitialData?.shipping_details?.zip
                );
        
                if (hasDeliveryChanges || hasShippingChanges) {
                    payload.data.order_level = {};
        
                    if (hasDeliveryChanges) {
                        payload.data.order_level.deliver_by = {
                            delivery_date: dayjs(poHeaderFormWatch?.("delivery_date")).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit),
                            // action: "counter"
                        };
                    }
        
                    if (hasShippingChanges) {
                        payload.data.order_level.deliver_to = {
                            line1: poHeaderFormWatch?.("shipping_details.line1"),
                            line2: poHeaderFormWatch?.("shipping_details.line2")?.trim() || null,
                            city: poHeaderFormWatch?.("shipping_details.city"),
                            state_id: poHeaderFormWatch?.("shipping_details.state_id"),
                            zip: poHeaderFormWatch?.("shipping_details.zip"),
                            // action: "counter"
                        };
                    }
                }
        
                // Add order_line_level only if there are line item changes
                const lineChanges: any[] = [];
        
                if (createPoResultCopy) {
                    Object.keys(createPoResultCopy).forEach((id, index) => {
                        if (undoNewSet.has(id)) {
                            const itemData = createPoResultCopy[id];
                            if (!itemData) return;
        
                            if (itemData.id) {
                                // Only add if quantity changed
                                const qtyChanged = itemData.qty !== orderManageMentInitialData?.cart_items?.[id]?.qty;
                                if (qtyChanged) {
                                    lineChanges.push({
                                        po_line: itemData?.po_line,
                                        qty: parseFloat(itemData.qty),
                                        qty_unit: itemData.qty_unit.toUpperCase(),
                                        // action: ""
                                    });
                                }
                            } else {
                                lineChanges.push({
                                    po_line: index + 1,
                                    product_id: itemData.product_id,
                                    qty: parseFloat(itemData.qty),
                                    qty_unit: itemData.qty_unit.toUpperCase(),
                                    price_unit: itemData.price_unit.toUpperCase(),
                                    domestic_material_only: itemData.domesticMaterialOnly,
                                    product_tag: itemData.product_tag,
                                    delivery_date: itemData.delivery_date,
                                    action: "add_line"
                                });
                            }
                        }
                    });
                }
        
                if (lineChanges.length > 0) {
                    payload.data.order_line_level = lineChanges;
                }
                if(hasStateZipChanges){
                    showCommonDialog("Hold up!", "Changing the delivery destination may cause the seller to be unable to fulfill this order and cancel their order claim.", null, resetDialogStore, [
                        {
                            name: <span>Understood <span> Proceed with Change </span></span>,
                            action: ()=>{handleRequestChangesClaimedApiCall(payload); resetDialogStore();}
                        },
                        {
                            name: <span>Cancel <span> Keep Current Destination </span></span>,
                            action: resetDialogStore
                        }
                    ]);
                } else {
                    handleRequestChangesClaimedApiCall(payload);
                }
            } else {
                // payload.data.po_number = orderManageMentInitialData?.buyer_po_number;
                if(requestType === "exitEditMode"){
                    payload.data.internal_po_number = orderManageMentInitialData?.buyer_internal_po;
                    payload.data.delivery_date = dayjs(orderManageMentInitialData?.delivery_date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit);
                    payload.data.shipping_details = orderManageMentInitialData?.shipping_details;
                    payload.data.shipping_details.line2 = orderManageMentInitialData?.shipping_details?.line2 ? orderManageMentInitialData?.shipping_details?.line2?.trim() : null;
                    payload.data.cart_items = [];
                } else {
                    payload.data.internal_po_number = poHeaderFormWatch?.("buyer_internal_po");
                    payload.data.delivery_date = dayjs(poHeaderFormWatch?.("delivery_date")).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit);
                    payload.data.shipping_details = poHeaderFormWatch?.("shipping_details");
                    payload.data.shipping_details.line2 = poHeaderFormWatch?.("shipping_details.line2")?.trim() || null;
                    const lineChanges = [];
                    if(createPoResultCopy){
                        Object.keys(createPoResultCopy).forEach((id, index) => {
                            const itemData = createPoResultCopy[id];
                            if (!itemData) return;
                            if (undoNewSet.has(id) && (poHeaderFormWatch?.("shipping_details.zip") === orderManageMentInitialData?.shipping_details?.zip && poHeaderFormWatch?.("shipping_details.state_id") === orderManageMentInitialData?.shipping_details?.state_id || !itemData?.id)) {
                                lineChanges.push(itemData);
                            }
                        });
                    }
                    payload.data.cart_items = newFormatCartItems(lineChanges);
                }
                console.log("Final payload @>>>>>>>", payload);
                const response = await editUnclaimedOrder(payload);
                console.log("response @>>>>>>>", response);
                if (response?.data?.error_message) {
                    showCommonDialog(null, response?.data?.error_message, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
                    return;
                } else {
                    setIsEditingPo(false);
                    setUndoStack([]);
                    clearLocal(localStorageKeys.poPurchasing);
                    return response;
                }
            }
            // return payload;
        } catch (error) {
            console.log("error @>>>>>>>", error);
            showCommonDialog(null, error?.message, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
            return;
        }
    };

    const handleRequestChangesClaimedApiCall = async (payload: any) => {
        console.log("Final payload @>>>>>>>", payload);
        const response = await disputeOrderMutation(payload);
        console.log("response @>>>>>>>", response);
        if (response?.data?.error_message) {
            showCommonDialog(null, response?.data?.error_message, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
            return;
        } else {
            setIsEditingPo(false);
            setUndoStack([]);
            clearLocal(localStorageKeys.poPurchasing);
            return response;
        }

    }

    const handleExitEditMode = async() => {
        if(!selectedQuote?.seller_name){
            const payload = await handleRequestChanges("exitEditMode");
            console.log("payload @>>>>>>>", payload);
            if(payload?.data?.error_message){
                showCommonDialog(null, payload?.data?.error_message, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
                return;
            }
        }
        setIsEditingPo(false);
        setUndoStack([]);
        clearLocal(localStorageKeys.poPurchasing);
        handleExitEditModeTableData();
    }

    const handleEditOrderBtnClick = () => {
        if(selectedQuote?.seller_name){
            setIsEditingPo(true);
            const hasLineDisputeCounter = selectedQuote?.cart_items?.some(item => !!item?.line_dispute_counter);
            const lastDeliverToCounterData = selectedQuote?.order_level_dispute?.deliver_to?.length > 0 ? selectedQuote?.order_level_dispute?.deliver_to[selectedQuote?.order_level_dispute?.deliver_to.length - 1] : {};
            console.log("hasLineDisputeCounter @>>>>>>>", hasLineDisputeCounter);
            setIsOrderLineChanges(hasLineDisputeCounter);
            return;
        }
        showCommonDialog(null, `${clientName} sellers won’t be able to claim this order until your edits are complete. Proceed?`, null, resetDialogStore, [
            {
                name: <span><span>Yes</span> <span> EDIT ORDER </span></span>,
                action: handleEditOrder
            },
            {
                name: <span><span>No</span> <span> TAKE ME BACK </span></span>,
                action: resetDialogStore
            }
        ]);

    }

    const handleEditOrder = async () => {
        try {
            // const payload = {
            //     "data": {
            //         "po_number": selectedQuote?.buyer_po_number
            //     }
            // }
            const response = await removeOrder(selectedQuote?.buyer_po_number);
            console.log("response @>>>>>>>", response);
            if (response?.data?.error_message) {
                showCommonDialog(null, response?.data?.error_message, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
            } else {
                if (!selectedQuote?.seller_name) {
                    setSelectedCancelLines([]);
                }
                setIsEditingPo(true);
                setIsOrderLineChanges(false);
                setIsStateZipValChange(false);
                resetDialogStore();
                console.log("response @>>>>>>>", response);
            }
        } catch (error) {
            console.log("error @>>>>>>>", error);
            showCommonDialog(null, error?.message, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
        }
    }

    const handleCloseOrderConfirmationPopup = () => {
        setShowOrderConfirmationPopup(false);
        setTimeout(() => {
            setOrderConfirmationData(null);
        }, 100);
    }

    const handleCancelOrderOnSeller = async () => {
        try {
            resetDialogStore();
            setShowLoader(true);
            setCancelOrderOnSellerBtnDisabled(true);
            if (selectedQuote?.seller_po_number) {
                const payload = {
                    data: {
                        po_number: selectedQuote?.seller_po_number?.replace("P","S"),
                        type: orderConfirmationConst.sellerCancel
                    }
                }
                const res = await cancelOrderOnSeller(payload)
                if (res) {
                    if(orderManagementData?.length === 1){
                        setSelectedQuote(null);
                    }
                    setOrderManageMentInitialData(null);

                   selectMostRecentOrder(selectedQuote?.id);
                }
            }


        } catch (error) {
            console.log("error @>>>>>>>", error);
            showCommonDialog(null, error?.message, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
            setShowLoader(false);
        } finally {
            if(orderManagementData?.length === 1){
                setShowLoader(false);
            }
            setCancelOrderOnSellerBtnDisabled(false);
        }
    }

    const handleCancelOrderSellerBtnClick = () => {
        showCommonDialog(null, "Are you sure you want to Cancel this order?", null, resetDialogStore, [
            {
                name: <span>Yes <span> CANCEL LINES </span></span>,
                action: handleCancelOrderOnSeller
            },
            {
                name: <span>No <span> TAKE ME BACK </span></span>,
                action: resetDialogStore
            }
        ]);
    }


    return (
        <div className={clsx(styles.createPoContent,styles.orderManagementContainer, 'bgBlurContent' , isBuyer ? styles.buyerOrderManagementContainer : styles.sellerOrderManagementContainer)}>
                  
            <div className={styles.formInnerContent} ref={HeaderDetailsConfirmedRef}>
                {/* <div className={styles.headerNoteCreatePO}>
                            <span className={styles.leftIcon}><WarningIcon /></span>

                            <span className={styles.headerNoteText}>All delivered material will be new (aka "prime"), fulfilled to the defined specification,
                                loaded/packaged for forklift and/or magnetic offloading and have mill test reports.</span>
                            <span className={clsx(styles.headerNoteText, styles.marginTop8)}>The maximum bundle weight is 5,000 pounds.</span>
                            <span className={styles.rightIcon}><WarningIcon /></span>
                        </div> */}
                <div className={clsx(styles.tblWrapper, 'w100')}>
                    {/* dynamic Header Component */}
                    <DynamicHeader 
                        component={globalDisputeComponent} 
                        componentProps={globalDisputeProps} 
                    />
                    
                    <div id="buttonContainer" className={clsx(styles.buttonContainer , styles.headerToolbar)}>
                        {isSeller ? (
                            <>
                                <div className={styles.buttonContainerOrder}>
                                        <>
                                            <div className={styles.exportContainer} ref={downloadContainerRef}>
                                                <button
                                                    className={clsx(styles.selectedProductHeaderButton, styles.exportButton)}
                                                    onClick={() => setIsDownloadDropdownOpen(!isDownloadDropdownOpen)}
                                                >
                                                    Download
                                                    <span className={styles.exportArrow}><DropDownArrowIcon /></span>
                                                </button>
                                                {isDownloadDropdownOpen && (
                                                    <div className={styles.exportDropdown}>
                                                        <button
                                                            className={styles.exportOption}
                                                            onClick={() => handleDownload(selectedQuote?.seller_claimed_pdf_url, 'Order Confirmation')}
                                                            disabled={!selectedQuote?.seller_claimed_pdf_url}
                                                        >
                                                            Order Confirmation
                                                        </button>
                                                        <button
                                                            className={styles.exportOption}
                                                            onClick={() => downloadCertificate(import.meta.env.VITE_API_SERVICE + '/user/seller/resale/certificate')}
                                                        >
                                                            Bryzos’ Resale Cert
                                                        </button>
                                                    </div>
                                                )}
                                            </div>
                                            <button onClick={() => handleCancelOrderSellerBtnClick()} disabled={isOrderCanceled || isCancelOrderDisputeThere || isOrderClosed}> Cancel Order </button>
                                        </>
                                </div>
                            </>
                        ) :
                            (
                                <div className={styles.buttonContainerOrder}>
                                    {isEditingPo ? (
                                        <>
                                            {!(selectedQuote?.in_dispute) &&
                                                <button onClick={() => handleUndo()} disabled={undoStack.length === 0} > Undo </button>
                                            }
                                            <button className={styles.requestChangesButton} onClick={handleRequestChanges} disabled={(undoStack.length === 0 || disableConvertToPoButton)} > Request Changes </button>
                                        </>
                                    ) : (
                                        <>
                                            <button data-hover-video-id="om-edit" onClick={handleEditOrderBtnClick} disabled={isOrderCanceled || isCancelOrderDisputeThere || isOrderClosed} > Edit Order <EditIcon /></button>
                                            <div className={styles.exportContainer} ref={downloadContainerRef}>
                                                <button
                                                    className={clsx(styles.selectedProductHeaderButton, styles.exportButton)}
                                                    onClick={() => setIsDownloadDropdownOpen(!isDownloadDropdownOpen)}
                                                // disabled={selectedProductsData.length === 0}
                                                >
                                                    Download
                                                    <span className={styles.exportArrow}><DropDownArrowIcon /></span>
                                                </button>
                                                {isDownloadDropdownOpen && (
                                                    <div className={styles.exportDropdown}>
                                                        <button
                                                            className={styles.exportOption}
                                                            onClick={() => handleDownload(selectedQuote?.buyer_checkout_pdf_url, 'Order Confirmation')}
                                                            disabled={!selectedQuote?.buyer_checkout_pdf_url}
                                                        >
                                                            Order Confirmation
                                                        </button>
                                                        <button
                                                            className={styles.exportOption}
                                                            onClick={() => handleDownload(selectedQuote?.buyer_invoice_url, 'Invoice')}
                                                            disabled={!selectedQuote?.buyer_invoice_url}
                                                        >
                                                            Invoice
                                                        </button>
                                                        <button
                                                            className={styles.exportOption}
                                                            onClick={() => downloadCertificate(import.meta.env.VITE_API_SERVICE + '/user/buyer/w9form')}
                                                        >
                                                            Bryzos’ W-9
                                                        </button>
                                                    </div>
                                                )}
                                            </div>
                                            <button data-hover-video-id="om-duplicate" onClick={() => handleDuplicatePo()} disabled={isCancelOrderDisputeThere || isOrderCanceled || isDisputeThere} > Duplicate <DuplicateIcon /> </button>
                                        </>
                                    )}
                                    {
                                        (!isOrderCanceled && !isCancelOrderDisputeThere && !isOrderClosed) && ( 
                                    <div className={styles.exportContainer} ref={cancelContainerRef}>
                                        <button
                                            className={clsx(styles.selectedProductHeaderButton, styles.cancelOrderLineBtn, styles.exportButton, selectedCancelLines?.length > 0 && styles.cancelSelectedLines)}
                                            onClick={() => setIsCancelDropdownOpen(!isCancelDropdownOpen)}
                                        >
                                            <DeleteIcon />  Cancel {selectedCancelLines?.length ? `x ${selectedCancelLines?.length}` : ''}
                                            <span className={styles.exportArrow}><DropDownArrowIcon /></span>
                                        </button>
                                        {isCancelDropdownOpen && (
                                            <div className={clsx(styles.exportDropdown, styles.cancelDropdown)}>
                                                <button
                                                    className={styles.exportOption}
                                                    onClick={() => handleCancelSelectedLines()}
                                                    disabled={(!selectedCancelLines?.length || !!(isEditingPo && !selectedQuote?.seller_name))}
                                                >
                                                    Cancel Selected Lines
                                                </button>
                                                <button
                                                    className={styles.exportOption}
                                                    onClick={() => handleCancelEntireOrder()}
                                                    disabled={false}
                                                >
                                                    Cancel Entire Order
                                                </button>
                                            </div>
                                        )}
                                    </div>
                                        )

                                    }
                                    {isEditingPo && <button className={styles.exitEditMode} onClick={handleExitEditMode}> Exit Edit Mode <ExitIcon /> </button>}
                                </div>
                            )}
                            {/* dispute item counter */}
                             <div className={styles.rightGridDisputeItemCounter}>
                            {
                                (isDisputeThereForCurrentUser && !globalDisputeComponent) &&
                            <ItemAttentionNav
                                currentFocusedItemIndex={currentFocusedItemIndex}
                                attentionItems={attentionItems}
                                handleNext={handleAttentionNext}
                                handlePrev={handleAttentionPrev}
                                totalCount={totalDisputeCount}
                            />
                        }
                        {
                            isOrderCanceled && (
                                <>
                                    {
                                        Number(selectedQuote?.restocking_fee) > 0 ? ( 
                                            <span className={styles.canceledOrderDescription}>
                                                This order was canceled with a <br/> ${formatToTwoDecimalPlaces(selectedQuote?.restocking_fee)} restocking fee on {selectedQuote?.cancel_date}.    
                                            </span>
                                        ) : (
                                            <span className={styles.canceledOrderDescription}>
                                               { `This order was canceled by the ${userData?.data?.type === userRole.sellerUser ? "buyer" : "supplier"} on ${selectedQuote?.cancel_date}. `}
                                            </span>
                                        )
                                    }
                                </>
                            )
                        }
                        {
                            isOrderClosed && (
                                <>
                                    <span className={styles.canceledOrderDescription}>
                                        This order was closed.
                                    </span>
                                </>
                            )
                        }
                        </div>
                    </div>
                            <>
                                {/* <div className={clsx(styles.headerContainer)} ref={headerContainerRef} onClick={scrollToTop} >
                                    <div className={styles.headerItem}>
                                        {poHeaderFormWatch?.('internal_po_number')?.toUpperCase() || '-'}
                                    </div>
                                    <div className={styles.headerItem}>
                                        {poHeaderFormWatch?.('delivery_date') ?
                                            `${dayjs(poHeaderFormWatch?.('delivery_date')).format('ddd').toUpperCase()}, ${poHeaderFormWatch?.('delivery_date')}`
                                            : '-'
                                        }
                                    </div>
                                    <div className={styles.headerItem}>
                                        {poHeaderFormWatch?.('order_type')?.toUpperCase() || '-'}
                                    </div>
                                    <div className={styles.headerItem}>
                                        {<><span>{(poHeaderFormWatch?.('shipping_details.line1')?.toUpperCase() || '-')}</span> <span>{(poHeaderFormWatch?.('shipping_details.line2')?.toUpperCase() || '')}</span></>}
                                    </div>
                                </div> */}
                                <div className={styles.createPOContainerWrapper}>
                                    <div className={styles.createPOContainerWrapperInner} ref={createPOContainerWrapperInnerRef}>
                                    <Scroller
                                ref={scrollerRef}
                                containerHeight={createPOContainerWrapperInnerRef?.current?.clientHeight || 800}
                                contentHeight={maxScrollHeight}
                                onScrollChange={handleScrollerDrag}
                                rightOffset={5}
                                bottomOffset={20}
                                topOffset={20}
                                fixedThumbHeight={100}
                                />

                                <div className={clsx(styles.createPOContainer, styles.removeFooter)} ref={createPoContainerRef} onScroll={handleMainScroll}>
                        
                            {isSeller ?
                                <SellerHeaderInfo
                                    ref={createPoHeaderInfoRef}
                                    formInputGroupRef={formInputGroupRef}
                                    isCalendarOpen={isCalendarOpen}
                                    setIsCalendarOpen={setIsCalendarOpen}
                                    setOpenErrorDialog={setOpenErrorDialog}
                                    setErrorMessage={setErrorMessage}
                                    saveBomHeaderDetails={saveBomHeaderDetails}
                                    disableBidBuyNow={disableBidBuyNow}
                                    setOpenDeliveryToDialog={setOpenDeliveryToDialog}
                                    openDeliveryToDialog={openDeliveryToDialog}
                                    scrollToTop={scrollToTop}
                                    isSavedBom={isSavedBom}
                                    focusJobPoInput={focusJobPoInput}
                                    setCameFromSavedBom={setCameFromSavedBom}
                                    saveUserActivity={saveUserActivity}
                                    HeaderDetailsConfirmedRef={HeaderDetailsConfirmedRef}
                                    isHeaderDetailsConfirmed={isHeaderDetailsConfirmed}
                                    setIsHeaderDetailsConfirmed={setIsHeaderDetailsConfirmed}
                                    cameFromSavedBom={cameFromSavedBom}
                                    handleStoreUndoStack={handleStoreUndoStack}
                                    setIsStateZipValChange={setIsStateZipValChange}
                                    isOrderLineChanges={isOrderLineChanges}
                                    currentFocusedItem={currentFocusedItem}
                                    setFinalDisputePayload={setFinalDisputePayload}
                                    finalDisputePayload={finalDisputePayload}
                                    removeFromAttentionItems={removeFromAttentionItems}
                                    addToAttentionItems={addToAttentionItems}
                                />
                                :
                                <OrderManagementHeaderInfo
                                    styles={styles}
                                    ref={createPoHeaderInfoRef}
                                    formInputGroupRef={formInputGroupRef}
                                    isCalendarOpen={isCalendarOpen}
                                    setIsCalendarOpen={setIsCalendarOpen}
                                    setOpenErrorDialog={setOpenErrorDialog}
                                    setErrorMessage={setErrorMessage}
                                    saveBomHeaderDetails={saveBomHeaderDetails}
                                    disableBidBuyNow={disableBidBuyNow}
                                    setOpenDeliveryToDialog={setOpenDeliveryToDialog}
                                    openDeliveryToDialog={openDeliveryToDialog}
                                    scrollToTop={scrollToTop}
                                    isSavedBom={isSavedBom}
                                    focusJobPoInput={focusJobPoInput}
                                    setCameFromSavedBom={setCameFromSavedBom}
                                    saveUserActivity={saveUserActivity}
                                    HeaderDetailsConfirmedRef={HeaderDetailsConfirmedRef}
                                    isHeaderDetailsConfirmed={isHeaderDetailsConfirmed}
                                    setIsHeaderDetailsConfirmed={setIsHeaderDetailsConfirmed}
                                    cameFromSavedBom={cameFromSavedBom}
                                    handleStoreUndoStack={handleStoreUndoStack}
                                    setIsStateZipValChange={setIsStateZipValChange}
                                    isOrderLineChanges={isOrderLineChanges}
                                    componentType={'ORDER'}
                                    currentFocusedItem={currentFocusedItem}
                                    setFinalDisputePayload={setFinalDisputePayload}
                                    finalDisputePayload={finalDisputePayload}
                                    removeFromAttentionItems={removeFromAttentionItems}

                                />
                            }
                                    <CreatePoTable 
                                        ref={createPoTableRef}
                                        styles={styles}
                                        createPoContainerRef={createPoContainerRef}
                                        formInputGroupRef={formInputGroupRef}
                                        hidePoLineScroll={hidePoLineScroll}
                                        setHidePoLineScroll={setHidePoLineScroll}
                                        addPoLineTableRef={addPoLineTableRef}
                                        bomUploadResult={bomUploadResult}
                                        products={products}
                                        userPartData={userPartData}
                                        sessionId={sessionId}
                                        searchStringData={searchStringData}
                                        setSearchString={setSearchString}
                                        setDisableBidBuyNow={setDisableBidBuyNow}
                                        setOpenDeliveryToDialog={setOpenDeliveryToDialog}
                                        scrollToTop={scrollToTop}
                                        currentBomData={currentBomData}
                                        scrollPoHeaderToBottom={scrollPoHeaderToBottom}
                                        setFocusJobPoInput={setFocusJobPoInput}
                                        scrollerRef={scrollerRef}
                                        setMaxScrollHeight={setMaxScrollHeight}
                                        setCurrentBomData={setCurrentBomData}
                                        isHeaderDetailsConfirmed={isHeaderDetailsConfirmed}
                                        initializePoHeaderForm={initializePoHeaderForm}
                                        setOpenErrorDialog={setOpenErrorDialog}
                                        setErrorMessage={setErrorMessage}
                                        setCameFromSavedBom={setCameFromSavedBom}
                                        maxScrollHeight={maxScrollHeight}
                                        isProgrammaticScroll={isProgrammaticScroll}
                                        setIsProgrammaticScroll={setIsProgrammaticScroll}
                                        cameFromSavedBom={cameFromSavedBom}
                                        pricingBrackets={pricingBrackets}
                                        isCreatePOModule={isCreatePOModule}
                                        poHeaderFormWatch={poHeaderFormWatch}
                                        setBomUploadResult={setBomUploadResult}
                                        navigateWithConfirmation={navigateWithConfirmation}
                                        saveUserActivity={saveUserActivity}
                                        setCreatePoSessionId={setCreatePoSessionId}
                                        handleStoreUndoStack={handleStoreUndoStack}
                                        setIsOrderLineChanges={setIsOrderLineChanges}
                                        isStateZipValChange={isStateZipValChange}
                                        componentType={'ORDER'}
                                        currentFocusedItem={currentFocusedItem}
                                        setFinalDisputePayload={setFinalDisputePayload}
                                        finalDisputePayload={finalDisputePayload}
                                        removeFromAttentionItems={removeFromAttentionItems}
                                        findItemAttention={findItemAttention}
                                        addToAttentionItems={addToAttentionItems}
                                    />
                                </div>
                                </div>
                                </div>
                     
                            </>
                    {/* {isCreatePOModule && <div className={styles.backBtnMain}>
                        <button className={styles.cancelPOGoBack} onClick={() => handleNavigateAway(backNavigation)} >CANCEL</button>
                        {(channelWindow?.fetchPdf || channelWindow?.generatePdf) &&
                            <PdfMakePage getExportPoData={getExportPoData} buyingPreferenceData={buyerSetting} disabled={disableFormValidation && location.pathname !== routes.savedBom} getCartItems={getCartItems} />
                        }
                        {!isSavedBom && <button className={styles.savePOGoBack} onClick={() => {
                            showCommonDialog(null, 'This will exit Create PO. You can resume from your saved BOM. Do you want to continue?', null, resetDialogStore, [{ name: commomKeys.yes, action: handleSavePo }, { name: commomKeys.no, action: resetDialogStore }]);
                        }} disabled={disableFormValidation} >SAVE PO / BUY LATER</button>}
                    </div>} */}
                </div>
                <Dialog
                    open={openErrorDialog}
                    onClose={(event) => setOpenErrorDialog(false)}
                    transitionDuration={200}
                    hideBackdrop
                    disableScrollLock={true}
                    container={HeaderDetailsConfirmedRef.current}
                    classes={{
                        root: styles.ErrorDialog,
                        paper: styles.dialogContent
                    }}

                >
                    <p>{errorMessage}</p>
                    <button className={styles.submitBtn} onClick={(event) => { setOpenErrorDialog(false); }}>Ok</button>
                </Dialog>

                {/* <Dialog
                    open={!isHeaderDetailsConfirmed && cameFromSavedBom}
                    transitionDuration={200}
                    disableScrollLock={true}
                    container={HeaderDetailsConfirmedRef.current}
                    style={{
                        position: 'absolute',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: 'rgba(255, 255, 255, 0.1)',
                        border: '1px solid transparent',
                        borderRadius: '0px 0px 20px 20px',
                    }}
                    PaperProps={{
                        style: {
                            position: 'absolute',
                            top: '50%',
                            left: '50%',
                            transform: 'translate(-50%, -50%)',
                            margin: 0
                        }
                    }}
                    hideBackdrop
                    classes={{
                        root: styles.confirmHeaderDetailsPopup,
                        paper: styles.dialogContent
                    }}

                >

                    <div className={styles.confirmHeaderDetailsContainer}>
                        <span>CONFIRM HEADER DETAILS</span>
                        <button onClick={() => {
                            setIsHeaderDetailsConfirmed(true);
                        }}>PROCEED</button>
                    </div>


                </Dialog> */}

                <Dialog
                    open={openDuplicatePoDialog}
                    // onClose={() => setOpenDuplicatePoDialog(false)}
                    transitionDuration={200}
                      classes={{
                                        root: styles.duplicatePoDialog,
                                        paper: styles.dialogContent
                                    }}
                    hideBackdrop
                       style={{
                    position: 'absolute',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backdropFilter: 'blur(7px)',
                    WebkitBackdropFilter: 'blur(7px)',
                    backgroundColor: 'rgba(0, 0, 0, 0.23)',
                    border: '1px solid transparent'
                }}
                PaperProps={{
                    style: {
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        margin: 0
                    }
                }}
                    disableScrollLock={true}
                    container={HeaderDetailsConfirmedRef.current}
                >
                    <DuplicatePoDialog
                        onViewDraft={handleViewDuplicatePoDraft}
                        onUndo={handleUndoDuplication}
                        onClose={handleCloseDuplicatePoDialog}
                        setOpenErrorDialog={setOpenErrorDialog}
                        setErrorMessage={setErrorMessage}
                    />
                </Dialog>

                <Dialog
                    open={openCancelLineWhenUnclaimedDialog}
                    onClose={() => setOpenCancelLineWhenUnclaimedDialog(false)}
                    transitionDuration={200}
                    disableScrollLock={true}
                    classes={
                        {
                            root: styles.cancelLineWhenUnclaimedDialog,
                            paper: styles.dialogContent
                        }
                    }
                    container={mainWrapperRef?.current}
                    style={{
                        position: 'absolute',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backdropFilter: 'blur(7px)',
                        WebkitBackdropFilter: 'blur(7px)',
                        backgroundColor: 'rgba(0, 0, 0, 0.23)',
                    }}
                    PaperProps={{
                        style: {
                            position: 'absolute',
                            top: '50%',
                            left: '50%',
                            transform: 'translate(-50%, -50%)',
                            margin: 0,
                            width: '100%'
                        }
                    }}
                    hideBackdrop
                >
                    <CancelLineWhenUnclaimedDialog
                        onClose={() => setOpenCancelLineWhenUnclaimedDialog(false)}
                        onProceedWithCancelation={handleRemoveLineBtnClick}
                        selectedCancelLines={selectedCancelLines}
                        linesData = {repriceLinesData}
                    />
                </Dialog>
                <Dialog
                    open={showOrderConfirmationPopup}
                    onClose={() => handleCloseOrderConfirmationPopup()}
                    transitionDuration={200}
                    disableScrollLock={true}
                    classes={
                        {
                            root: styles.orderConfirmationDialog,
                            paper: styles.dialogContent
                        }
                    }
                    // container={mainWrapperRef?.current}
                    style={{
                        position: 'absolute',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backdropFilter: 'blur(7px)',
                        WebkitBackdropFilter: 'blur(7px)',
                        backgroundColor: 'rgba(0, 0, 0, 0.23)',
                        zIndex: 9999
                    }}
                    PaperProps={{
                        style: {
                            position: 'absolute',
                            top: '50%',
                            left: '50%',
                            transform: 'translate(-50%, -50%)',
                            margin: 0,
                            width: '100%'
                        }
                    }}
                    hideBackdrop
                >
                    <NewOrderConfirmation orderConfirmationData={orderConfirmationData} onClose={handleCloseOrderConfirmationPopup} />
                </Dialog>
            </div>
        </div>
    )
}


const OrderManagement = ({mainWrapperRef}: {mainWrapperRef: React.RefObject<HTMLDivElement>}) => {
    const location = useLocation(); // Add this line to get location

    return (
        <OrderManagementComponent  key={location.pathname} mainWrapperRef={mainWrapperRef}/>
    )
}

export default OrderManagement;


const CancelLineWhenUnclaimedDialog = ({
    onClose,
    onProceedWithCancelation,
    selectedCancelLines,
    linesData
}: {
    onClose: () => void;
    onProceedWithCancelation: () => void;
    selectedCancelLines: any;
    linesData: any;
}) => {
    const gridStyle = {
        height: '500px',
        width: '100%'
    };
    // Prepare data for the grid with pricing calculations
    // const preparedGridData = linesData?.map((item, index) => {
    //     const currentQty = item.qty || 0;
    //     const currentPrice = item.buyer_price_per_unit || 0;
    //     const currentExt = currentQty * currentPrice;
        
    //     // Calculate reprice values based on cancellation
    //     let repriceQty = currentQty;
    //     let repriceUnit = currentPrice;
        
    //     // If this line is selected for cancellation, set qty to 0
    //     if (selectedCancelLines.includes(item.id)) {
    //         repriceQty = 0;
    //         repriceUnit = 0;
    //     } else {
    //         // Apply volume discount adjustment (simplified logic)
    //         // In real implementation, this would be based on business rules
    //         const totalRemainingQty = linesData.reduce((sum, line) => {
    //             if (!selectedCancelLines.includes(line.id)) {
    //                 return sum + (line.qty || 0);
    //             }
    //             return sum;
    //         }, 0);
            
    //         // Adjust price based on remaining volume
    //         if (totalRemainingQty < 100) {
    //             repriceUnit = currentPrice * 1.1; // 10% increase for lower volume
    //         } else if (totalRemainingQty < 200) {
    //             repriceUnit = currentPrice * 1.05; // 5% increase
    //         }
    //     }
        
    //     const repriceExt = repriceQty * repriceUnit;
    //     const variance = repriceExt - currentExt;
        
    //     return {
    //         id: item.id,
    //         description: item.description || '',
    //         qty: repriceQty,
    //         qty_unit: item.qty_unit?.toUpperCase() || 'PC',
    //         current_price: parseFloat(currentPrice),
    //         price_unit: item.price_unit?.toUpperCase() || 'PC',
    //         current_ext: currentExt,
    //         reprice_unit: parseFloat(repriceUnit),
    //         reprice_ext: repriceExt,
    //         variance: variance
    //     };
    // }) || [];

    // Calculate totals for summary
    const currentTotal = linesData?.reduce((sum, item) => sum + (item.current_ext || 0), 0) || 0;
    const repriceTotal = linesData?.reduce((sum, item) => sum + (item.reprice_ext || 0), 0) || 0;
    console.log("linesData @>>>>>>>", linesData);
    
    
    const handleProceedWithCancelation = () => {
        console.log("handleProceedWithCancelation @>>>>>>>");
        onProceedWithCancelation();
    }

    const gridColumnDefs = [
        {
            headerName: 'LN',
            valueGetter: (params: any) => params.node.rowIndex + 1,
            sortable: false,
            width: 60,
            cellClass: 'cancelLineCell',
            headerClass: 'cancelLineHeader'
        },
        {
            headerName: 'DESCRIPTION',
            field: 'description',
            sortable: false,
            // flex: 1,
            // minWidth: 300,
            width: 300,
            cellClass: (params: any) => {
                const baseClass = 'cancelLineDescriptionCell';
                const dataClass = params?.data?.isLineCancelled ? 'cancelLineDataCell isLineCancelled' : 'cancelLineDataCell';
                return `${baseClass} ${dataClass}`;
            },
            headerClass: 'cancelLineHeader',
            cellRenderer: (props: any) => {
                console.log('first', props?.data?.description)
                return (
                    <div style={{ padding: '8px 0' }}>
                        <Tooltip title={props?.data?.description}
                         classes={{
                            tooltip: styles.cancelLineTooltip,
                        }}
                        >
                            <span>
                                {props?.data?.description}
                            </span>
                        </Tooltip>
                    </div>
                );
            }
        },
        {
            headerName: 'QTY',
            field: 'qty',
            sortable: false,
            width: 100,
            cellClass: (params: any) => {
                const baseClass = 'cancelLineCell';
                const dataClass = params?.data?.isLineCancelled ? 'cancelLineDataCell isLineCancelled' : 'cancelLineDataCell';
                return `${baseClass} ${dataClass}`;
            },
            headerClass: 'cancelLineHeader',
            cellRenderer: (props: any) => {
                return (
                    <div style={{ textAlign: 'center', padding: '8px 0' }}>
                        {props?.data?.qty} {props?.data?.qty_unit}
                    </div>
                );
            }
        },
        {
            headerName: 'CURRENT $/UNIT',
            field: 'current_price',
            sortable: false,
            width: 150,
            cellClass: (params: any) => {
                const baseClass = 'cancelLineCell';
                const dataClass = params?.data?.isLineCancelled ? 'cancelLineDataCell isLineCancelled' : 'cancelLineDataCell';
                return `${baseClass} ${dataClass}`;
            },
            headerClass: 'cancelLineHeader',
            cellRenderer: (props: any) => {
                console.log("props?.data?.current_price", props?.data?.current_price, typeof props?.data?.current_price)
                return (
                    <div style={{ textAlign: 'right', padding: '8px 0' }}>
                        {`$ ${props?.data?.current_price?.toFixed(2)} ${props?.data?.price_unit}`}
                    </div>
                );
            }
        },
        {
            headerName: 'REPRICE $/UNIT',
            field: 'reprice_unit',
            sortable: false,
            width: 150,
            cellClass: (params: any) => {
                const baseClass = 'cancelLineCell';
                const dataClass = params?.data?.isLineCancelled ? 'cancelLineDataCell isLineCancelled' : 'cancelLineDataCell';
                return `${baseClass} ${dataClass}`;
            },
            headerClass: 'cancelLineHeader',
            cellRenderer: (props: any) => {
                return (
                    <div style={{ textAlign: 'right', padding: '8px 0' }}>
                        {`$ ${props?.data?.reprice_unit?.toFixed(2)} ${props?.data?.price_unit}`}
                    </div>
                );
            }
        },
        {
            headerName: 'REPRICE EXT ($)',
            field: 'reprice_ext',
            sortable: false,
            width: 130,
            cellClass: (params: any) => {
                const baseClass = 'cancelLineCell';
                const dataClass = params?.data?.isLineCancelled ? 'cancelLineDataCell isLineCancelled' : 'cancelLineDataCell';
                return `${baseClass} ${dataClass}`;
            },
            headerClass: 'cancelLineHeader',
            cellRenderer: (props: any) => {
                return (
                    <div style={{ textAlign: 'right', padding: '8px 0' }}>
                        $ {props?.data?.reprice_ext?.toFixed(2)}
                    </div>
                );
            }
        },
        {
            headerName: 'VARIANCE',
            field: 'variance',
            sortable: false,
            flex:1,
            width: 100,
            cellClass: 'cancelLineVarianceCell',
            headerClass: 'cancelLineVarianceHeader',
            cellRenderer: (props: any) => {
                const variance = props?.data?.variance;
                const isPositive = variance > 0;
                const isNegative = variance < 0;
                const isZero = variance === 0;
                return (
                    <div style={{
                        textAlign: 'right',
                        padding: '8px 0'
                        // color: isPositive ? '#4caf50' : isNegative ? '#f44336' : 'inherit',
                    }}>
                        {isPositive ? '+' : isNegative ? '-' : isZero ? '' : ''} $ {Math.abs(variance)?.toFixed(2)}
                    </div>
                );
            }
        }
    ];

    return (
        <div>
            <button className={styles.closeButton} onClick={onClose}>
                      <CloseIcon/>
            </button>
            <div>
                <div className={styles.titlePopup}>
                    BEFORE YOU PROCEED WITH THAT CANCELATION...
                </div>
                <div className={styles.subTitlePopup}>
                    Your cancelation request reduces the volume of this order. This change affects the volume-based discount you received and requires a price adjustment.
                </div>
            </div>
            {linesData && linesData.length > 0 ? (
                <div className={'gridTheme'}>
                <div className={"ag-theme-alpine"} style={gridStyle}>
                    <AgGridReact
                        columnDefs={gridColumnDefs}
                        defaultColDef={{ resizable: true, singleClickEdit: true, lockVisible: true }}
                        rowData={linesData}
                        headerHeight={46}
                        rowHeight={88}
                        getRowStyle={(params) => {
                        return {
                            backgroundColor: params.node?.rowIndex % 2 === 0 ? '#0f0f14' : '#222329',
                            color: '#fff',
                            borderBottom: '0px solid transparent'
                        };
                    }}
                        getRowClass={(params) => 'cancelLineRow'}
                    />
                </div>
                </div>
            ) : (
                <div style={{ 
                    height: '500px', 
                    width: '100%', 
                    borderRadius: '4px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    background: '#2a2a2a',
                    color: '#fff'
                }}>
                    No data available
                </div>
            )}
            
            {/* Summary Section */}
            {linesData && linesData.length > 0 && (
                <div className={styles.cancelLineSummaryDataMain}>
                    <div className={styles.cancelLineSummary}>
                        <span>CURRENT<br/>MATERIAL TOTAL</span>
                        <span>$ {currentTotal.toFixed(2)}</span>
                    </div>
                    <div className={styles.cancelLineSummary}>
                        <span>REPRICE<br/>MATERIAL TOTAL</span>
                        <span>$ {repriceTotal.toFixed(2)}</span>
                    </div>
                </div>
            )}
             <div className={styles.cancelLineSummaryBtnMain}>
                <button onClick={onClose} className={styles.cancelLineBtn}>
                    NO
                    <span>TAKE ME BACK</span>
                </button>
                <button onClick={handleProceedWithCancelation} className={styles.cancelLineBtn}>
                    YES
                    <span>PROCEED WITH CANCELATION</span>
                </button>
            </div>
        </div>
    )
}