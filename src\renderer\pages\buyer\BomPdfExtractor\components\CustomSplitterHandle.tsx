import React from 'react';
import { ReactComponent as SplitIcon } from '../../../../assets/images/BOM-Extractor/split-icon.svg';
import styles from '../styles/BomExtractor.module.scss';
import clsx from 'clsx';
import { ResizeHandleProps } from 'src/renderer/component/BomProcessingWindow/ResizablePanels';

const CustomSplitterHandle = (props: ResizeHandleProps) => {
  return (
    <div className={clsx(styles.customSplitterHandle, styles.dragging)} {...props}>
      <SplitIcon 
        className={clsx(
          styles.splitIcon,
        )}
      />
    </div>
  );
};

export default CustomSplitterHandle; 