import React, { useEffect, useState } from 'react'
import CustomTextField from 'src/renderer/component/CustomTextField';
import InputWrapper from 'src/renderer/component/InputWrapper';
import clsx from 'clsx';
import styles from '../tabs/TabContent.module.scss';
import { CustomMenu } from 'src/renderer/pages/buyer/CustomMenu';

const CustomAddressComponent = ({ focusedInput, States, register, handleInputBlur, handleInputFocus, errors, control, setValue, setCustomAddressComponentOpen, setFocusedInput }: { focusedInput: string | null, States: any, register: any, handleInputBlur: any, handleInputFocus: any, errors: any, control: any, setValue   : any, setCustomAddressComponentOpen: any, setFocusedInput  : any }) => {
    
    // Only proceed if focusedInput is provided
    if (!focusedInput) return null;

    useEffect(() => {
        setTimeout(() => {
           const inputElement = document.querySelector(`input[name="${focusedInput}.line1"]`);
           if(inputElement){
            inputElement.focus();
           }
        }, 100);
    }, []);

    // Derive field names from focusedInput
    const line1 = `${focusedInput}.line1`;
    const line2 = `${focusedInput}.line2`;
    const cityField = `${focusedInput}.city`;
    const stateField = `${focusedInput}.state`;
    const zipField = `${focusedInput}.zip`;
    const stateCodeField = `${focusedInput}.stateCode`;

    return (
        <div className={clsx(styles.customAddressContainer)}>
            <InputWrapper>
                <CustomTextField
                    className={clsx(styles.inputCreateAccount, errors?.[focusedInput]?.line1 && styles.error)}
                    type='text'
                    autoFocus={true}
                    register={register(line1)}
                    placeholder='Line 1'
                    onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                        register(line1).onBlur(e);
                        handleInputBlur(line1)
                    }}
                    onFocus={() => handleInputFocus(line1)}
                    errorInput={errors?.[focusedInput]?.line1}
                />
            </InputWrapper>
            <InputWrapper>
                <CustomTextField
                    className={clsx(styles.inputCreateAccount, errors?.[focusedInput]?.line2 && styles.error)}
                    type='text'
                    // autoFocus={true}
                    register={register(line2)}
                    placeholder='Line 2'
                    onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                        register(line2).onBlur(e);
                        handleInputBlur(line2)
                    }}
                    onFocus={() => handleInputFocus(line2)}
                    errorInput={errors?.[focusedInput]?.line2}
                />
            </InputWrapper>
           
            <span className={styles.zipInputContainer}>
                <span className={styles.col1}>
                    <InputWrapper>
                        <CustomTextField
                            className={clsx(styles.inputCreateAccount, errors?.[focusedInput]?.city && styles.error)}
                            type='text'
                            register={register(cityField)}
                            placeholder='City'
                            onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                register(cityField).onBlur(e);
                                handleInputBlur(cityField)
                            }}
                            onFocus={() => handleInputFocus(cityField)}
                            errorInput={errors?.[focusedInput]?.city}
                        />
                    </InputWrapper>
                </span>
                <span className={clsx(styles.inputSection, styles.yourLocationAdressState,styles.col2, styles.bdrRadius0, styles.bdrRight0, errors[focusedInput]?.state && styles.borderOfError)}>
                    <CustomMenu
                        control={control}
                        name={stateField}
                        placeholder={'State'}
                        MenuProps={{
                                classes: {
                                    paper: clsx(styles.Dropdownpaper,styles.Dropdownpaper1),
                                    list: styles.muiMenuList,
                                    select: styles.selectClassName,
                                },
                            }}
                            className={clsx(styles.selectDropdown, styles.selectState)}
                        items={States.map((x: any) => ({ title: x.code, value: x.id }))}
                        onChange={(e: any) => {
                            States.map((item: any) => {
                                if(item.id === e.target.value) {
                                    setValue(stateCodeField, item.code)
                                }
                            })
                        }}
                    />
                </span>
                <span className={styles.col3}>
                <InputWrapper>
                        <CustomTextField
                            className={clsx(styles.inputCreateAccount, (errors?.[zipField] || errors?.[focusedInput]?.state) && styles.error)}
                            type='text'
                            maxLength={5}
                            register={register(zipField)}
                            placeholder='Zip Code'
                            onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                register(zipField).onBlur(e);
                                handleInputBlur(zipField)
                            }}
                            onFocus={() => handleInputFocus(zipField)}
                            errorInput={errors?.[focusedInput]?.state || errors?.[focusedInput]?.zip}
                            mode="wholeNumber"
                        />
                    </InputWrapper>
                </span>
              

            </span>
          
        </div>
    )
}

export default CustomAddressComponent
