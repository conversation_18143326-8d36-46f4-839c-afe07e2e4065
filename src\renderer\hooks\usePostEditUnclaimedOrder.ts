
import { useMutation } from "@tanstack/react-query";
import axios from "axios";

const usePostEditUnclaimedOrder = () => {

  return useMutation(async (data: any) => {
    try {
      const url = `${import.meta.env.VITE_API_ORDER_SERVICE}/buyer/edit/order`;
      const response = await axios.post(
        url,
        data
      );
      console.log("data @>>>>>>>", response.data);

     return response.data;
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default usePostEditUnclaimedOrder;
