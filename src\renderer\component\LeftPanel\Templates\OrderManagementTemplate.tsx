import { commom<PERSON><PERSON>s, emojiRemoverRegex, formatToTwoDecimalPlaces, noIdGeneric, priceUnits, useBuyerSettingStore, useCreatePoStore, useGlobalStore , getChannelWindow, format4DigitAmount, orderType, dateTimeFormat, useOrderManagementStore, userRole, useChatWithVendorStore } from "@bryzos/giss-ui-library";
import { calculateLineWeight, clearLocal, exportToExcel, fetchPrice, formatOrderWeightWithComma, getLocal, handleOrderManagementNavigation, setLocal, transformOrderToSheetData, updateOrderManagementData } from "src/renderer/helper";
import { useLeftPanelStore } from "../LeftPanelStore";
import styles from "../ListTab/ListTab.module.scss";
import { ReactComponent as PopoutIcon } from "../../../assets/images/icon-popout.svg";
import { ReactComponent as ShareIcon } from "../../../assets/images/share-outlined.svg";
import { ReactComponent as EditIcon } from "../../../assets/images/pencil-outlined.svg";
import { ReactComponent as OrderManagement } from "../../../assets/images/search-result/order-management.svg";
import { ReactComponent as PointLeftIcon } from "../../../assets/images/PointLeft.svg";
import { forwardRef, useEffect, useMemo, useRef, useState } from "react";
import clsx from "clsx";
import useDialogStore from "../../DialogPopup/DialogStore";
import useDeleteSearchProducts from "src/renderer/hooks/useDeleteSearchProducts";
import { localStorageKeys, routes, shareEmailTypes } from "src/renderer/common";
import ShareEmailWindow from "../../ShareEmailWindow/ShareEmailWindow";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import useGetDraftLines from "src/renderer/hooks/useGetDraftLines";
import usePostCancelDraftPo from "src/renderer/hooks/usePostCancelDraftPo";
import { useRightWindowStore } from "src/renderer/pages/RightWindow/RightWindowStore";
import { Popover } from '@mui/material';
import { descriptionLines, getOtherDescriptionLines } from "src/renderer/utility/pdfUtils";
import PdfMakePage from "src/renderer/pages/PdfMake/pdfMake";
import useGetOrderLines from "src/renderer/hooks/useGetOrderLines";
dayjs.extend(customParseFormat);

interface OrderManagementTemplateProps {
    item: any;
    index: number;
    isSearchMode?: boolean;
    animatedItems: Set<string>;
    selectedSavedSearchIdList: any[];
    setSelectedSavedSearchIdList: (ids: any[]) => void;
    lastClickedIndex: number | null;
    setLastClickedIndex: (index: number | null) => void;
    handleCtrlClick: (item: any, index: number, onCtrlClickCallback: (currentSelectedIds: any[], updatedIds: any[]) => void) => void;
}

const OrderManagementTemplate = forwardRef<HTMLDivElement, OrderManagementTemplateProps>(({item, index, animatedItems, selectedSavedSearchIdList, setSelectedSavedSearchIdList, lastClickedIndex, setLastClickedIndex, handleCtrlClick, isSearchMode = false, ...props  }: OrderManagementTemplateProps, ref) => {
    const selectedQuote = useCreatePoStore(state => state.selectedQuote);
    const setSelectedQuote = useCreatePoStore(state => state.setSelectedQuote);
    const setCreatePoData = useCreatePoStore(state => state.setCreatePoData);
    const setShowLoader = useGlobalStore(state => state.setShowLoader);
    const userData = useGlobalStore(state => state.userData);
    const productMapping = useGlobalStore(state => state.productMapping);
    const setIsCreatePoDirty = useCreatePoStore(state => state.setIsCreatePoDirty);
    const draftOrderListFromSocket = useCreatePoStore((state: any) => state.draftOrderListFromSocket);
    const setDraftOrderListFromSocket = useCreatePoStore((state: any) => state.setDraftOrderListFromSocket);
    const updatedDraftId = useCreatePoStore((state: any) => state.updatedDraftId);
    const setUpdatedDraftId = useCreatePoStore((state: any) => state.setUpdatedDraftId);
    const {mutateAsync: getOrderLines} = useGetOrderLines();
    const {mutateAsync: cancelDraftPo} = usePostCancelDraftPo();
    const [shareAnchorEl, setShareAnchorEl] = useState<HTMLButtonElement | null>(null);
    const { setShareEmailWindowProps, setLoadComponent, setShareEmailType } = useRightWindowStore();
    const { showCommonDialog, resetDialogStore } = useDialogStore();
    const quoteList = useCreatePoStore(state => state.quoteList);
    const buyerSettings = useBuyerSettingStore(state => state.buyerSetting);
    const setIsEditingPo = useOrderManagementStore(state => state.setIsEditingPo);
    const setSelectedCancelLines = useOrderManagementStore(state => state.setSelectedCancelLines);
    const setOrderManageMentInitialData = useOrderManagementStore(state => state.setOrderManageMentInitialData);
    const isEditingPo = useOrderManagementStore(state => state.isEditingPo);
    const channelWindow = getChannelWindow();
    const setQuoteList = useCreatePoStore(state => state.setQuoteList);
    const setPurchasingList = useCreatePoStore(state => state.setPurchasingList);
    const purchasingList = useCreatePoStore(state => state.purchasingList);
    const isSeller = userData?.data?.type === userRole.sellerUser ;
    const setMessages = useChatWithVendorStore((state: any) => state.setMessages);
    const setPoNumber = useChatWithVendorStore((state: any) => state.setPoNumber);

    const totalPurchase = useMemo(()=>{
        let _totalPurchase: any = "0.00";

        if(item?.total_purchase){
            _totalPurchase = item?.total_purchase;
        }else{
            // Convert string values to numbers and add them if present
            const buyerPoPrice = parseFloat(item?.buyer_po_price) || parseFloat(item?.material_total) || 0;
            const salesTax = parseFloat(item?.sales_tax) || 0;
            const processingFees = parseFloat(item?.processing_fees) || 0;
            const depositAmount = parseFloat(item?.deposit_amount) || parseFloat(item?.deposit) || 0;
            const subscriptionAmount = parseFloat(item?.subscription) || 0;
            
            const calculatedTotal = buyerPoPrice + salesTax + processingFees + depositAmount + subscriptionAmount;
            // Format to 2 decimal places and convert back to string
            _totalPurchase = calculatedTotal.toFixed(2);
        }
        
        return _totalPurchase;
    },[item]);

    const isShippingDetailsFilled = useMemo(()=>{
        return !!item.shipping_details?.city && !!item.shipping_details?.state_id && !!item.shipping_details?.zip && !!item?.delivery_date && !!item?.buyer_internal_po && !!item.shipping_details?.line1;
    },[item]);


    const disableExportToPdf = useMemo(() => {
        return !selectedQuote?.cart_items?.length || !buyerSettings?.delivery_address || buyerSettings?.delivery_address?.length === 0 || !isShippingDetailsFilled;
    }, [selectedQuote?.cart_items, buyerSettings, isShippingDetailsFilled]);

    const isSharePopoverOpen = Boolean(shareAnchorEl && selectedQuote?.id === item.id);

    useEffect(()=>{
        if(item)
            if(item?.id?.includes(noIdGeneric) && !animatedItems.has(item?.id)){
                setTimeout(()=>{
                    animatedItems.add(item.id);
                },3000);
            }
    },[item]);

    
    // useEffect(()=>{
    //     const getLocalQuote = getLocal(localStorageKeys.poQuoting, null);
    //     if(draftOrderListFromSocket?.length > 0 && selectedQuote?.id){
    //         const selectedQuoteFetchLatestData = draftOrderListFromSocket.find((item: any) => item.id === selectedQuote?.id);
    //         if(!selectedQuoteFetchLatestData) setDraftOrderListFromSocket([]);
    //         if(selectedQuoteFetchLatestData?.id && selectedQuoteFetchLatestData?.id === selectedQuote?.id){
    //             setDraftOrderListFromSocket([]);
    //             handleUpdateSelectedData(selectedQuoteFetchLatestData);
    //         }
    //     }
    // },[draftOrderListFromSocket])

    const handleUpdateSelectedData = async(selectedQuoteFetchLatestData: any) => {
        if(!selectedQuote.pricing_expired){
            showCommonDialog(null, 'This quote has been updated.', null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
        }
        clearLocal(localStorageKeys.poQuoting);
        setUpdatedDraftId(null);
        await handleLoadQuoteData(selectedQuoteFetchLatestData);

    }

    const handleItemClick = async (item: any, index: number, event: React.MouseEvent | undefined) => {
        try{
          setSelectedSavedSearchIdList([])
            // Handle shift-click for multiple selection
            // if (event?.shiftKey && lastClickedIndex !== null) {
            //     handleCtrlClick(item, index, handleWhenCtrlClickOnItem);
            //     return;
            // }
            if(selectedQuote?.id === item.id){
                return;
            }
            // setLastClickedIndex(index);
            // setSelectedSavedSearchIdList([item.id]); // Reset to single selection
            if (isEditingPo && !selectedQuote?.seller_company_name && !isSeller) {
                handleOrderManagementNavigation(null, location.pathname, handleLoadQuoteData, [item]);
            }else{
                await handleLoadQuoteData(item);
            }
            isSearchMode&& setShowLoader(false);
        } catch(error){
            setShowLoader(false);
            console.log("error @>>>>>>>", error);
        }
    }

    const handleWhenCtrlClickOnItem = (currentSelectedIds: any[], updatedIds: any[]) => {
        const itemId = item.id;
        if (currentSelectedIds.includes(itemId)) {
            if (selectedQuote?.id === itemId) {
                // clearSelectedSavedSearch();
                // const itemIndex = savedSearchProducts.findIndex((item: any) => item.id === updatedIds[0]);
                if (item.id === updatedIds[0]) {
                    handleLoadQuoteData(item);
                }
            }
        }
        if (updatedIds.length === 1 && selectedQuote?.id !== updatedIds[0]) {
            handleItemClick(item, index, undefined);
            return;
        }
    }

    const handleLoadQuoteData = async (item: any) => {
        try{
            setIsEditingPo(false);
            setSelectedCancelLines([]);
            setSelectedQuote(null);
            setMessages([]);
            setPoNumber('')
            setShowLoader(true);
            setIsCreatePoDirty(false);
            const orderData = await getOrderLines(item.id);
            if(orderData?.data?.error_message){
                showCommonDialog(null, orderData?.data?.error_message, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
                setShowLoader(false);
                return;
            }
            // const draftLines = item?.items || [];
            updateOrderManagementData(item, orderData?.data);
            // setShowLoader(false);
        } catch(error){
            console.log("error @>>>>>>>", error);
            showCommonDialog(null, error?.message, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
        }
    }

    const handleEditClick = (item: any, e: React.MouseEvent) => {
        e.stopPropagation();
        if(selectedQuote?.id === item.id){
            const updateSelectedQuote = {...selectedQuote, isEdit: true};
            setSelectedQuote(updateSelectedQuote);
            setCreatePoData(updateSelectedQuote);
        }
    }

    const handleDeleteClick = async (e: React.MouseEvent, item: any) => {
        e.stopPropagation();
        console.log("delete icon clicked @>>>>>>>", e, item);
        if(item?.id.includes(noIdGeneric)){
            setSelectedQuote(null);
            setSelectedSavedSearchIdList([]);
            setLastClickedIndex(null);
            if(item.order_type === orderType.QUOTE){
                setQuoteList(quoteList.filter((quote: any) => quote.id !== item.id));
                clearLocal(localStorageKeys.poQuoting);
            }else{
                setPurchasingList(purchasingList.filter((po: any) => po.id !== item.id));
                clearLocal(localStorageKeys.poPurchasing);
            }
            return;
        }
        try{
            const payload = {
                data: [item.id]
            }
            setSelectedQuote(null);
            const response = await cancelDraftPo(payload);
            if(response?.data?.data){
                setSelectedQuote(null);
                setSelectedSavedSearchIdList([]);
                setLastClickedIndex(null);
            }
        } catch(error){
            console.log("error @>>>>>>>", error);
            await handleLoadQuoteData(item);
        }
    }

    const getExportPoData = () => {
        let _recevingHours = [];
        if(buyerSettings?.delivery_address?.length === 1){
            _recevingHours = buyerSettings?.delivery_address[0]?.user_delivery_receiving_availability_details;
        }else{
            if(selectedQuote?.shipping_details?.delivery_address_id || item?.shipping_details?.delivery_address_id){
                const deliveryAddress = buyerSettings?.delivery_address?.find((address: any) => address.id === (selectedQuote?.shipping_details?.delivery_address_id || item?.shipping_details?.delivery_address_id));
                _recevingHours = deliveryAddress?.user_delivery_receiving_availability_details;
            }else{
                _recevingHours = buyerSettings?.delivery_address?.find((address: any) => address.is_default)?.user_delivery_receiving_availability_details;
            }
        }
    

        const formmattedItem = {...item, totalPurchase : totalPurchase}
        return {...formmattedItem , recevingHours : _recevingHours}
    }

    const getCartItems = () =>{
        const {cart_items} = selectedQuote;
        const formattedItems = cart_items.filter(item => ((item?.descriptionObj?.UI_Description && !!item?.qty) || item?.lineStatus)).map((item, index) => ({
            description: descriptionLines(item?.descriptionObj?.UI_Description ?? ''), 
            otherDescription: getOtherDescriptionLines(item?.descriptionObj?.UI_Description ?? ''), 
            product_tag: item?.product_tag ?? '',
            domesticMaterialOnly: item?.domestic_material_only ? '\nDomestic (USA) Material Only' : '',
            qty: formatToTwoDecimalPlaces(item?.qty ?? ''),
            qty_unit: item?.qty_unit?.toLowerCase() ?? '',
            price_unit: item?.price_unit?.toLowerCase() ?? '',
            extended: formatToTwoDecimalPlaces(item?.extendedValue ?? ''),
            price: item?.price_unit?.toLowerCase() === priceUnits.lb ? format4DigitAmount(item?.buyer_price_per_unit ?? '') : formatToTwoDecimalPlaces(item?.buyer_price_per_unit ?? ''),
            line_weight: location.pathname === routes.savedBom ? formatToTwoDecimalPlaces(item?.line_weight ?? '') : calculateLineWeight(item),
            line_weight_unit: "Lb", 
            line_no: index,
            po_line: index.toString(),
            descriptionObj:item?.descriptionObj ?? {},
            extendedValue:item?.extendedValue ?? '',
            lineStatus: item?.line_status ?? ''
        }));
        return formattedItems
    }

    const handleExportToExcel = (e: React.MouseEvent) => {
        e.stopPropagation();
        setShareAnchorEl(null);
        
        exportToExcel(transformOrderToSheetData(selectedQuote), selectedQuote.buyer_internal_po);
    }

    const handleSharePriceClick = async (e: React.MouseEvent<HTMLButtonElement>, item: any) => {
        e.stopPropagation();
        setShareAnchorEl(e.currentTarget);
        if(selectedQuote?.id === item.id) return;
        await handleLoadQuoteData(item);
    }

    const handleCloseSharePopover = (e) => {
        e.stopPropagation();
        setShareAnchorEl(null);
    }

    const handleSharePrice = async (e: React.MouseEvent, item: any) => {
        e.stopPropagation();
        console.log("handleSharePrice @>>>>>>>", item)
        setShareEmailWindowProps({ isSharePrice: true });
        // setLoadComponent(<ShareEmailWindow />)
        setShareEmailType(shareEmailTypes.shareQuote);
        setShareAnchorEl(null);
    }
    
    let animate = false;
    if(item?.id?.includes(noIdGeneric) && !animatedItems.has(item?.id)) {
        animate = true;
    }

    return (
        <div    
            data-hover-video-id="om-po-view"
            ref={ref}
            key={index}
            className={clsx(styles.searchItemContainer, (selectedQuote?.id === item.id ) && styles.selectedSearchItem)}
            onClick={(e) => { handleItemClick(item, index, e); }}>
            <div className={styles.searchTitle}>
                <span className={clsx(styles.searchTitleText, {
                    [styles.initialPositionForAnimation]: animate,
                    [styles.slideInAnimation1]: animate
                })}>
                    {item.buyer_internal_po}
                </span>
                <span className={styles.itemCount}>{isSearchMode&&<OrderManagement />}${formatToTwoDecimalPlaces(totalPurchase)}</span>
                <div className={styles.iconContainer}>
                    {/* <span className={styles.shareIcon} onClick={(e) => console.log("share icon clicked ", e, item)}></span> */}
                    {/* <div className={styles.exportContainer}>
                        <button
                            className={clsx(styles.selectedProductHeaderButton)}
                            onClick={(e) => handleSharePriceClick(e, item)}
                            
                        >
                            <ShareIcon />
                        </button>
                        <Popover
                            open={isSharePopoverOpen}
                            anchorEl={shareAnchorEl}
                            onClose={handleCloseSharePopover}
                            anchorOrigin={{
                                vertical: 'top',
                                horizontal: 'center',
                            }}
                            transformOrigin={{
                                vertical: 'bottom',
                                horizontal: 86,
                            }}
                            classes={{
                                paper: styles.exportDropdownMenu,
                            }}
                        >
                            <div>
                                <button
                                    className={styles.exportOption}
                                    onClick={(e) => handleSharePrice(e, item)}
                                >
                                    Share
                                </button>
                                {(channelWindow?.fetchPdf || channelWindow?.generatePdf) &&  
                                        <PdfMakePage getExportPoData={getExportPoData} buyingPreferenceData={buyerSettings} disabled={disableExportToPdf} getCartItems={getCartItems} />
                                    }
                                <button
                                    className={styles.exportOption}
                                    onClick={handleExportToExcel}
                                >
                                    Export to Excel
                                </button>
                            </div>
                        </Popover>
                    </div> */}
                    { item?.cancel_date && (<span className={styles.canceledOrderDescription}>Canceled</span>)}
                    { item?.is_closed_order ? (<span className={styles.canceledOrderDescription}>Closed</span>) : (<span></span>)}
                    <span></span>
                    <span className={styles.deleteIcon} ><PopoutIcon /></span>
                </div>
            </div>
            <div className={styles.orderNumber}>
                <span className={clsx(styles.orderNumberText, {
                    [styles.initialPositionForAnimation]: animate,
                    [styles.slideInAnimation1]: animate
                })}>
                    {isSeller ? item.seller_po_number : item.buyer_po_number}
                </span>
            </div>         
            <div className={styles.templateDetails}>
            <div className={styles.searchDetails}>
                <div className={styles.positionRelative}>
                    <span className={clsx({
                        [styles.initialPositionForAnimation]: animate,
                        [styles.slideInAnimation2]: animate
                    })}>{item.shipping_details?.city || item?.city}, {item.shipping_details?.state_code || item?.state_code}
                    </span>
                </div>
                <div className={styles.positionRelative}>
                    <span className={clsx({
                        [styles.initialPositionForAnimation]: animate,
                        [styles.slideInAnimation3]: animate
                    })}>{item.total_weight ? formatOrderWeightWithComma(item.total_weight) : '0'} LBS</span>
                </div>
                {/* {item?.products?.length > 0 ?
                Array.from(new Set(
                    item?.products
                        .map((obj: any) => productMapping[obj.product_id]?.Key2 ?? '')
                )).join(', ')
            : '-'} */}
                {/* {orderSizeData ? <span>Based Upon {Number(orderSizeData?.min_weight) === Number(orderSizeList[orderSizeList.length - 1].min_weight) ? Number(orderSizeData?.min_weight).toLocaleString() + '+' : `${Number(orderSizeData?.min_weight).toLocaleString()} to ${Number(orderSizeData?.max_weight).toLocaleString()}`} LBS</span> : '-'}<br /> */}
                <div className={styles.positionRelative}>
                    <span className={clsx({
                        [styles.initialPositionForAnimation]: animate,
                        [styles.slideInAnimation4]: animate
                    })}>Deliver By: {item?.delivery_date ? dayjs(item.delivery_date).format(dateTimeFormat.dateSeparateWithSlashAndDayTwoDigit) : '-'}</span>
                </div>
                {isSeller ? (
                    <div className={styles.positionRelative}>
                        <span className={clsx({
                            [styles.initialPositionForAnimation]: animate,
                            [styles.slideInAnimation4]: animate
                        })}>Buyer: {item?.buyer_company_name ? item?.buyer_company_name : '-'}</span>
                    </div>
                ) : (
                    <div className={styles.positionRelative}>
                        <span className={clsx({
                            [styles.initialPositionForAnimation]: animate,
                            [styles.slideInAnimation4]: animate
                        })}>Fulfilled By: {item?.seller_company_name ? item?.seller_company_name : '-'}</span>
                    </div>
                )}
            </div>
            {
               ( Boolean(item?.in_dispute)) && (
                <div className={styles.disputeCount}>
                         { item?.dispute_count > 0 && <span className={styles.disputeCountText}>{item?.dispute_count}</span>}
                        <span> <PointLeftIcon /></span>
                </div>
               ) 
            }
            </div>

        </div>
    )
});

export default OrderManagementTemplate