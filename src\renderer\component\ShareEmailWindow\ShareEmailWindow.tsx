import React, { useState, useRef, useEffect } from 'react'
import styles from './ShareEmailWindow.module.scss';
import InputWrapper from '../InputWrapper';
import CustomTextField from '../CustomTextField';
import clsx from 'clsx';
import dayjs from 'dayjs';
import { axios, dataOpticsApi1, dateTimeFormat, getFormattedUnit, HttpRequestPayload, SearchAnalyticDataModel, useBuyerSettingStore, useCreatePoStore, useGlobalStore, useSearchStore } from '@bryzos/giss-ui-library';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { ReactComponent as EmojiIcon } from 'src/renderer/assets/images/Share-Pricing/Emoji.svg';
import { ReactComponent as EmojiIconHover } from 'src/renderer/assets/images/Share-Pricing/Emoji-Hover.svg';
import { ReactComponent as BoldIcon } from 'src/renderer/assets/images/Share-Pricing/Bold.svg';
import { ReactComponent as BoldIconHover } from 'src/renderer/assets/images/Share-Pricing/Bold-Hover.svg';
import { ReactComponent as ItalicIcon } from 'src/renderer/assets/images/Share-Pricing/Italic.svg';
import { ReactComponent as ItalicIconHover } from 'src/renderer/assets/images/Share-Pricing/Italic-Hover.svg';
import { ReactComponent as UnderlineIcon } from 'src/renderer/assets/images/Share-Pricing/Underline.svg';
import { ReactComponent as UnderlineIconHover } from 'src/renderer/assets/images/Share-Pricing/Underline-Hover.svg';
import { ReactComponent as ExpandIcon } from 'src/renderer/assets/images/Share-Pricing/Expand.svg';
import { ReactComponent as CloseIcon } from 'src/renderer/assets/images/close-share.svg';
import { ReactComponent as SuccessCloseIcon } from 'src/renderer/assets/images/Share-Close-Icon.svg';
import { useRightWindowStore } from 'src/renderer/pages/RightWindow/RightWindowStore';
import PricePerUnit from 'src/renderer/pages/search/mainContent/PricePerUnit';
import { ProductPricingModel } from 'src/renderer/types/Search';
import SharedAppHistoryWindow from '../SharedAppHistoryWindow/SharedAppHistoryWindow';
import { shareEmailTypes } from 'src/renderer/common';
import { useHeaderStore } from 'src/renderer/pages/Header/HeaderStore';

// Form data type
type FormValues = {
  emails: string;
  message?: string;
};

// Common emojis for the picker
const commonEmojis = [
  "😀", "😃", "😄", "😁", "😆", "😅", "😂", "🤣", "😊", "😇",
  "🙂", "🙃", "😉", "😌", "😍", "🥰", "😘", "😗", "😙", "😚",
  "😋", "😛", "😝", "😜", "🤪", "🤨", "🧐", "🤓", "😎", "🤩",
  "👍", "👎", "👌", "✌️", "🤞", "🤝", "🙏", "👏", "🎉", "❤️"
];

// Email validation schema using Yup
const schema = yup.object().shape({
  emails: yup.string()
    .required('Email is required')
    .test('emails', 'Please enter valid email addresses', (value) => {
      if (!value) return false;
      const emails = value.split(';').map(email => email.trim());
      return emails.every(email => 
        /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(email)
      );
    }),
  message: yup.string().optional()
});

// Success View Component
const SuccessView = ({ onShareAgain, isSharePrice }: { onShareAgain: () => void, isSharePrice: boolean }) => {
  return (
    <div className={styles.contentContainer1}>
      <div className={styles.successHeader}>
        <h2>SUCCESS</h2>
        <h1>{ isSharePrice ? 'PRICING SENT' : 'INVITATION SENT'}</h1>
      </div>
      <div className={styles.successContent}>
        <p className={styles.successMessage}>
          { isSharePrice ? 
          <>
            We have sent an email to your friend with your personalized message (or our canned message if you did not write anything), the product pricing you selected, and a link to download the <i>Gone in 60 Seconds™ </i>&nbsp;application.
          </>
          : 
          <>
             We have sent an email to your friend with your personalized message (or our canned message if you did not write anything) and a link to download the <i>Gone in 60 Seconds™ </i>&nbsp;application.
          </>
          }
        </p>
        <button className={styles.shareAgainButton} onClick={onShareAgain}>
        SHARE AGAIN
        </button>
      </div>
    </div>
  );
};

const ShareEmailWindow = () => {
    const { userData }: any = useGlobalStore();
    const { selectedProductsData, setSelectedProductsData, selectedDomesticOption, shortListedSearchProductsData, selectedPriceUnit, sessionId, searchZipCode, orderSizeSliderValue } = useSearchStore();
    const { shareEmailWindowProps, setLoadComponent, setShareEmailWindowProps, setShareEmailType, setIsSharedAppHistory, isSharedAppHistory, shareEmailType } = useRightWindowStore();
    const filterShortListedSearchProductsData = useSearchStore(state => state.filterShortListedSearchProductsData);
    const selectedQuote = useCreatePoStore(state => state.selectedQuote);
    
    const { isSharePrice } = shareEmailWindowProps || {isSharePrice: false};
    const [emailTags, setEmailTags] = useState<string[]>([]);
    const [emailInput, setEmailInput] = useState('');
    const [isSendInProgress, setIsSendInProgress] = useState(false);
    const [emailError, setEmailError] = useState('');
    const messageRef = useRef<HTMLDivElement>(null);
    const [showEmojiPicker, setShowEmojiPicker] = useState(false);
    const [activeButtons, setActiveButtons] = useState({
        bold: false,
        italic: false,
        underline: false
    });
    const [lastClickedButton, setLastClickedButton] = useState<string | null>(null);
    const [isDuplicateError, setIsDuplicateError] = useState(false);
    const [focusedTagIndex, setFocusedTagIndex] = useState<number | null>(null);
    const emailInputRef = useRef<HTMLInputElement>(null);
    const [isSameEmailError, setIsSameEmailError] = useState(false);
    // Change to isSuccess to track when to show success view
    const [isSuccess, setIsSuccess] = useState(false);
    const {showInviteTeam, setShowInviteTeam} = useHeaderStore();

    const placeholderText = isSharePrice 
        ? 'Write a message to your friend here to be included in the email with the product pricing. We don\'t want your friend to think this is a Spam email...'
        : 'Write a message to your friend here to be included in the email. We will include the Download Link for easy access to the app. We don\'t want your friend to think this is a Spam email...';
    
    // Initialize react-hook-form
    const { register, handleSubmit, setValue, formState: { errors }, reset } = useForm<FormValues>({
        resolver: yupResolver(schema),
        defaultValues: {
            emails: emailTags.join(';'),
            message: ''
        }
    });

    // Effect to automatically hide the last clicked button notification
    useEffect(() => {
        if (lastClickedButton) {
            const timer = setTimeout(() => {
                setLastClickedButton(null);
            }, 2000);
            
            return () => clearTimeout(timer);
        }
    }, [lastClickedButton]);

    // Close emoji picker when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            const target = event.target as HTMLElement;
            if (showEmojiPicker && !target.closest(`.${styles.emojiPicker}`) && !target.closest(`.${styles.emojiButton}`)) {
                setShowEmojiPicker(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [showEmojiPicker]);

    // Update the hidden message field value when content changes
    useEffect(() => {
        const updateMessageValue = () => {
            if (messageRef.current) {
                // Only set the value if it's not the placeholder
                if (!messageRef.current.innerHTML.includes('placeholder')) {
                    setValue('message', messageRef.current.innerHTML);
                } else {
                    setValue('message', ''); // Set empty value if it's just the placeholder
                }
            }
        };

        // Set initial placeholder text
        if (messageRef.current && messageRef.current.innerHTML === '') {
            messageRef.current.innerHTML = `<span class="placeholder">${placeholderText}</span>`;
        }

        // Set up mutation observer to detect content changes
        if (messageRef.current) {
            const observer = new MutationObserver(updateMessageValue);
            observer.observe(messageRef.current, { 
                childList: true, 
                subtree: true, 
                characterData: true 
            });

            return () => observer.disconnect();
        }
    }, [setValue, isSuccess]);

    // Update active state when selection changes
    useEffect(() => {
        const checkActiveStyles = () => {
            if (document.queryCommandState) {
                setActiveButtons({
                    bold: document.queryCommandState('bold'),
                    italic: document.queryCommandState('italic'),
                    underline: document.queryCommandState('underline')
                });
            }
        };

        // Add event listeners for selection change
        document.addEventListener('selectionchange', checkActiveStyles);
        if (messageRef.current) {
            messageRef.current.addEventListener('click', checkActiveStyles);
            messageRef.current.addEventListener('keyup', checkActiveStyles);
        }

        return () => {
            // setShareEmailType(null);
            document.removeEventListener('selectionchange', checkActiveStyles);
            if (messageRef.current) {
                messageRef.current.removeEventListener('click', checkActiveStyles);
                messageRef.current.removeEventListener('keyup', checkActiveStyles);
            }
        };
    }, []);

    useEffect(()=>{
        handleShareAgain();
    },[isSharePrice])

    // Handle form submission
    const onSubmit = async (data: FormValues) => {
        try {
            // Remove placeholder if it's still there
            if (data.message && data.message.includes('placeholder')) {
                data.message = '';
            }

            console.log('Form submitted with data:', data);
            // Add your send email logic here
            setIsSendInProgress(true);
            if (shareEmailType === shareEmailTypes.sharePrice && selectedProductsData.length > 0) {
                await onShareProductPricing(data.emails, data.message || '');
            } else if (shareEmailType === shareEmailTypes.shareQuote && selectedQuote?.id) {
                await onShareDraftOrder(data.emails, data.message || '');
            } else {
                await onShareApp(data.emails, data.message || '');
            }
        } catch (error) {
            setEmailError('Something went wrong. Please try again.');
            console.error('Error submitting form:', error);
        } finally {
            setIsSendInProgress(false);
        }
    };

    
    const onShareProductPricing = async (emailTo: string, emailContent: string): Promise<void> => {

        const _selectedProduct: ProductPricingModel[] = selectedProductsData.length === 0 ? shortListedSearchProductsData : selectedProductsData;
        const productList: any[] = [];
        const dataOpticsData: SearchAnalyticDataModel[] = [];
        const { buyerSetting } = useBuyerSettingStore.getState();
        const defaultZipCode = buyerSetting?.price_search_zip || '63105';
        const _zipcode = searchZipCode?.length === 5 ? searchZipCode :  defaultZipCode;
        _selectedProduct.forEach((product: ProductPricingModel) => {
            productList.push({
                "product_id": product.id,
                "product_description": product.UI_Description,
                "price_ft": product.ft_price,
                "price_lb": product.lb_price,
                "price_cwt": product.cwt_price,
                "price_pc": product.pc_price,
                "price_share_type": selectedPriceUnit.toUpperCase(),
            });
            dataOpticsData.push({
                "session_id": sessionId,
                "line_session_id": product.line_session_id,
                "product_id": product.id,
                "description": product.UI_Description,
                "price_shared": true,
                "search_price_unit": selectedPriceUnit.toUpperCase(),
                "zip_code": _zipcode.trim(),
                "order_size": String(orderSizeSliderValue),
                "price" : {
                    "price_ft": product?.ft_price,
                    "price_lb": product?.lb_price,
                    "price_cwt": product?.cwt_price,
                    "price_pc": product?.pc_price,
                },
            })
        });

        const dataOpticsPayload: HttpRequestPayload<SearchAnalyticDataModel[]> = {
            "data": dataOpticsData
        }
        dataOpticsApi1(dataOpticsPayload)
        const payload = {
            data: {
                "user_id": userData.data.id,
                "from_email": userData.data.email_id,
                "to_email": emailTo,
                "email_content": emailContent?.length === 0 ? null : emailContent,
                "products": productList,
                "zipcode": _zipcode.trim(),
                "order_size": String(orderSizeSliderValue)
            }
        }
        try {
            const res = await axios.post(import.meta.env.VITE_API_SERVICE + '/user/share-product-price', payload)
            if (
                typeof res.data.data === "object" &&
                "error_message" in res.data.data
            ) {
                throw new Error(res.data.data.error_message);
            } else {
                setIsSuccess(true);
                return res.data.data;
            }
        } catch (err) {
            throw new Error("Share Product Pricing Api Failure");
        }
    }

    const onShareApp = async (emailTo: string, emailContent: string): Promise<void> => {
        const payload = {
            data: {
                "user_id": userData.data.id,
                "from_email": userData.data.email_id,
                "to_email": emailTo,
                "email_content": emailContent?.length === 0 ? null : emailContent
            }
        }
        try {
            const res = await axios.post(import.meta.env.VITE_API_SERVICE + '/user/shareWidgetRequest', payload)
            if (
                typeof res.data.data === "object" &&
                "error_message" in res.data.data
            ) {
                throw new Error(res.data.data.error_message);
            } else {
                setIsSuccess(true);
                return res.data.data;
            }
        } catch (err) {
            throw new Error("Share App Api Failure");
        }
    }

    const onShareDraftOrder = async (emailTo: string, emailContent: string): Promise<void> => {
        const payload = {
            "data": {
                "from_email": userData.data.email_id,
                "to_email": emailTo,
                "email_content": emailContent?.length === 0 ? null : emailContent,
                "draft_id": selectedQuote?.id
            }
        }
        try {
            const res = await axios.post(import.meta.env.VITE_API_ORDER_SERVICE + '/buyer/share-draft', payload);
            if (
                typeof res.data.data === "object" &&
                "error_message" in res.data.data
            ) {
                throw new Error(res.data.data.error_message);
            } else {
                setIsSuccess(true);
                return res.data.data;
            }
        } catch (err) {
            throw new Error("Share Draft Order Api Failure");
        }
    }

    // Reset form and return to sharing view
    const handleShareAgain = () => {
        setIsSuccess(false);
        setEmailTags([]);
        setEmailInput('');
        setEmailError('');
        setIsDuplicateError(false);
        setIsSameEmailError(false);
        reset();
    };

    // Add email tag
    const addEmailTag = (email: string) => {
        const emailRegex = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        const emailTrimmed = email.trim();
        
        // Check if email is the same as the current user's email
        if (emailTrimmed && userData?.data?.email_id && 
            emailTrimmed.toLowerCase() === userData.data.email_id.toLowerCase()) {
            setEmailError('To and from email cannot be same.');
            setIsSameEmailError(true);
            setEmailInput('');
            return;
        }
        
        // Check if email already exists in the list
        if (emailTrimmed && emailTags.some(tag => tag.toLowerCase() === emailTrimmed.toLowerCase())) {
            setEmailError('This email address is already added');
            setIsDuplicateError(true);
            setIsSameEmailError(false);
            setEmailInput('');
            return;
        }
        
        if (emailTrimmed && emailRegex.test(emailTrimmed)) {
            const newTags = [...emailTags, emailTrimmed];
            console.log('newTags  ',newTags);
            console.log('emailTags  ',emailTags, email);
            setEmailTags(newTags);
            setValue('emails', newTags.join(';'));
            setEmailInput('');
            setEmailError('');
            setIsDuplicateError(false);
            setIsSameEmailError(false);
        } else if (emailTrimmed) {
            setEmailError('Please enter a valid email address');
            setIsDuplicateError(false);
            setIsSameEmailError(false);
        }
    };

    // Remove email tag
    const removeEmailTag = (indexToRemove: number) => {
        const newTags = emailTags.filter((_, index) => index !== indexToRemove);
        setEmailTags(newTags);
        setValue('emails', newTags.join(';'));
    };

    const handleEmailTagOnClick = (index: number) => {
        setFocusedTagIndex(index);
        const removedTag = emailTags[index];
        const newTags = emailTags.filter((_, i) => i !== index);
        setEmailTags(newTags);
        setValue('emails', newTags.join(';'));
        
        // Put the removed tag into the input field and select it
        setEmailInput(removedTag);
        
        // Focus and select the text after a small delay
        setTimeout(() => {
            if (emailInputRef.current) {
                emailInputRef.current.focus();
                emailInputRef.current.select();
            }
        }, 10);
    };

    // Handle key press in email input
    const handleEmailKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter' || e.key === ',' || e.key === ';') {
            e.preventDefault();
            addEmailTag(emailInput);
        } else if (e.key === 'Backspace' && emailInput === '' && emailTags.length > 0) {
            // Remove the last tag when backspace is pressed and input is empty
            // and put it back into the input field
            e.preventDefault();
            const newTags = [...emailTags];
            const removedTag = newTags.pop();
            setEmailTags(newTags);
            setValue('emails', newTags.join(';'));
            setEmailInput(removedTag || '');
            setEmailError('');
            
            // Select the text in the input field after a small delay to ensure it's rendered
            setTimeout(() => {
                if (emailInputRef.current) {
                    emailInputRef.current.select();
                }
            }, 10);
        } else if (e.key === 'ArrowLeft' && emailInput === '' && emailTags.length > 0) {
            // Focus on the last tag when left arrow is pressed and input is empty
            setFocusedTagIndex(emailTags.length - 1);
        }
    };

    // Handle focused tag keyboard navigation
    const handleTagKeyDown = (e: React.KeyboardEvent<HTMLDivElement>, index: number) => {
        if (e.key === 'Backspace' || e.key === 'Delete') {
            e.preventDefault();
            const newTags = [...emailTags];
            const removedTag = newTags.splice(index, 1)[0];
            setEmailTags(newTags);
            setValue('emails', newTags.join(';'));
            setEmailInput(removedTag || '');
            setFocusedTagIndex(null);
            
            // Focus back on input and select the text
            if (emailInputRef.current) {
                emailInputRef.current.focus();
                // Select the text after a small delay to ensure it's rendered
                setTimeout(() => {
                    if (emailInputRef.current) {
                        emailInputRef.current.select();
                    }
                }, 10);
            }
        } else if (e.key === 'ArrowRight' && index === emailTags.length - 1) {
            // Move focus back to input when right arrow is pressed on last tag
            setFocusedTagIndex(null);
            if (emailInputRef.current) {
                emailInputRef.current.focus();
            }
        } else if (e.key === 'ArrowLeft' && index > 0) {
            // Move focus to previous tag
            setFocusedTagIndex(index - 1);
        } else if (e.key === 'ArrowRight' && index < emailTags.length - 1) {
            // Move focus to next tag
            setFocusedTagIndex(index + 1);
        } else if (e.key === 'Escape') {
            // Remove focus from tags
            setFocusedTagIndex(null);
            if (emailInputRef.current) {
                emailInputRef.current.focus();
            }
        }
    };

    // Handle message input focus
    const handleMessageFocus = () => {
        if (messageRef.current && messageRef.current.innerHTML.includes('placeholder')) {
            messageRef.current.innerHTML = '';
            setValue('message', ''); // Ensure form value is empty
        }
    };

    // Handle formatting buttons
    const handleFormat = (command: string) => {
        document.execCommand(command, false);
        if (messageRef.current) {
            messageRef.current.focus();
            setValue('message', messageRef.current.innerHTML);
            
            // Update active states and set last clicked button
            setActiveButtons({
                ...activeButtons,
                [command]: document.queryCommandState(command)
            });
            
            setLastClickedButton(command);
        }
    };

    // Handle emoji button
    const handleEmojiButtonClick = () => {
        setShowEmojiPicker(!showEmojiPicker);
        setLastClickedButton('emoji');
    };

    // Insert emoji at cursor position
    const insertEmoji = (emoji: string) => {
        if (messageRef.current) {
            messageRef.current.focus();
            document.execCommand('insertText', false, emoji);
            setValue('message', messageRef.current.innerHTML);
        }
        // Don't close the picker so user can insert multiple emojis
    };

    const handleCloseWidget = () => {
        if(shareEmailType === shareEmailTypes.inviteUser && shareEmailWindowProps?.isSharePrice === false && showInviteTeam){
            setShowInviteTeam(false);
        }else{
            handleShareAgain();
            setShareEmailWindowProps(null);
            setShareEmailType(null);
            if(shareEmailType !== shareEmailTypes.shareQuote) setLoadComponent(null);
        }
    };

    const handleSendInvites = () => {
        if (!isSharedAppHistory) {
            setLoadComponent(<SharedAppHistoryWindow />);
            setIsSharedAppHistory(true);
        } else {
            setIsSharedAppHistory(false);
            setLoadComponent(null);
        }
    }

    return (
        <div className={clsx(styles.RightWindowcontainer, shareEmailType === shareEmailTypes.inviteUser && styles.inviteUser)}>
            <div className={styles.crossContainer}>
                {isSuccess ?
                    <SuccessCloseIcon className={styles.cross} onClick={handleCloseWidget} />
                    :
                    <>
                        <CloseIcon className={styles.cross} onClick={handleCloseWidget} />
                        {/* <ExpandIcon /> */}
                    </>
                }
            </div>
            {isSuccess ? (
                <SuccessView onShareAgain={handleShareAgain} isSharePrice={isSharePrice} />
            ) : (
                <div data-hover-video-id= {isSharePrice ? 'invite-user' : ''}>
                    {shareEmailType === shareEmailTypes.inviteUser && <div className={styles.title}>SHARE THIS APPLICATION WITH <br />YOUR FRIEND OR COWORKER</div>}
                    {shareEmailType === shareEmailTypes.sharePrice && <div className={styles.shareTitle}>SHARE<br/>PRICING</div>}
                    {shareEmailType === shareEmailTypes.shareQuote && <div className={styles.title}>SHARE THIS QUOTE WITH YOUR <br/>FRIEND OR COWORKER</div>}
                    <div className={styles.content}>
                        <div className={styles.emailInputContainer}>
                            <div className={clsx(
                                styles.emailTagsContainer, 
                                emailInput === '' && styles.emptyInput
                            )}>
                                {emailTags.map((tag, index) => (
                                    <div 
                                        key={index} 
                                        className={clsx(
                                            styles.emailTag,
                                            focusedTagIndex === index && styles.focused
                                        )}
                                        tabIndex={0}
                                        onKeyDown={(e) => handleTagKeyDown(e, index)}
                                        onClick={() => handleEmailTagOnClick(index)}
                                    >
                                        <span className={styles.emailText}>{tag}</span>
                                        <span 
                                            className={styles.removeTag} 
                                            onClick={(e) => {
                                                e.stopPropagation(); // Prevent tag focus
                                                removeEmailTag(index);
                                            }}
                                        >
                                            X
                                        </span>
                                    </div>
                                ))}
                                <textarea

                                    ref={emailInputRef}
                                    type="text"
                                    value={emailInput}
                                    onChange={(e) => {setEmailInput(e.target.value); setIsSendInProgress(false)}}
                                    onKeyDown={handleEmailKeyPress}
                                    onFocus={() => setFocusedTagIndex(null)}
                                    onBlur={() => {
                                        if (emailInput) addEmailTag(emailInput);
                                    }}
                                    className={styles.emailInput}
                                    placeholder={emailTags.length === 0 ? "Enter send-to email address.\nYou can enter multiple email addresses." : ""}
                                > </textarea>
                            </div>
                            <input 
                                type="hidden" 
                                {...register("emails")} 
                                value={emailTags.join(';')}
                            />
                            {emailError && 
                                <div className={clsx(
                                    styles.errorText, 
                                    isDuplicateError && styles.duplicateError,
                                    isSameEmailError && styles.sameEmailError
                                )}>
                                    {emailError}
                                </div>
                            }
                            {errors.emails && <div className={styles.errorText}>{errors.emails.message}</div>}
                        </div>
                        
                        <div className={styles.messageContainer}>
                            <div
                                ref={messageRef}
                                className={clsx(styles.messageEditor, messageRef.current?.innerHTML === '' && styles.empty)}
                                contentEditable
                                onFocus={handleMessageFocus}
                                onBlur={() => {
                                    if (messageRef.current && messageRef.current.innerHTML === '') {
                                        messageRef.current.innerHTML = `<span class="placeholder">${placeholderText}</span>`;
                                        // Leave empty so CSS placeholder can work
                                    }
                                }}
                                data-placeholder={placeholderText}
                            ></div>
                            
                            {isSharePrice && (
                                    <div className={styles.ProductContainer}>
                                        {(selectedProductsData.length > 0 ? selectedProductsData : filterShortListedSearchProductsData).map((product) => {
                                            const lines: string[] = product.UI_Description.split('\n');
                                            const firstLine: string = lines[0];
                                            const restLines: string[] = lines.slice(1);
                                            return (
                                            <div className={styles.selectedSearchProductTop} key={product.id} >
                                                <div className={clsx(styles.productDescriptionMain)} >
                                                <p className={styles.firstLine}>{firstLine}</p>
                                                <div className={styles.searchProductDescContainer}>

                                                <div className={styles.searchProductDesc}>
                                                    
                                                    {restLines.map((line: string, index:number) => (
                                                        <p key={index}>{line}</p>
                                                    ))}
                                                    {/* {(product.domestic_material_only && selectedDomesticOption) && <span className={styles.usaFlag}> USA ONLY </span>} */}
                                                </div>
                                                <div className={styles.priceRating} >
                                                    <div className={clsx(styles.priceMain,'priceMainShare')}>
                                                        <PricePerUnit
                                                            product={product}
                                                        />
                                                    </div>
                                                </div>
                                                </div>
                                                    {/* <ProductDescription product={product} /> */}
                                                    {/* <ProductPricing
                                                        product={selectedSearchProduct}
                                                        productDescriptionAndPricingRef={productDescriptionAndPricingRef}
                                                        isMouseOverOnProduct={isMouseOverOnProduct}
                                                    /> */}
                                                </div>
                                            </div>
                                        )})}
                                    </div>
                                )}
                                {shareEmailType === shareEmailTypes.shareQuote && (
                                    <div className={styles.quoteContainer}>
                                        <div>

                                        <div className={styles.quoteTitle}>{selectedQuote?.buyer_internal_po}</div>
                                        <div className={styles.quoteDetails}>
                                            <span>{selectedQuote?.shipping_details?.city}, {selectedQuote?.shipping_details?.state_code} {selectedQuote?.shipping_details?.zip}</span><br/>
                                            <span>{selectedQuote?.total_weight} LBS</span><br/>
                                            <span>Deliver by {selectedQuote?.delivery_date ? dayjs(selectedQuote.delivery_date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit) : ''}</span>
                                        </div>
                                        </div>
                                        <div className={styles.quotePriceContainer}>
                                            <span className={styles.quotePrice}>${selectedQuote?.buyer_po_price}</span><br/>
                                            <span>{selectedQuote?.cart_items?.length || 0} Lines</span>
                                        </div>
                                    </div>
                                )}
                            <input 
                                type="hidden" 
                                {...register("message")} 
                            />
                               
                        </div>
                        <div className={styles.formattingToolbarContainer}>
                            <div className={styles.formattingToolbar}>
                                <div className={styles.emojiContainer}>
                                <button
                                    type="button"
                                    className={clsx( styles.formattingButton, showEmojiPicker && styles.emojiButton )}
                                    onClick={handleEmojiButtonClick}
                                    aria-label="Insert emoji"
                                >
                                    <EmojiIcon className={styles.Icon} />  <EmojiIconHover className={styles.IconHover} />
                                </button>
                                    {showEmojiPicker && (
                                        <div className={styles.emojiPicker}>
                                            {commonEmojis.map((emoji, index) => (
                                                <button
                                                    key={index}
                                                    type="button"
                                                    className={styles.emojiOption}
                                                    onClick={() => insertEmoji(emoji)}
                                                >
                                                    {emoji}
                                                </button>
                                            ))}
                                        </div>
                                    )}
                                </div>
                                <button
                                    type="button"
                                    className={clsx(styles.formattingButton, activeButtons.bold && styles.buttonClicked)}
                                    onClick={() => handleFormat('bold')}
                                    aria-label="Bold text"
                                >
                                    <BoldIcon className={styles.Icon} />  <BoldIconHover className={styles.IconHover} />
                                </button>
                                <button
                                    type="button"
                                    className={clsx(styles.formattingButton, activeButtons.italic && styles.buttonClicked)}
                                    onClick={() => handleFormat('italic')}
                                    aria-label="Italic text"
                                >
                                    <ItalicIcon className={styles.Icon} />  <ItalicIconHover className={styles.IconHover} />
                                </button>
                                <button
                                    type="button"
                                    className={clsx(styles.formattingButton, activeButtons.underline && styles.buttonClicked)}
                                    onClick={() => handleFormat('underline')}
                                    aria-label="Underline text"
                                >
                                    <UnderlineIcon className={styles.Icon} />  <UnderlineIconHover className={styles.IconHover} />
                                </button>
                            </div>
                            
                            <div className={styles.buttonContainer}>
                                {(emailTags.length === 0 || (shareEmailType === shareEmailTypes.sharePrice && selectedProductsData.length === 0) || (shareEmailType === shareEmailTypes.shareQuote && !selectedQuote?.id)) ?
                                <button className={styles.sendButtonDisabled}>send</button>
                                :
                                <button 
                                    type="submit" 
                                    className={styles.sendButton}
                                    disabled={isSendInProgress}
                                    onClick={handleSubmit(onSubmit)}
                                >SEND
                                </button>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    )
}

export default ShareEmailWindow
