// Configuration settings for the application

// API configuration
export const API_CONFIG = {
  // Base URL for API calls - configurable for different environments
  baseUrl: 'https://textextract-service.onrender.com',//process.env.REACT_APP_API_BASE_URL || 'https://textextract-service.onrender.com',
  
  // API endpoints
  endpoints: {
    uploadPdf: '/upload-pdf',
    processPdf: '/process-pdf',
  },
  
  // Request timeouts (in milliseconds)
  timeouts: {
    upload: 30000,     // 30 seconds for upload
    processing: 60000, // 60 seconds for processing
  }
};

// Feature flags
export const FEATURES = {
  useTextract: true, // Whether to use AWS Textract for text extraction
};
