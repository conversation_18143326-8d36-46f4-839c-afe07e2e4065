import React, { useCallback, useEffect, useRef, useState } from 'react';
import styles from './TabContent.module.scss';
import InputWrapper from 'src/renderer/component/InputWrapper';
import CustomTextField from 'src/renderer/component/CustomTextField';
import clsx from 'clsx';
import Cass from 'src/renderer/pages/buyer/Cass';
import { commomKeys, useGlobalStore, useSellerSettingStore } from '@bryzos/giss-ui-library';
import axios from 'axios';
import TrueVaultClient from 'truevault';
import useSaveUserSettings from 'src/renderer/hooks/useSaveUserSettings';
import { ReactComponent as TrueVaultLogo } from '../../../../assets/images/payment-method-vault-logo.svg';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { paymentSchema } from '../schemas/paymentSchema';
import { sellerSettingTabConstants, userRole } from 'src/renderer/common';
import useDialogStore from 'src/renderer/component/DialogPopup/DialogStore';
import { EmailTagInputField } from 'src/renderer/component/EmailTagInput';
import { useNavigate } from 'react-router-dom';

interface InputFocusState {
  bankName1: boolean;
  routingNo: boolean;
  accountNo: boolean;
  remittanceEmail: boolean;
}

const PaymentsTab: React.FC = ({setSaveFunctions , setActiveTab, locationState}: any) => {
  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    clearErrors,
    setError,
    setValue,
    reset,
    watch,
    control,
    getValues,
    trigger,
    resetField,
    formState: { errors, dirtyFields, isDirty, isValid, isSubmitting },
    getFieldState,
  } = useForm({
    resolver: yupResolver(paymentSchema),
    mode: 'onSubmit',
  });

  
  const [isInputFocused, setIsInputFocused] = useState<InputFocusState>({
    bankName1: false,
    routingNo: false,
    accountNo: false,
    remittanceEmail: false
  });
  const referenceData: any = useGlobalStore(state => state.referenceData);
  const userData: any = useGlobalStore(state => state.userData);
  const [states, setStates] = useState([]);
  const { mutateAsync: saveUserSettings } = useSaveUserSettings();
  const {sellerSettings , setShowFadeLoader, showFadeLoader} = useSellerSettingStore();
  const [achId, setAchId] = useState<number>(0);
  const [wireId, setWireId] = useState<number>(0);
  const [referenceDocumentId, setReferenceDocumentId] = useState('');
  const isButtonDisabled = !isDirty || isSubmitting;
  const {showCommonDialog, resetDialogStore} = useDialogStore();

  useEffect(() => {
    if(sellerSettings && referenceData) {
      //payments tab
      let _achId = 0;
      // let _wireId = 0;
      referenceData?.ref_pgpm_mapping.forEach((paymentMethod: any) => {
        if (paymentMethod.user_type === userRole.sellerUser.toLowerCase() && paymentMethod.payment_method === 'ACH') {
          _achId = paymentMethod.id;
          return setAchId(paymentMethod.id)
        }
        // if (paymentMethod.user_type === userRole.sellerUser.toLowerCase() && paymentMethod.payment_method === 'WIRE') {
        //   _wireId = paymentMethod.id;
        //   return setWireId(paymentMethod.id)
        // }
      })
      if (sellerSettings?.funding_settings) {
        if (sellerSettings?.funding_settings?.pgpm_mapping_id === _achId) {
          setValue('achCheckBox', true)
          setValue('bankName1', sellerSettings.funding_settings.bank_name);
          setValue('routingNo', sellerSettings.funding_settings.routing_number);
          setValue('accountNo', sellerSettings.funding_settings.account_number);
          setReferenceDocumentId(sellerSettings.funding_settings.reference_document_id)
        }
      }
      setValue('remittanceEmail', sellerSettings?.remittance_email);
    }
}, [sellerSettings, referenceData]);

  useEffect(() => {
    if(!sellerSettings?.company_address?.line1 || !sellerSettings?.phone){
      let message = 'Please update your company address and phone number';
      let redirectTo = sellerSettingTabConstants.company
      if(sellerSettings?.company_address?.line1){
        message = 'Please update your phone number';
        redirectTo = sellerSettingTabConstants.user;
      }
      showCommonDialog(null, message, null, handleUpdateAccountNavigation, [{ name: commomKeys.errorBtnTitle, action: () => handleUpdateAccountNavigation(redirectTo) }]);
    }
  }, [sellerSettings]);


useEffect(() => {
  setSaveFunctions({
      onSave: () => handleSubmit(handleSavePaymentSettings)(),
      isDisabled: isButtonDisabled,
  });
}, [isButtonDisabled, handleSubmit]);

useEffect(() => {   
  setTimeout(() => {
      const defaultFocusSpan = document.getElementById('defaultFocusSpan');
      if (defaultFocusSpan) {
          defaultFocusSpan.focus();
      }
  }, 100)
}, []);

  useEffect(() => {
    if (referenceData) {
      setStates(referenceData.ref_states);
    }
  }, [referenceData]);

  const paymentCreditReq = useRef(null);
  const childRef = useRef();

  const handleUpdateAccountNavigation = (redirectTo: string) => {
    setActiveTab(redirectTo)
    resetDialogStore();
  }

  const fundingSettingChanged = (field: any) => {
    const bankName1 = getValues("bankName1");
    const routingNo = getValues("routingNo");
    const accountNo = getValues("accountNo");
    setValue('achCheckBox', true)
    if (field === "bankName1") {
      if (routingNo?.includes("x")) {
        setValue('routingNo', "");
      }
      if (accountNo?.includes("x")) {
        setValue('accountNo', "");
      }

    } else if (field === "routingNo") {
      if (bankName1?.includes("x")) {
        setValue('bankName1', "");
      }
      if (accountNo?.includes("x")) {
        setValue('accountNo', "");
      }
      if(routingNo.includes("x")) {
        setValue('routingNo', "");
      }
    } else if (field === "accountNo") {
      if (bankName1?.includes("x")) {
        setValue('bankName1', "");
      }
      if (routingNo?.includes("x")) {
        setValue('routingNo', "");
      }
      if(accountNo.includes("x")) {
        setValue('accountNo', "");
      }
    }
  }

  const getTruevaultData = async (companyName, userData, bankName, routingNo, accountNo, paymentId) => {
    try {
      const res = await axios.get(import.meta.env.VITE_API_SERVICE + '/user/getAccessToken');
      const accessToken = res.data.data;
      const sellerPaymentData = {
        "document": {
          "company_name": companyName,
          "user_id": userData,
          "bank_name": bankName,
          "routing_number": routingNo,
          "account_number": accountNo,
          "pgpm_mapping_id": paymentId
        }
      }

      const client = new TrueVaultClient({ accessToken });

      try {
        const response = await client.createDocument(import.meta.env.VITE_TRUE_VAULT_ID_SELLER_VAULT_ID, null, sellerPaymentData)
        const documentIdFromTruevault = response.id;
        return documentIdFromTruevault;

      } catch (error) {
        console.error(error);
      }
    } catch (error) {
      console.error(error);
    }
  }

  const isAchChanged  = () => {
    let bankName = watch('bankName1');
    let routingNumber = watch('routingNo');
    let accountNumber = watch('accountNo');

    let savedAccountDetails = sellerSettings?.funding_settings;
    if(savedAccountDetails){
      if(bankName !== savedAccountDetails?.bank_name ||
         routingNumber !== savedAccountDetails?.routing_number
          || accountNumber !== savedAccountDetails?.account_number){
        return true;
      }
    }else{
      return true;
    }
  }

  const handleSavePayment = async () => {
    try {
    if (watch('bankName1') && watch('routingNo') && watch('accountNo') && watch('remittanceEmail') && await trigger('remittanceEmail') && await trigger('bankName1') && await trigger('routingNo') && await trigger('accountNo')) {
      setShowFadeLoader(true);
      const fieldsToReset = ['bankName1', 'routingNo', 'accountNo'];
      fieldsToReset.forEach((fieldName) => {
        const currentValue = watch(fieldName);
        resetField(fieldName, { 
          defaultValue: currentValue,
          keepError: false,
          keepDirty: false,
          keepTouched: true
        });
      });
      if (referenceData?.ref_general_settings.length) {
        let obj = referenceData.ref_general_settings.find((obj) => obj.name === "CASS_MASTER_DATA_CREATE");
        if (obj?.value === "true") {
          try {
            const result = await childRef.current.startCassCreateion();
          } catch (error) {
            console.error("error in cass creation", error)
            setShowFadeLoader(false)
          }
        }
      }

      if (isNaN(watch("routingNo")) || isNaN(watch("accountNo"))) {
        const key = isNaN(watch("routingNo")) ? 'routingNo' : 'accountNo'
        setError(key, { message: 'ACH Credit is not valid' }, { shouldFocus: true });
        return
      }
      setShowFadeLoader(true);
      getTruevaultData(sellerSettings?.company_name, userData.data.id, watch("bankName1"), watch("routingNo"), watch("accountNo"), achId).then(async (documentIdFromTruevault) => {
        const achFundingSetting = {};
        const convertedRoutingNo = watch("routingNo").slice(-4).padStart(watch("routingNo").length, 'x');
        const convertedAccountNO = watch("accountNo").slice(-4).padStart(watch("accountNo").length, 'x');
        achFundingSetting.bank_name = watch("bankName1");
        achFundingSetting.routing_number = convertedRoutingNo;
        achFundingSetting.account_number = convertedAccountNO;
        achFundingSetting.reference_document_id = documentIdFromTruevault;
        achFundingSetting.pgpm_mapping_id = achId;    
        const payload = { 
          funding_settings: achFundingSetting,
          remittance_email: watch('remittanceEmail')
        }        
        await submitData(payload);
        setShowFadeLoader(false);
      })
    }
    } catch (error) {
      setShowFadeLoader(false)
    }
  }

  const submitData = async (data: any) => {
    try {
      await saveUserSettings({ route: 'user/seller/settings/funding', data: data })
      reset(watch())
      if (locationState?.previousPath) {
        navigate(locationState.previousPath);
      }
    } catch (error) {
    } finally {
      setShowFadeLoader(false)
    }
  }

  const handleSavePaymentSettings = async () => {
    setShowFadeLoader(true);
    if(isAchChanged()){
      await handleSavePayment()
    }else{
      const isEmailValid = await trigger('remittanceEmail');
      if(isEmailValid){
        const payload = {
          funding_settings: {
            bank_name: sellerSettings?.funding_settings?.bank_name,
            routing_number: sellerSettings?.funding_settings?.routing_number,
            account_number: sellerSettings?.funding_settings?.account_number,
            pgpm_mapping_id: sellerSettings?.funding_settings?.pgpm_mapping_id,
            reference_document_id: sellerSettings?.funding_settings?.reference_document_id
          },
          remittance_email: watch('remittanceEmail')
        }
        await submitData(payload);
      }
    }
  }


  // Add onChange validation for ACH fields
  const handleAchFieldChange = async (fieldName: string, e: any) => {
    fundingSettingChanged(fieldName);
    clearErrors(fieldName);
  }


  const handleInputFocus = (inputName: keyof InputFocusState): void => {
    setIsInputFocused((prevState) => ({
      ...prevState,
      [inputName]: true,
    }));
  };

  const handleInputBlur = (inputName: keyof InputFocusState): void => {
    setIsInputFocused((prevState) => ({
      ...prevState,
      [inputName]: false,
    }));
  };


  return (
    <div className={styles.paymentTabContent} ref={paymentCreditReq} data-hover-video-id="settings-payment">
      <div className={styles.scrollerContainer}>

        <div className={styles.formContainer}>
          <div className={styles.paymentHeaderTextContainer}>
            <span className={styles.paymentHeaderText}> Enter your company’s bank information to be funded by {import.meta.env.VITE_CLIENT_NAME} via ACH Credit. All invoices from your company must be emailed to 
              <span className={styles.textBold}> <EMAIL></span> to be recorded for payment. All information will be encrypted immediately upon entry.</span>
              <span>
               <TrueVaultLogo />
              </span>
          </div>
          <span tabIndex={0} id="defaultFocusSpan" onKeyDown={(e) => {
            if (e.key === 'Tab') {
              if(!e.shiftKey){
                e.preventDefault();
                const bankName1 = document.getElementById('bankName1');
                if(bankName1){
                  bankName1.focus();
                }
              }
            }
          }}></span>
          <div className={styles.formGroupInputContainer}>
            <div className={styles.formGroupInput}>
              <span className={styles.col1}>
                <label className={styles.paymentHeader} htmlFor="bankName1">
                  ACH CREDIT
                </label>
              </span>
              <span className={styles.col1}>
              </span>
            </div>
            <>
              <div className={styles.formGroupInput}>
                <span className={styles.col1}>
                  <label className={clsx(isInputFocused.bankName1 && styles.focusLbl)} htmlFor="bankName1">
                    BANK NAME
                  </label>
                </span>
                <span className={clsx(styles.col1, styles.inputMain)}>
                  <InputWrapper>
                    <CustomTextField
                      className={clsx(styles.inputCreateAccount, errors?.bankName1 && styles.error)}
                      errorInput={errors?.bankName1}
                      type='text'
                      register={register("bankName1")}
                      placeholder=''
                      onChange={(e: any) => {
                        handleAchFieldChange("bankName1", e)
                      }}
                      id='bankName1'
                    />
                  </InputWrapper>
                </span>
              </div>
              <div className={styles.formGroupInput}>
                <span className={styles.col1}>
                  <label className={clsx(isInputFocused.routingNo && styles.focusLbl)} htmlFor="routingNo">
                    BANK ROUTING NUMBER
                  </label>
                </span>
                <span className={clsx(styles.col1, styles.inputMain)}>
                  <InputWrapper>
                    <CustomTextField
                      className={clsx(styles.inputCreateAccount, errors?.routingNo && styles.error)}
                      errorInput={errors?.routingNo}
                      type='text'
                      register={register("routingNo")}
                      placeholder=''
                      maxLength={9}
                      mode="maskedWholeNumber"
                      onChange={(e) => {
                        handleAchFieldChange("routingNo", e)
                      }}
                    />
                  </InputWrapper>
                </span>
              </div>
              <div className={styles.formGroupInput}>
                <span className={styles.col1}>
                  <label className={clsx(isInputFocused.accountNo && styles.focusLbl)} htmlFor="accountNo">
                    BANK ACCOUNT NUMBER
                  </label>
                </span>
                <span className={clsx(styles.col1, styles.inputMain)}>
                  <InputWrapper>
                    <CustomTextField
                      errorInput={errors?.accountNo}
                      className={clsx(styles.inputCreateAccount, errors?.accountNo && styles.error)}
                      type='text'
                      register={register("accountNo")}
                      mode="maskedWholeNumber"
                      placeholder=''
                      onChange={(e) => {
                        handleAchFieldChange("accountNo", e)
                      }}
                    />
                  </InputWrapper>
                </span>
              </div>
              <div className={clsx(styles.formGroupInput, styles.bdrBtm0)}>
                <span className={styles.col1}>
                  <label className={clsx(isInputFocused.remittanceEmail && styles.focusLbl)} htmlFor="remittanceEmail">
                    REMITTANCE EMAIL
                  </label>
                </span>
                <span className={clsx(styles.col1, styles.inputMain)}>
                <EmailTagInputField
                            value={watch('remittanceEmail') ? watch('remittanceEmail').split(',').filter(Boolean) : []}
                            onChange={(emails) => {
                                setValue('remittanceEmail', emails.join(','), { 
                                    shouldDirty: true, 
                                    shouldTouch: true 
                                });
                                clearErrors('remittanceEmail')
                            }}
                            placeholder=""
                            maxEmails={5}
                            register={register("remittanceEmail")}
                            error={errors?.remittanceEmail?.message}
                            onBlur={() => {
                                handleInputBlur('remittanceEmail')
                            }}
                            inputBlur={() => {
                                handleInputBlur('remittanceEmail')
                            }}
                            onFocus={() => {
                                handleInputFocus('remittanceEmail')
                            }}
                                                         onKeyDown={(e) => {
                                 if(e.key === 'Tab'){
                                     e.preventDefault();
                                     const saveButton = document.getElementById('settings-save-button') as HTMLButtonElement;
                                     if (saveButton) {
                                         saveButton.focus();
                                     }
                                 }
                             }}
                        />

                </span>
              </div>
            </>


          </div>
        </div>
        <Cass ref={childRef} getValues={getValues} referenceData={referenceData} states={states} containerRef={paymentCreditReq.current} />

      </div>
    </div>
  );
};

export default PaymentsTab; 