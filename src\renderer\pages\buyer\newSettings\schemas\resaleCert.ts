import * as yup from 'yup';

const isEmail = (email: string) => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
}

export const resaleCertSchema = yup.object().shape({
    resaleCertFile: yup.string().required('Resale certificate file is required'),
    resaleCertFileUrl: yup.string().required('Resale certificate file is required'),
    expiration_date: yup.string().required('Expiration date is required'),
    state_list: yup.array().of(yup.string()).default([]).required('State is required')
});

export type resaleFormData = yup.InferType<typeof resaleCertSchema>;
