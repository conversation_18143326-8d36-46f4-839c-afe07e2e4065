.createAccountFormContainer {
    width: 100%;
    max-width: 941px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 30px auto;

    .joinBryzosText {
        font-family: Syncopate;
        font-size: 28px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: -1.12px;
        text-align: center;
        color: #fff;
    }
}

.createAccountMain {
    background-color: #0f0f14;
    height: 91.41vh;
    overflow-y: scroll;
    overflow-x: hidden;
    &::-webkit-scrollbar {
        width: 4px;
        height: 6px;
    }

    .createAccountForm {
        padding: 6px 24px;
        position: relative;
        width: 100%;
        margin: 40px 0px 40px 0px;
        border-radius: 16px;
        background-color: #191a20;
    }

    .formGroupInput {
        display: flex;
        height: 64px;
        display: flex;
        justify-content: flex-start;
        border-bottom: solid 1px rgba(255, 255, 255, 0.07);

        &.bdrBtm0 {
            border-bottom: 0px;
        }

        label {
            font-family: Syncopate;
            font-size: 18.2px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: -0.73px;
            text-align: left;
            color: rgba(255, 255, 255, 0.4);
            text-transform: uppercase;
        }

        .focusLbl {
            color: #fff;
        }

        .radioGroupContainer {
            width: 100%;
            display: flex;
        }

        label.radioButtonLeft,
        label.radioButtonRight {
            flex: 1;
            font-family: Syncopate;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: 0.56px;
            text-align: center;
            color: #616575;

            &.labelUserTypeError {
                border: 1px solid red;
            }
        }

        .col1 {
            flex: 1;
            display: flex;
            align-items: center;
            position: relative;
            &:nth-child(2){
                flex: 0 365px;
            }
        }

        .bgAutoComplete {
            width: 100%;
            border-radius: 10px;
            background: url(../../assets/images/Create-Account/company-name-dropdown1.svg) no-repeat;
            background-position: bottom right;
            background-size: contain;
            height: 180px;
            display: flex;
            align-items: baseline;
            position: relative;
            // top: 9px;
            z-index: 999;
            padding-right: 5px;
            padding-top: 5px;

            .companyNameInput.companyNameInput {
                background: transparent;
                height: 40px;
                top: 8px;

                input {
                    background: transparent;
                    font-size: 14px;
                }
            }
        }

        .companyNameInput {
            width: 100%;
            position: relative;
            height: 40px;
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            padding: 6px 16px;
            border-radius: 12px;
            background-color: rgba(255, 255, 255, 0.04);
            border: 0px;
             overflow: hidden;
            z-index: 0;
             transition: all 200ms ease;
            &.companyError {
                background: url(../../assets/images/Create-Account/error-input.svg) no-repeat;
                background-size: cover;
                box-shadow: none;
            }

             

            input {
                width: fit-content;
                background-color: transparent;
                border: 0px;
                font-family: Inter;
                font-size: 14px;
                font-weight: normal;
                line-height: normal;
                letter-spacing: 0.56px;
                text-align: left;
                color: #fff;
                max-width: 35.5ch;

                &:focus {
                    outline: none;
                    color: #fff;
                }
            }

                &:focus-within {
                    outline: none;
                }
        }
        .companyNameInputFocus{
            &:focus-within {
                box-shadow: inset 4px 5px 2.2px 0 #000;

                &::before {
                    content: '';
                    position: absolute;
                    inset: 0;
                    border-radius: inherit;
                    padding: 1.5px;
                    background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.04), rgba(255, 255, 255, 0.04)), linear-gradient(350deg, rgba(255, 255, 255, 0.2) 147%, #101015 45%);
                    -webkit-mask:
                        linear-gradient(#fff 0 0) content-box,
                        linear-gradient(#fff 0 0);
                    -webkit-mask-composite: xor;
                    mask-composite: exclude;
                    background-clip: border-box;
                    z-index: -1;
                    pointer-events: none;
                    left: -2px;
                    top: -3px;
                }
            }
        }


        .inputWrapper {
            display: flex;
            align-items: center;
            width: 100%;

        }


        .autoSuggestionText {
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: 0.6px;
            text-align: left;
            color: rgba(255, 255, 255, 0.33);
            position: relative;
            top: 1px
        }

        .inputMain {
            position: relative;
            border-radius: 12px;
            overflow: hidden;
            z-index: 0;
            width: 100%;
            transition: all 200ms ease;

            &:focus-within {
                box-shadow: inset 4px 5px 2.2px 0 #000;

                &::before {
                    content: '';
                    position: absolute;
                    inset: 0;
                    border-radius: inherit;
                    padding: 1.5px;
                    background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.04), rgba(255, 255, 255, 0.04)), linear-gradient(350deg, rgba(255, 255, 255, 0.2) 147%, #101015 45%);
                    -webkit-mask:
                        linear-gradient(#fff 0 0) content-box,
                        linear-gradient(#fff 0 0);
                    -webkit-mask-composite: xor;
                    mask-composite: exclude;
                    background-clip: border-box;
                    z-index: -1;
                    pointer-events: none;
                    left: -2px;
                    top: -3px;
                }
            }
        }

        .inputCreateAccount {
            width: 100%;
            height: 40px;
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: flex-start;
            padding: 6px 16px;
            border-radius: 12px;
            background-color: rgba(255, 255, 255, 0.04);
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            line-height: normal;
            letter-spacing: 0.56px;
            text-align: left;
            color: #fff;
            position: relative;

            &.error {
                background: url(../../assets/images/Create-Account/error-input.svg) no-repeat;
                background-size: cover;
                box-shadow: none;
            }
        }

        .confirmPasswordInput {
            width: 100%;
            position: relative;
            z-index: 12;
               transition: all 200ms ease-in-out;

            &.focusPass {
                background: url(../../assets/images/Create-Account/password-active.svg) transparent no-repeat;
                background-size: contain;
                position: absolute;
                height: 100px;
                top: 12px;
                z-index: 9;

                .passwordInput {
                    border-radius: 12px 12px 0px 0px;
                    background: transparent;
                    position: relative;
                }
            }


            &.focusPass2 {
                top: -52px;
                display: flex;
                align-items: flex-end;

                .passwordInput2 {
                    border-radius: 0px 0px 12px 12px;
                    position: relative;
                    top: -2px;
                }
            }

            &.bgRemove {
                .passwordInput {
                    background: transparent !important;
                }
            }

        }

        .inputOnboarding1 {
            width: 100%;
        }

        .passwordInput {
            border-radius: 12px;
            background-color: rgba(255, 255, 255, 0.04);
            width: 100%;
            height: 40px;
            padding: 0px 16px;
               transition: all 200ms ease-in-out;

            &:focus-within {
                background: rgba(255, 255, 255, 0.04);
            }


            input {
                background-color: transparent;
                padding: 0px;
            }
        }

        .passwordRequirements {
            position: absolute;
            top: -34px;
            left: 1px;
            width: 99%;
            height: 18px;
            display: flex;
            padding: 0px 20px;
            flex-direction: row;
            z-index: 999;
            justify-content: space-between;
            align-items: center;
            border-image-source: linear-gradient(to top, #fff 120%, rgba(26, 27, 32, 0) 85%);
            border-image-slice: 1;
            background-color: #1b1c21;
            font-family: Inter;
            font-size: 11px;
            font-weight: normal;
            line-height: 1.23;
            letter-spacing: normal;
            text-align: left;
            color: rgba(255, 255, 255, 0.5);
            z-index: 1;
            box-shadow: inset 4px 4px 10.1px 0 #000;
            overflow: hidden;

            &.passwordRequirements2 {
                top: 30px
            }
        }


    }
}

.btnFooterTnc {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .btnBack {
        button {
            font-family: Inter;
            font-size: 15.6px;
            font-weight: normal;
            line-height: 1.3;
            letter-spacing: normal;
            text-align: left;
            color: rgba(255, 255, 255, 0.4);
            display: flex;

            svg {
                margin-right: 10px;
            }

            span {
                padding-top: 4px;
            }
        }
    }

    .alreadyAccountLogin {
        font-family: Inter;
        font-size: 18px;
        font-weight: normal;
        line-height: 1.3;
        letter-spacing: normal;
        text-align: left;
        color: rgba(255, 255, 255, 0.4);

        span {
            color: #fff;
            cursor: pointer;
        }
    }

    .nextTncBtn {
        width: 194px;
        height: 52px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        border-radius: 12px;
        box-shadow: 0 -5.3px 5.3px 0 rgba(0, 0, 0, 0.8);
        background-image: linear-gradient(140deg, #1c40e7 -48%, #16b9ff 132%);
        font-family: Syncopate;
        font-size: 18px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: -0.72px;
        text-align: left;
        color: #fff;
        text-transform: uppercase;

        &[disabled] {
            opacity: unset;
            border-radius: 12px;
            background-color: rgba(255, 255, 255, 0.08);
            box-shadow: none;
            background-image: unset;
            color: rgba(255, 255, 255, 0.4);

        }
    }

}

.autocompleteDescPanel {
    border-radius: 4px;
    padding: 8px 0px;
}

.autocompleteDescInnerPanel.autocompleteDescInnerPanel {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    background: transparent;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
    border-radius: 0px 0px 4px 4px;
    padding: 0px 0px 0px 16px;
    box-shadow: unset;

    .listAutoComletePanel.listAutoComletePanel {
        width: 100%;
        padding-right: 8px;
        margin: 2px 0px;
        padding: 0px 0px 0px 0px;
        max-height: 115px;

        &::-webkit-scrollbar {
            width: 4px;
            height: 6px;
        }

        span {
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.23;
            letter-spacing: normal;
            text-align: left;
            color: #fff;
            background-image: unset !important;
            padding-left: 0px;

            &[aria-selected="true"] {
                background-color: transparent !important;
                box-shadow: unset;
                color: #fff;
            }
        }
    }
}


.radioGroupContainer {
    display: flex;
    text-align: center;
    cursor: pointer;
    justify-content: center;
    position: relative;

    .chosseOneIcon {
        position: absolute;
        top: -19px;
    }

    .radioButton {
        height: 41px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        background-color: rgba(255, 255, 255, 0.04);

        &:not(.disableBidBuyNowBtn):hover {
            color: #fff;
        }

        &.selected {
            font-family: Syncopate;
            font-size: 14px;
            font-weight: bold;
            line-height: 1;
            letter-spacing: 0.56px;
            text-align: left;
            color: #459fff;
        }

        &.buyerSelected {
            // background: url(../../assets/images/Create-Account/buyer-active.svg);
            // background-repeat: no-repeat;
            // background-position: bottom left;
            color: #fff !important;
            font-weight: bold !important;
            box-shadow: inset -2px -1px 4.5px 0 rgba(0, 0, 0, 0.5), inset 3px 5px 4.8px 0 rgba(0, 0, 0, 0.42);
            background-color: rgba(255, 255, 255, 0.04);
        }

        &.sellerSelected {
            // background: url(../../assets/images/Create-Account/seller-active.svg);
            // background-repeat: no-repeat;
            // background-position: bottom right;
            color: #fff !important;
            font-weight: bold !important;
            box-shadow: inset -2px -1px 4.5px 0 rgba(0, 0, 0, 0.5), inset 3px 5px 4.8px 0 rgba(0, 0, 0, 0.42);
            background-color: rgba(255, 255, 255, 0.04);
        }


    }

    .disableBidBuyNowBtn {
        cursor: not-allowed;
    }

    .radioButtonLeft {
        border-right: solid 2.5px rgba(1, 1, 1, 0.95);
        border-top-left-radius: 13px;
        border-bottom-left-radius: 13px;

        &:focus-visible {
            outline: none;

            &.radioButton {
                color: #459fff;
            }
        }
    }


    .radioButtonRight {
        border-left: solid 1px #1c1d23;
        border-top-right-radius: 13px;
        border-bottom-right-radius: 13px;

        &:focus-visible {
            outline: none;

            &.radioButton {
                color: #459fff;
            }
        }
    }

    .hiddenRadio {
        display: none;
    }
}

.passwordRequirements {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;

    .passwordRequirementItem {
        font-family: Inter;
        font-size: 12px;
        font-weight: 300;
        line-height: 1.6;
        text-align: left;
    }

    .passwordRequirementItemActive {
        color: #32ff6c;
    }
}

.hiddenMeasure {
    visibility: hidden;
    position: absolute;
    white-space: pre;
    font-family: Inter;
    font-size: 18px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: left;
    color: #fff;
    height: 0;
    padding: 0;
    margin: 0;
}

.dialogContainer.dialogContainer {
    max-width: 504px;
    padding: 48px 52px;
    border-radius: 50px;
    background-color: #0f0f14;


    .dialogHeader {
        font-family: Syncopate;
        font-size: 24px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: -0.96px;
        text-align: center;
        color: #fff;
        position: relative;
        svg {
            position: absolute;
            right: -20px;
            top: -20px;
            cursor: pointer;
        }

        .dialogHeaderFreeTrial {
            font-weight: normal;
            line-height: 1.3;
            letter-spacing: 2.4px;
            color: #32ff6c;

        }
    }

    .dialogContent {
        font-family: Inter;
        font-size: 16px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: normal;
        text-align: left;
        color: #c3c4ca;
        display: flex;
        flex-direction: column;
        gap: 18px;
        width: 100%;
        padding-top: 24px;

        p {
            margin: 0;
        }

        .bold {
            font-weight: bold;
        }
    }

    .dialogFooter {
        border-image-slice: 1;
        padding: 20px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        button {
            width: 352px;
            height: 69px;
            font-family: Syncopate;
            font-size: 18px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: -0.72px;
            text-align: center;
            color: #fff;
            border-radius: 50px;
            background-color: #2b2c32;
        }
    }
}

.buttonNone {
    visibility: hidden
}