import React, { useEffect, useMemo, useState } from 'react'
import styles from './RouteTab.module.scss';
import { useLocation, useNavigate } from 'react-router-dom';
import { purchaseOrder, routes, sellerView, sellerViewConstants } from 'src/renderer/common';
import { useAuthStore, useCreatePoStore, useGlobalStore, useOrderManagementStore, useSellerOrderStore } from '@bryzos/giss-ui-library';
import clsx from 'clsx';
import { ReactComponent as InstantPriceIcon } from '../../../assets/images/Instant Price Search.svg';
import { ReactComponent as InstantPriceIconHover } from '../../../assets/images/Instant Price Search Active.svg';
import { ReactComponent as DeleteIcon } from '../../../assets/images/routes-delete-icon.svg';
import { useBomPdfExtractorStore } from 'src/renderer/pages/buyer/BomPdfExtractor/BomPdfExtractorStore';
import { handleOrderManagementNavigation } from 'src/renderer/helper';
import { useLeftPanelStore } from '../LeftPanelStore';
import { useRightWindowStore } from 'src/renderer/pages/RightWindow/RightWindowStore';
import { useGlobalSearchStore } from 'src/renderer/pages/GlobalSearchField/globalSearchStore';

const RouteTab = () => {
  const navigate = useNavigate();
  const {initiateLogout } = useAuthStore();
  const {userData} = useGlobalStore();
  const purchaseOrdersList = useSellerOrderStore((state: any) => state.ordersCart);
  const {viewedOrdersListForBadgeCount} = useGlobalStore();
  const setBomData = useBomPdfExtractorStore(state => state.setBomData);
  const setSelectedQuote = useCreatePoStore(state => state.setSelectedQuote);
  const setCreatePoData = useCreatePoStore(state => state.setCreatePoData);
  const setOrderManageMentInitialData = useOrderManagementStore(state => state.setOrderManageMentInitialData);
  const setClickedCreateNewButton = useLeftPanelStore(state => state.setClickedCreateNewButton);
  const setShareEmailWindowProps = useRightWindowStore((state: any) => state.setShareEmailWindowProps);
  const setShareEmailType = useRightWindowStore((state: any) => state.setShareEmailType);
  const setIsDisableLeftPanel = useLeftPanelStore(state => state.setIsDisableLeftPanel);
  const location = useLocation();
  const isAdmin = !!userData?.data?.is_super_admin;
  const orderManagementData = useOrderManagementStore(state => state.orderManagementData);
  const [disputeOrderCount, setDisputeOrderCount] = useState(0);

  const handleLogout = () => {
    // setCloseWithoutAnimation(true);
    // setOpenLeftPanel(false);
    initiateLogout(false, false, true);
}

  const previewOrders = purchaseOrdersList?.filter(order =>
    order.claimed_by === purchaseOrder.pending
  );

  const previewUnviewedCount = previewOrders?.reduce((count, order) => {
    return viewedOrdersListForBadgeCount?.includes(order.buyer_po_number) ? count : count + 1;
  }, 0);

  const claimOrders = purchaseOrdersList?.filter(order =>
    order.claimed_by === purchaseOrder.readyToClaim
  );
  
  const claimUnviewedCount = claimOrders?.reduce((count, order) => {
    return viewedOrdersListForBadgeCount?.includes(order.buyer_po_number) ? count : count + 1;
  }, 0);

  const handleRouteTabClick = (route: string) => {
    if(location.pathname === route) return;
    handleOrderManagementNavigation(route, location.pathname, handleRouting);
  }

  const handleRouting = (route: string) => {
    if(location.pathname === routes.searchResult){
      //clear search result
      useGlobalSearchStore.getState().setSearchResult(null);
    }
    setSelectedQuote(null);
    setCreatePoData(null);
    setBomData(null);
    setOrderManageMentInitialData(null);
    setIsDisableLeftPanel(false);
    if(route !== routes.buyerDeleteOrderPage){
      setClickedCreateNewButton(Math.random());
    }
    setShareEmailWindowProps(null);
    setShareEmailType(null);
    navigate(route);
  }

  useEffect(() => {
    if (!orderManagementData || !Array.isArray(orderManagementData)) return;
  
    let count = 0;
  
    orderManagementData?.forEach((item) => {
      if (item?.in_dispute && item?.dispute_count > 0) {
        count += 1;
      }
    });
  
    setDisputeOrderCount(count);
  }, [orderManagementData]);


  return (
    <div className={styles.navTab}>
        <div className={styles.routingPanel}>
          {
            userData?.data?.type === 'BUYER'  ? (
              <>
                <button className={clsx(styles.sideBarButton, styles.pricing)} onClick={() => handleRouteTabClick(routes.homePage)} data-hover-video-id='price-search-icon'>
                  <div className={styles.mainButton}>
                    <span className={styles.positionRelative}>
                      { location.pathname === routes.homePage ? <InstantPriceIconHover /> : <InstantPriceIcon />}
                      {location.pathname !== routes.homePage &&<div className={clsx(styles.routeOptions, styles.pricingHover)}>
                        <span className={clsx(styles.optionHoverBg2, styles.pricingPosition)}></span>
                        <span className={styles.instantPriceIcon}>
                          <InstantPriceIcon className={styles.instantPriceIcon1} />
                          <InstantPriceIconHover className={styles.instantPriceIcon2} />
                        </span>
                        <span className={clsx(styles.animationEffect)}>INSTANT PRICE SEARCH</span>
                      </div>}
                    </span>
                  </div>
                </button>
                <button data-hover-video-id='quoting-icon' className={clsx(styles.sideBarButton, styles.quoting, location.pathname === routes.quotePage && styles.sideBarButtonActive)} onClick={() => handleRouteTabClick(routes.quotePage)}>
                  <div className={styles.mainButton}>
                    <span className={styles.positionRelative}>
                      Q
                      {location.pathname !== routes.quotePage && <div className={clsx(styles.routeOptions, styles.quotingHover)}>
                        <span className={clsx(styles.optionHoverBg2)}></span>
                        <span>Q</span>
                        <span className={clsx(styles.animationEffect)}>UOTING</span>
                      </div>}
                    </span>
                  </div>
                </button>
                <button data-hover-video-id='purchase-icon' className={clsx(styles.sideBarButton, styles.purchasing, (location.pathname === routes.createPoPage || location.pathname === routes.orderConfirmationPage) && styles.sideBarButtonActive)} onClick={() => handleRouteTabClick(routes.createPoPage)}>
                  <div className={styles.mainButton}>
                    <span className={styles.positionRelative}>
                      P
                      {(location.pathname !== routes.createPoPage  && location.pathname !== routes.orderConfirmationPage) && <div className={clsx(styles.routeOptions, styles.purchasingHover)}>
                        <span className={clsx(styles.optionHoverBg2, styles.purchasingPosition)}></span>
                        <span>P</span>
                        <span className={clsx(styles.animationEffect)}>URCHASING</span>
                      </div>}
                    </span>
                  </div>
                </button>
                <button data-hover-video-id='om-icon' className={clsx(styles.sideBarButton, styles.order, location.pathname === routes.orderManagementPage && styles.sideBarButtonActive)} onClick={() => handleRouteTabClick(routes.orderManagementPage)}>
                  <div className={styles.mainButton}>
                    <span className={styles.positionRelative}>
                      O 
                      {location.pathname !== routes.orderManagementPage && <div className={clsx(styles.routeOptions, styles.orderHover)}>
                        <span className={clsx(styles.optionHoverBg2, styles.orderPosition)}></span>
                        <span>O</span>
                        <span className={clsx(styles.animationEffect)}>RDER MANAGEMENT</span>
                      </div>}
                      {disputeOrderCount > 0 && <span className={clsx(styles.badge,styles.orderBadge)}>{disputeOrderCount}</span>}
                    </span> 
                  </div>
                </button>
                <button data-hover-video-id='deleted-items' className={clsx(styles.sideBarButton, styles.deletedItems)} onClick={() => {handleRouteTabClick(routes.buyerDeleteOrderPage)}}>
                  <div className={styles.mainButton}>
                    <span className={styles.deleteIcon}>
                      <DeleteIcon />
                      {/* <div className={clsx(styles.routeOptions, styles.purchasingHover)}>
                        <span className={clsx(styles.optionHoverBg2, styles.purchasingPosition)}></span>
                        <span>D</span>
                        <span className={clsx(styles.animationEffect)}>ELETE</span>
                      </div> */}
                    </span>
                  </div>
                </button>
              </>
            ) : (
              <>
                <button className={clsx(styles.sideBarButton, styles.pricing, location.pathname === routes.previewOrderPage && styles.sideBarButtonActive)} onClick={() => {handleRouteTabClick(routes.previewOrderPage);}}>
                <div className={styles.mainButton}>
                    <span className={styles.positionRelative}>
                      P
                      {location.pathname !== routes.previewOrderPage && <div className={clsx(styles.routeOptions, styles.previewHover)}>
                        <span className={clsx(styles.optionHoverBg2, styles.quotingPosition)}></span>
                        <span>P</span>
                        <span className={clsx(styles.animationEffect)}>REVIEW ORDERS</span>
                      </div>}
                     {previewUnviewedCount > 0 && <span className={styles.badge}>{previewUnviewedCount}</span>}
                    </span>
                  </div>
                </button>
                
                <button className={clsx(styles.sideBarButton, styles.quoting, location.pathname === routes.orderPage && styles.sideBarButtonActive)} onClick={() => {handleRouteTabClick(routes.orderPage);}}>
                  <div className={styles.mainButton}>
                    <span className={styles.positionRelative}>
                      C
                      {location.pathname !== routes.orderPage && <div className={clsx(styles.routeOptions, styles.claimHover)}>
                        <span className={clsx(styles.optionHoverBg2, styles.quotingPosition)}></span>
                        <span>C</span>
                        <span className={clsx(styles.animationEffect)}>LAIM ORDERS</span>
                      </div>}
                     {claimUnviewedCount > 0 && <span className={styles.badge}>{claimUnviewedCount}</span>}
                    </span>
                  </div>
                </button>
                
                <button data-hover-video-id='om-icon' className={clsx(styles.sideBarButton, styles.order, location.pathname === routes.orderManagementPage && styles.sideBarButtonActive)} onClick={() => {handleRouteTabClick(routes.orderManagementPage)}}>
                  <div className={styles.mainButton}>
                    <span className={styles.positionRelative}>
                      O
                      {location.pathname !== routes.orderManagementPage && <div className={clsx(styles.routeOptions, styles.orderHover)}>
                        <span className={clsx(styles.optionHoverBg2, styles.orderPosition)}></span>
                        <span>O</span>
                        <span className={clsx(styles.animationEffect)}>RDER MANAGEMENT</span>
                      </div>}
                      {disputeOrderCount > 0 && <span className={clsx(styles.badge,styles.orderBadge)}>{disputeOrderCount}</span>}
                    </span>
                  </div>
                </button>
                <button data-hover-video-id='deleted-items' className={clsx(styles.sideBarButton, styles.deletedItems, location.pathname === routes.deleteOrderPage && styles.sideBarButtonActive)} onClick={() => {handleRouteTabClick(routes.deleteOrderPage)}}>
                  <div className={styles.mainButton}>
                    <span className={styles.deleteIcon}>
                      <DeleteIcon />
                      {/* <div className={clsx(styles.routeOptions, styles.purchasingHover)}>
                        <span className={clsx(styles.optionHoverBg2, styles.purchasingPosition)}></span>
                        <span>D</span>
                        <span className={clsx(styles.animationEffect)}>ELETE</span>
                      </div> */}
                    </span>
                  </div>
                </button>
              </>
            )
          }
          {
            isAdmin && (
        <button className={clsx(styles.sideBarButton, styles.order, location.pathname === routes.impersonateList && styles.sideBarButtonActive)} onClick={() => { handleRouteTabClick(routes.impersonateList) }}>
          <div className={styles.mainButton}>
            <span className={styles.positionRelative}>
              I
              {location.pathname !== routes.impersonateList && <div className={clsx(styles.routeOptions, styles.orderHover)}>
                <span className={clsx(styles.optionHoverBg2, styles.orderPosition)}></span>
                <span>I</span>
                <span className={clsx(styles.animationEffect)}>MPERSONATE</span>
              </div>}
            </span>
          </div>
        </button>
            )
          }
        </div>
        <div className={styles.logout}>
            <button className={styles.logoutButton} onClick={() => handleOrderManagementNavigation(routes.loginPage, location.pathname, handleLogout)}>Logout</button>
        </div>  
    </div>
  )
}

export default RouteTab