import React from 'react';
import styles from './ProgressBar.module.scss';

interface ProgressBarProps {
  label: string;
  percent: number;
  showPercentage?: boolean;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ 
  label, 
  percent = 0, 
  showPercentage = true 
}) => {
  // Ensure percent is between 0 and 100
  const safePercent = Math.max(0, Math.min(100, percent));
  
  return (
    <div className={styles.progressItem}>
      <div className={styles.progressLabel}>{label}</div>
      <div className={styles.progressBarContainer}>
        <div className={styles.segmentMarker}>
          {[...Array(10)].map((_, index) => (
            <React.Fragment key={index}>
              <div className={styles.segmentMarkerFill}></div>
              {index < 9 && <div className={styles.gap}></div>}
            </React.Fragment>
          ))}
        </div>
        <div className={styles.progressBar}>
          {safePercent > 0 && (
            <div 
              className={styles.progressBarFill} 
              style={{ width: `${safePercent}%` }}
            />
          )}
        </div>
      </div>
      {showPercentage && (
          <div className={styles.progressPercentage}>
            {safePercent > 0 ? `${Math.floor(safePercent)}%` : ''}
          </div>
        )}
    </div>
  );
};

export default ProgressBar; 