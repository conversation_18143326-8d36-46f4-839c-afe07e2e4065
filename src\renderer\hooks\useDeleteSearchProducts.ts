import { useMutation, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { reactQueryKeys } from "../common";

const useDeleteSearchProducts = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: any) => {
      try {
        const url = `${import.meta.env.VITE_API_SERVICE}/user/remove-saved-search`;
        const response = await axios.post(url, data);
        return response;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: () => {
      // Invalidate the GET search products cache so it will refetch when needed
      queryClient.invalidateQueries({ queryKey: [reactQueryKeys.getSaveSearchProducts] });
    }
  });
};

export default useDeleteSearchProducts; 